# EzEnv Development Workflow & Rules

## Development Setup

### Prerequisites
- Node.js 20.x LTS
- pnpm 9.0+
- Git 2.40+
- VS Code (recommended) with extensions:
  - ESLint
  - Prettier
  - Tailwind CSS IntelliSense
  - TypeScript and JavaScript Language Features

### Initial Setup
```bash
# Clone repository
git clone https://github.com/your-org/ezenv.git
cd ezenv

# Install dependencies
pnpm install

# Copy environment variables
cp .env.example .env.local

# Setup Supabase local development
pnpm supabase start

# Run development server
pnpm dev
```

## Branch Strategy

### Branch Types
- `main` - Production-ready code
- `develop` - Integration branch
- `feature/*` - New features
- `fix/*` - Bug fixes
- `hotfix/*` - Production hotfixes
- `chore/*` - Maintenance tasks

### Branch Workflow
```bash
# Start new feature
git checkout develop
git pull origin develop
git checkout -b feature/add-team-settings

# Work on feature
# ... make changes ...

# Commit changes
git add .
git commit -m "feat(teams): add team settings page"

# Push and create PR
git push -u origin feature/add-team-settings
```

## Development Rules

### 1. Pre-Development Checklist
- [ ] Pull latest `develop` branch
- [ ] Check if similar work is in progress
- [ ] Review related issues/tickets
- [ ] Understand the requirements fully
- [ ] Plan the implementation approach

### 2. During Development
- **Test Locally**: Always test your changes locally
- **Check Types**: Run `pnpm typecheck` frequently
- **Lint Code**: Run `pnpm lint` before committing
- **Small Commits**: Make atomic, focused commits
- **Update Tests**: Add/update tests for changes

### 3. Before Creating PR
```bash
# Ensure all checks pass
pnpm typecheck
pnpm lint
pnpm test
pnpm build

# Update from develop
git checkout develop
git pull origin develop
git checkout feature/your-feature
git rebase develop
```

### 4. Pull Request Guidelines

#### PR Title Format
```
feat(scope): Brief description
fix(scope): Brief description
```

#### PR Description Template
```markdown
## Summary
Brief description of changes

## Changes Made
- Added X feature
- Fixed Y bug
- Refactored Z component

## Testing
- [ ] Tested locally
- [ ] Added unit tests
- [ ] Updated documentation

## Screenshots (if UI changes)
[Add screenshots here]

## Related Issues
Closes #123
```

### 5. Code Review Process

#### As Reviewer
- Check for security vulnerabilities
- Verify TypeScript types
- Ensure coding standards are followed
- Test the changes locally
- Provide constructive feedback
- Approve only when confident

#### As Author
- Respond to all comments
- Make requested changes promptly
- Re-request review after changes
- Don't merge without approval

## Testing Requirements

### Unit Testing
```typescript
// Every component should have tests
describe('ProjectCard', () => {
  it('renders project information', () => {
    render(<ProjectCard project={mockProject} />);
    expect(screen.getByText(mockProject.name)).toBeInTheDocument();
  });
  
  it('handles delete action', async () => {
    const onDelete = jest.fn();
    render(<ProjectCard project={mockProject} onDelete={onDelete} />);
    
    await userEvent.click(screen.getByRole('button', { name: /delete/i }));
    expect(onDelete).toHaveBeenCalledWith(mockProject.id);
  });
});
```

### Integration Testing
- Test API endpoints with different inputs
- Verify database operations
- Check authentication flows
- Test error scenarios

### E2E Testing
```typescript
// Critical user journeys
test('user can create and manage project', async ({ page }) => {
  // Login
  await page.goto('/login');
  await page.fill('[name="email"]', '<EMAIL>');
  await page.fill('[name="password"]', 'password');
  await page.click('button[type="submit"]');
  
  // Create project
  await page.click('text="New Project"');
  await page.fill('[name="name"]', 'Test Project');
  await page.click('text="Create"');
  
  // Verify creation
  await expect(page.locator('text="Test Project"')).toBeVisible();
});
```

## Database Migration Rules

### Creating Migrations
```bash
# Create new migration
pnpm supabase migration new add_team_settings

# Edit the migration file
# migrations/[timestamp]_add_team_settings.sql
```

### Migration Guidelines
1. Always include rollback statements
2. Test migrations locally first
3. Never modify existing migrations
4. Include data migrations if needed
5. Document breaking changes

### Example Migration
```sql
-- Up Migration
CREATE TABLE team_settings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  team_id UUID REFERENCES teams(id) ON DELETE CASCADE,
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add RLS
ALTER TABLE team_settings ENABLE ROW LEVEL SECURITY;

-- Add policy
CREATE POLICY "Team members can view settings"
ON team_settings FOR SELECT
USING (team_id IN (
  SELECT team_id FROM team_members 
  WHERE user_id = auth.uid()
));

-- Down Migration (in separate file for production)
DROP TABLE IF EXISTS team_settings;
```

## Environment Management

### Local Development
```env
# .env.local
NEXT_PUBLIC_SUPABASE_URL=http://localhost:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-local-anon-key
ENCRYPTION_KEY=local-dev-encryption-key
```

### Staging
```env
# .env.staging
NEXT_PUBLIC_SUPABASE_URL=https://staging.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=staging-anon-key
```

### Production
- Environment variables managed via hosting platform
- Never commit production secrets
- Use secure key rotation

## Performance Guidelines

### Frontend Performance
1. Use dynamic imports for large components
2. Optimize images with Next.js Image
3. Minimize client-side state
4. Implement proper loading states
5. Use React.memo for expensive components

### Backend Performance
1. Use database indexes appropriately
2. Implement query result caching
3. Batch operations when possible
4. Monitor query performance
5. Use connection pooling

## Security Checklist

### Every PR Must Consider
- [ ] No hardcoded secrets
- [ ] Input validation implemented
- [ ] SQL injection prevention
- [ ] XSS prevention
- [ ] CSRF protection
- [ ] Proper authentication checks
- [ ] Authorization verified
- [ ] Sensitive data encrypted
- [ ] Error messages don't leak info
- [ ] Rate limiting considered

## Release Process

### Version Numbering
Follow Semantic Versioning (MAJOR.MINOR.PATCH)

### Release Steps
1. Create release branch from develop
2. Update version in package.json
3. Update CHANGELOG.md
4. Run full test suite
5. Deploy to staging
6. Perform QA testing
7. Merge to main
8. Tag release
9. Deploy to production
10. Monitor for issues

### Hotfix Process
1. Branch from main
2. Fix the issue
3. Test thoroughly
4. Deploy directly to production
5. Merge back to main and develop

## Monitoring & Debugging

### Development Tools
```bash
# View Supabase logs
pnpm supabase logs

# Debug Next.js
DEBUG=* pnpm dev

# Analyze bundle
pnpm analyze
```

### Production Monitoring
- Sentry for error tracking
- Supabase dashboard for API metrics
- Vercel Analytics for performance
- Custom alerts for critical errors