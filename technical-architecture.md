SecureEnv Platform Architecture Document
Introduction / Preamble
This document outlines the overall project architecture for the SecureEnv Platform, including backend systems, data models, and non-UI specific concerns. Its primary goal is to serve as the guiding architectural blueprint for development. This document is designed to be used in conjunction with the Frontend Architecture Document, which details the frontend-specific design.

Technical Summary
The SecureEnv Platform will be built using a serverless architecture leveraging Supabase as the primary backend-as-a-service (BaaS) platform. The system consists of a Next.js frontend application, a PostgreSQL database, user authentication, and serverless edge functions, all managed within the Supabase ecosystem. The entire codebase will be managed in a single monorepo. This approach prioritizes rapid development, scalability, and security by utilizing managed services.

High-Level Overview
The architecture is centered around Supabase. The Next.js frontend (for users) and the NPM package (for programmatic access) will interact directly with Supabase's secure, auto-generated APIs for data and authentication. Custom business logic, particularly for the secure retrieval of secrets via the NPM package, will be handled by a Supabase Edge Function.

Code snippet

C4Context
title System Context Diagram for SecureEnv Platform

    Person(developer, "Developer")
    System(b2b_dev_tools, "SecureEnv Platform", "Manages project environment variables securely")
    System_Ext(supabase, "Supabase", "Backend as a Service (DB, Auth, Functions)")

    Rel(developer, b2b_dev_tools, "Uses")
    Rel(b2b_dev_tools, supabase, "Built on")

Component View
The system is composed of several key components that interact to provide the full functionality.

Code snippet

C4Component
title Component Diagram for SecureEnv Platform

    Container(spa, "Next.js Frontend", "TypeScript, React", "The web interface for managing projects, teams, and secrets.")
    Container(sdk, "NPM Package", "TypeScript", "Allows programmatic fetching of secrets in developer projects.")
    ContainerDb(db, "Supabase Database", "PostgreSQL", "Stores all data including users, projects, teams, and encrypted secrets.")
    Container(auth, "Supabase Auth", "GoTrue", "Handles user sign-up, login, and session management.")
    Container(func, "Supabase Edge Function", "Deno / TypeScript", "Provides the secure API endpoint for the NPM package.")

    Rel(spa, auth, "Authenticates via")
    Rel(spa, db, "Manages data using", "RLS Policies")
    Rel(sdk, func, "Fetches secrets from")
    Rel(func, db, "Retrieves secrets from", "RLS Policies")

API Reference
Internal APIs Provided
Secure Secrets API
This API is consumed by the NPM package to fetch environment variables.

Endpoint: /functions/v1/get-secrets

Method: POST

Authentication: Bearer token. The request must include the user's Supabase JWT in the Authorization header.

Request Body:

JSON

{
"projectName": "string",
"environmentName": "string"
}
Success Response (200 OK):

JSON

{
"secrets": {
"DATABASE_URL": "...",
"API_SECRET": "..."
}
}
Error Responses:

401 Unauthorized: If the JWT is missing, invalid, or expired.

403 Forbidden: If the user does not have 'Member' (or higher) access to the requested project.

404 Not Found: If the project or environment does not exist.

Data Models
The following SQL DDL defines the core tables for the PostgreSQL database managed by Supabase. Row Level Security (RLS) policies will be applied to these tables.

SQL

-- Teams to group users and projects
CREATE TABLE teams (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
name TEXT NOT NULL,
created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Junction table for users and teams with roles
CREATE TYPE team_role AS ENUM ('admin', 'manager', 'member');
CREATE TABLE team_members (
team_id UUID NOT NULL REFERENCES teams(id) ON DELETE CASCADE,
user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
role team_role NOT NULL DEFAULT 'member',
PRIMARY KEY (team_id, user_id)
);

-- Projects belonging to a team
CREATE TABLE projects (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
team_id UUID NOT NULL REFERENCES teams(id) ON DELETE CASCADE,
name TEXT NOT NULL,
created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
UNIQUE(team_id, name)
);

-- Environments within a project (dev, staging, etc.)
CREATE TABLE environments (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
name TEXT NOT NULL,
created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
UNIQUE(project_id, name)
);

-- Encrypted secrets for an environment
CREATE TABLE secrets (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
environment_id UUID NOT NULL REFERENCES environments(id) ON DELETE CASCADE,
key TEXT NOT NULL,
encrypted_value TEXT NOT NULL, -- The encrypted secret
iv TEXT NOT NULL, -- Initialization Vector for AES
created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
UNIQUE(environment_id, key)
);
Definitive Tech Stack Selections
Category Technology Version / Details Description / Purpose
Cloud Platform Supabase Hosted Primary BaaS for auth, database, and functions.
Database PostgreSQL 15+ Relational data store provided by Supabase.
Languages TypeScript 5.x Primary language for frontend, backend functions, and SDK.
Runtime Node.js, Deno Latest LTS For Next.js build/runtime and Supabase Edge Functions.
Frameworks Next.js 14+ Frontend application framework.
Testing Jest, React Testing Library, Playwright Latest For component, integration, and end-to-end testing.
CI/CD GitHub Actions N/A For automating tests and deployments from the monorepo.

Export to Sheets
Infrastructure and Deployment Overview
Backend: All backend components (Database, Auth, Edge Functions) are hosted and managed by Supabase.

Frontend: The Next.js web application will be deployed to Vercel.

Deployment Strategy: A CI/CD pipeline in GitHub Actions will be triggered on pushes to the main branch. The pipeline will run all tests (frontend and backend). On success, it will deploy the Next.js application to Vercel and deploy any updated Edge Functions to Supabase.

Security Best Practices
Row Level Security (RLS): Supabase RLS will be the primary mechanism for data access control. Policies will be written to ensure users can only access data (projects, secrets, etc.) belonging to teams they are a member of, and that their actions are constrained by their role.

Application-Level Encryption: All secret values will be encrypted before being stored in the database. The secrets table will store the encrypted value and the initialization vector (IV). The encryption key will be managed as a highly secure secret, inaccessible to the database itself.

API Key Security: The user's Supabase JWT will be used for API authentication. This token has a short lifespan and can be refreshed, providing better security than static API keys.

Input Validation: All Supabase Edge Functions must validate incoming request bodies to prevent malformed data.
