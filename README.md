# EzEnv - Secure Environment Variable Management Platform

EzEnv (SecureEnv Platform) is a secure environment variable management platform for development teams. It provides a centralized, secure way to manage and share project environment variables (.env files) across your team.

## Features

- 🔐 **Secure Storage**: All secrets are encrypted at rest using AES-256
- 👥 **Team Collaboration**: Role-based access control (<PERSON><PERSON>, Manager, Member)
- 🌍 **Multiple Environments**: Manage development, staging, and production configs
- 📦 **NPM Package**: Programmatic access via SDK for CI/CD integration
- 🚀 **Easy Import/Export**: Work seamlessly with existing .env files
- 🎨 **Neobrutalist Design**: Clean, bold UI with exceptional user experience

## Project Structure

This is a monorepo containing:

```
/
├── apps/
│   └── web/               # Next.js web application
├── packages/              # Future: NPM SDK package
├── docs/                  # Documentation and stories
├── package.json          # Root package.json with workspaces
├── tsconfig.json         # Root TypeScript config
└── README.md            # This file
```

## Tech Stack

- **Frontend**: Next.js 14+ with App Router, TypeScript, React
- **Styling**: Tailwind CSS with Neobrutalist design system
- **Backend**: Supabase (PostgreSQL, Auth, Edge Functions)
- **State Management**: Zustand
- **Testing**: Jest, React Testing Library, Playwright

## Getting Started

### Prerequisites

- Node.js 18+ and pnpm 8+
- A Supabase account and project

### Installation

1. Clone the repository:
```bash
git clone https://github.com/your-org/ezenv.git
cd ezenv
```

2. Install dependencies:
```bash
pnpm install
```

3. Set up environment variables:
```bash
cd apps/web
cp .env.local.example .env.local
```

Edit `.env.local` with your Supabase project credentials:
```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
ENCRYPTION_KEY=your_32_byte_hex_encryption_key
```

To generate an encryption key:
```bash
openssl rand -hex 32
```

4. Apply database migrations:
```bash
# Navigate to the web app directory
cd apps/web

# Apply migrations to your Supabase project
# Note: You'll need the Supabase CLI installed
supabase db push
```

### Development

Run the development server:

```bash
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

### Building for Production

```bash
pnpm build
```

### Running Tests

```bash
pnpm test
pnpm lint
pnpm typecheck
```

## Development Commands

From the root directory:

- `pnpm dev` - Start the development server
- `pnpm build` - Build all workspaces
- `pnpm lint` - Lint all workspaces
- `pnpm typecheck` - Type check all workspaces
- `pnpm test` - Run tests in all workspaces
- `pnpm clean` - Clean all node_modules

## Project Status

Currently in initial development. See `/docs/stories/` for implementation progress.

## Contributing

1. Check out the stories in `/docs/stories/` for planned features
2. Create a branch for your feature
3. Follow the existing code style and patterns
4. Write tests for your changes
5. Submit a pull request

## Security

- All secrets are encrypted before storage
- Row Level Security (RLS) enforces access control at the database level
- API keys are hashed using bcrypt
- Short-lived JWT tokens for authentication

## License

[License Type] - See LICENSE file for details