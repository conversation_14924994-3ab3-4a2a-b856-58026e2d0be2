{"name": "ezenv-sdk", "version": "1.0.1", "description": "SDK for fetching environment variables from EzEnv", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.js", "import": "./dist/index.mjs"}}, "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "jest", "prepublishOnly": "pnpm run build"}, "keywords": ["env", "environment", "variables", "config", "ezenv", "secrets"], "author": "EzEnv Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ezenv/ezenv"}, "dependencies": {}, "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^20.0.0", "jest": "^29.7.0", "ts-jest": "^29.1.0", "tsup": "^8.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=14.0.0"}}