# EzEnv SDK Configuration Example
# Copy this file to .env and update with your actual values

# Your EzEnv API key (get this from your dashboard)
EZENV_API_KEY=ezenv_your_api_key_here

# Base URL of your EzEnv instance
# For local development:
EZENV_BASE_URL=http://localhost:3000
# For production:
# EZENV_BASE_URL=https://ezenv.dev

# Example project configuration
PROJECT_NAME=my-awesome-app
ENVIRONMENT=development

# Node environment (used by some examples)
NODE_ENV=development