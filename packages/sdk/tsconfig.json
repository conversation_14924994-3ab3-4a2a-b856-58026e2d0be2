{"compilerOptions": {"target": "ES2020", "module": "ESNext", "lib": ["ES2020"], "declaration": true, "declarationMap": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test"]}