Master Prompt for Claude 4 Sonnet
Hello Claude. You are an expert frontend developer specializing in Next.js, TypeScript, and modern web technologies. Your task is to generate the initial codebase for a new web application based on the following detailed architecture.

1. Overall Project Context & Technology Stack

The project is a secure platform for developers to manage environment variables, called "SecureEnv Platform". The frontend will be a Next.js application using the App Router.

Your generated code MUST adhere strictly to this technology stack:

Framework: Next.js (App Router)

Language: TypeScript

Styling: Tailwind CSS

State Management: Zustand

Data Fetching (Server State): React Query (or SWR) - for now, just set up the API layer.

Backend/Auth: Supabase (we will handle the client setup).

2. Design System: "Neobrutalism" with Tailwind CSS

The visual style is Neobrutalism. This means:

High contrast (primarily black and white).

A single, bold accent color (e.g., electric yellow or blue).

Hard, sharp shadows and bold, 1-2px solid borders on elements.

Functional, clear typography. Use a clean sans-serif for UI and a monospaced font for code/secrets.

No gradients, subtle blurs, or overly complex animations.

You MUST use Tailwind CSS utility classes directly for all styling. Create a basic tailwind.config.ts file to support this.

3. Directory Structure

You MUST create files in the following directory structure inside /src:

/src
├── app/
│ ├── (auth)/
│ │ ├── login/page.tsx
│ │ └── signup/page.tsx
│ ├── (main)/
│ │ ├── dashboard/page.tsx
│ │ ├── project/[id]/page.tsx
│ │ ├── settings/page.tsx
│ │ └── layout.tsx
│ ├── api/
│ ├── globals.css
│ └── layout.tsx
├── components/
│ ├── atoms/
│ │ ├── Button.tsx
│ │ ├── Input.tsx
│ │ └── Card.tsx
│ └── molecules/
│ └── LoginForm.tsx
├── lib/
│ └── supabase.ts
├── store/
│ └── sessionStore.ts
└── types/
└── index.ts 4. Code Generation Task

Please generate the code for the following files. Provide the code for each file one by one, starting with its full file path.

File 1: tailwind.config.ts

Set up a basic config for a Next.js app. Add a placeholder for an accent color in the theme.extend.colors section.

File 2: src/app/globals.css

Include the standard Tailwind CSS @tailwind directives for base, components, and utilities.

File 3: src/components/atoms/Button.tsx

A reusable button component.

It should accept children, a variant prop ('primary' or 'secondary'), and other standard button props.

Style it with Tailwind using Neobrutalist principles (e.g., solid border, sharp shadow on hover/focus, bold text). 'primary' should use the accent color.

File 4: src/components/atoms/Input.tsx

A reusable text input component.

Style it with a bold, solid border and a clean look.

File 5: src/components/atoms/Card.tsx

A reusable card component to act as a container.

Style it with a solid border and a hard shadow.

File 6: src/app/layout.tsx

The root layout for the entire application. Include basic HTML structure and import globals.css.

File 7: src/app/(auth)/login/page.tsx

The login page.

It should have a simple layout with a heading "Login" and use a <Card> to contain a <LoginForm> molecule (which we will create next).

File 8: src/components/molecules/LoginForm.tsx

A client component ('use client').

It should contain a form with two <Input> components (for email and password) and a <Button> component for submission.

Use useState to manage the form inputs for now.

File 9: src/app/(main)/layout.tsx

The main layout for the protected part of the app.

It should have a simple persistent sidebar or header with placeholder navigation links for "Dashboard", "Settings", and a "Logout" button.

File 10: src/app/(main)/dashboard/page.tsx

A placeholder dashboard page.

It should have a heading "Dashboard" and a <Button> for "Create New Project".

File 11: src/lib/supabase.ts

Create a Supabase client instance using createBrowserClient from @supabase/ssr. Use environment variables NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY.

File 12: src/store/sessionStore.ts

Create a simple Zustand store.

The store's state should include session: Session | null and user: User | null.

Include a setSession action.

Please begin with the first file.
