# EzEnv Tech Stack & Architecture Decisions

## Core Technology Stack

### Frontend
- **Framework**: Next.js 14.2+ (App Router)
- **Language**: TypeScript 5.5+
- **Styling**: Tailwind CSS 3.4+
- **UI Components**: Custom Neobrutalist design system
- **State Management**: Zustand 4.5+
- **Form Handling**: React Hook Form 7.5+ with Zod validation
- **HTTP Client**: Axios 1.6+ with interceptors
- **Date Handling**: date-fns 3.0+
- **Icons**: Lucide React

### Backend & Infrastructure
- **Platform**: Supabase (PostgreSQL 15+, Edge Functions, Auth)
- **API**: RESTful with Supabase PostgREST
- **Authentication**: Supabase Auth (JWT-based)
- **Encryption**: Node.js crypto (AES-256-GCM)
- **File Storage**: Supabase Storage (for exports/backups)
- **Rate Limiting**: Supabase Edge Functions built-in
- **Monitoring**: Sentry for error tracking

### Development Tools
- **Package Manager**: pnpm 9.0+
- **Build Tool**: Next.js built-in (Turbopack)
- **Linting**: ESLint 9.0+ with custom config
- **Formatting**: Prettier 3.2+
- **Git Hooks**: Husky 9.0+ with lint-staged
- **Testing**: Jest 29+, React Testing Library, Playwright 1.40+
- **CI/CD**: GitHub Actions

### NPM Package (SDK)
- **Build**: tsup for bundling
- **Target**: Node.js 18+ and modern browsers
- **Distribution**: npm registry as @ezenv/sdk

## Architecture Patterns

### Frontend Architecture
```
App Router Structure:
- Parallel routes for auth flows
- Route groups for layout sharing
- Server Components by default
- Client Components only when needed
- Middleware for auth protection
```

### Data Flow
```
User Action → Client Component → Server Action → Supabase Edge Function → Database
                                       ↓
                              Encryption/Decryption Layer
```

### Security Architecture
1. **Zero Trust**: Never trust client-side data
2. **Defense in Depth**: Multiple security layers
3. **Least Privilege**: Minimal permissions by default
4. **Encryption at Rest**: All secrets encrypted in database
5. **Encryption in Transit**: HTTPS only

## Technology Rationale

### Why Next.js App Router?
- Server Components reduce client bundle size
- Built-in API routes for custom endpoints
- Excellent TypeScript support
- SEO capabilities for landing pages
- Progressive enhancement

### Why Supabase?
- Complete backend solution (Auth, DB, Storage, Functions)
- Row Level Security for fine-grained access control
- Real-time capabilities for future features
- Open source with self-hosting option
- PostgreSQL for complex queries

### Why Zustand?
- Minimal boilerplate
- TypeScript first
- DevTools support
- Excellent performance
- Simple async actions

### Why Tailwind CSS?
- Rapid prototyping
- Consistent spacing/sizing
- Perfect for Neobrutalist design
- Small production bundle
- Component-friendly

## Performance Targets

- **First Contentful Paint**: < 1.2s
- **Time to Interactive**: < 2.5s
- **Lighthouse Score**: > 90
- **Bundle Size**: < 200KB (initial)
- **API Response Time**: < 200ms (p95)

## Browser Support

- Chrome/Edge 100+
- Firefox 100+
- Safari 15+
- No IE11 support

## Mobile Support

- Responsive design (mobile-first)
- Touch-optimized interactions
- PWA capabilities (future)