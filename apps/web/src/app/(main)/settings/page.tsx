'use client'

import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { redirect } from 'next/navigation'
import ApiKeysSection from '@/components/molecules/ApiKeysSection'
import SecuritySettings from '@/components/molecules/SecuritySettings'
import ProfileSettings from '@/components/molecules/ProfileSettings'

type Tab = 'profile' | 'api-keys' | 'security'

export default function SettingsPage() {
  const { user, loading } = useAuth()
  const [activeTab, setActiveTab] = useState<Tab>('profile')

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-lg">Loading...</div>
      </div>
    )
  }

  if (!user) {
    redirect('/login')
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <h1 className="text-3xl font-bold mb-8">Account Settings</h1>
      
      <div className="flex flex-col md:flex-row gap-8">
        {/* Navigation Tabs */}
        <nav className="w-full md:w-64">
          <ul className="space-y-2">
            <li>
              <button
                onClick={() => setActiveTab('profile')}
                className={`w-full text-left px-4 py-3 rounded-lg border-2 border-black transition-all ${
                  activeTab === 'profile'
                    ? 'bg-black text-white shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]'
                    : 'bg-white hover:shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]'
                }`}
              >
                Profile
              </button>
            </li>
            <li>
              <button
                onClick={() => setActiveTab('api-keys')}
                className={`w-full text-left px-4 py-3 rounded-lg border-2 border-black transition-all ${
                  activeTab === 'api-keys'
                    ? 'bg-black text-white shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]'
                    : 'bg-white hover:shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]'
                }`}
              >
                API Keys
              </button>
            </li>
            <li>
              <button
                onClick={() => setActiveTab('security')}
                className={`w-full text-left px-4 py-3 rounded-lg border-2 border-black transition-all ${
                  activeTab === 'security'
                    ? 'bg-black text-white shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]'
                    : 'bg-white hover:shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]'
                }`}
              >
                Security
              </button>
            </li>
          </ul>
        </nav>

        {/* Content Area */}
        <div className="flex-1">
          {activeTab === 'profile' && (
            <div className="bg-white border-2 border-black rounded-lg p-6 shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
              <h2 className="text-2xl font-bold mb-4">Profile</h2>
              <ProfileSettings />
            </div>
          )}

          {activeTab === 'api-keys' && (
            <div className="bg-white border-2 border-black rounded-lg p-6 shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
              <h2 className="text-2xl font-bold mb-2">API Keys</h2>
              <p className="text-gray-600 mb-6">
                Use API keys to authenticate your applications and fetch secrets programmatically.
              </p>
              <ApiKeysSection />
            </div>
          )}

          {activeTab === 'security' && (
            <div className="bg-white border-2 border-black rounded-lg p-6 shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
              <h2 className="text-2xl font-bold mb-4">Security</h2>
              <SecuritySettings />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}