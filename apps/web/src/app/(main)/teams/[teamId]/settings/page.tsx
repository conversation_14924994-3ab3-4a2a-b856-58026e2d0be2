'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/atoms/Button'
import { createClient } from '@/lib/supabase/client'
import { useTeamContext } from '@/contexts/TeamContext'
import { deleteTeam } from '@/lib/teams/api'

interface TeamDetails {
  id: string
  name: string
  created_at: string
  member_count: number
  project_count: number
}

export default function TeamSettingsPage() {
  const params = useParams()
  const teamId = params.teamId as string
  const { } = useTeamContext() // Just to trigger refresh on team changes
  
  const [team, setTeam] = useState<TeamDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isAdmin, setIsAdmin] = useState(false)
  const [teamName, setTeamName] = useState('')
  const [updating, setUpdating] = useState(false)
  const [deleteConfirm, setDeleteConfirm] = useState(false)
  const [deleting, setDeleting] = useState(false)
  const [userTeamCount, setUserTeamCount] = useState(1)

  useEffect(() => {
    async function fetchTeamDetails() {
      const supabase = createClient()
      
      try {
        // Get user
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) {
          setError('Not authenticated')
          return
        }

        // Check if user is admin of this team
        const { data: memberData } = await supabase
          .from('team_members')
          .select('role')
          .eq('team_id', teamId)
          .eq('user_id', user.id)
          .single()

        if (!memberData) {
          setError('You are not a member of this team')
          return
        }

        setIsAdmin(memberData.role === 'admin')

        // Get user's team count
        const { data: teamMemberships } = await supabase
          .from('team_members')
          .select('team_id')
          .eq('user_id', user.id)
        
        if (teamMemberships) {
          setUserTeamCount(teamMemberships.length)
        }

        // Get team details
        const { data: teamData, error: teamError } = await supabase
          .from('teams')
          .select(`
            *,
            team_members!inner(count),
            projects(count)
          `)
          .eq('id', teamId)
          .single()

        if (teamError) {
          setError('Failed to load team details')
          return
        }

        const teamDetails: TeamDetails = {
          id: teamData.id,
          name: teamData.name,
          created_at: teamData.created_at,
          member_count: teamData.team_members[0].count || 0,
          project_count: teamData.projects[0]?.count || 0
        }

        setTeam(teamDetails)
        setTeamName(teamDetails.name)
      } catch {
        setError('Failed to load team settings')
      } finally {
        setLoading(false)
      }
    }

    fetchTeamDetails()
  }, [teamId])

  const handleUpdateTeam = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!team || !teamName.trim() || teamName === team.name) return

    setUpdating(true)
    const supabase = createClient()

    try {
      const { error } = await supabase
        .from('teams')
        .update({ name: teamName.trim() })
        .eq('id', teamId)

      if (error) {
        alert('Failed to update team name')
      } else {
        // Update local state
        setTeam({ ...team, name: teamName.trim() })
        // Reload to update context
        window.location.reload()
      }
    } catch {
      alert('Failed to update team')
    } finally {
      setUpdating(false)
    }
  }

  const handleDeleteTeam = async () => {
    if (!team) return

    setDeleting(true)
    
    const { error } = await deleteTeam(teamId)
    
    if (error) {
      alert(error.message)
      setDeleting(false)
    } else {
      // Redirect to teams page - the API already handled team switching
      window.location.href = '/teams'
    }
    
    setDeleteConfirm(false)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="inline-block w-16 h-16 border-4 border-black border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 font-bold">Loading team settings...</p>
        </div>
      </div>
    )
  }

  if (error || !team) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="bg-red-50 border-2 border-red-500 p-6 text-center">
          <h2 className="text-xl font-bold mb-2">Error</h2>
          <p className="text-red-700 mb-4">{error || 'Team not found'}</p>
          <Link href="/teams">
            <Button>Back to Teams</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header with breadcrumb */}
      <div className="mb-8">
        <div className="flex items-center gap-2 text-sm text-gray-600 mb-4">
          <Link href="/teams" className="hover:underline">
            Teams
          </Link>
          <span>/</span>
          <span>{team.name}</span>
          <span>/</span>
          <span>Settings</span>
        </div>
        <h1 className="text-3xl font-bold mb-2">Team Settings</h1>
        <p className="text-gray-600">Manage settings for {team.name}.</p>
      </div>

      {/* Tabs */}
      <div className="border-b-2 border-black mb-6">
        <div className="flex gap-4">
          <Link
            href={`/teams/${teamId}/members`}
            className="px-4 py-3 font-medium border-b-2 border-transparent hover:border-gray-400"
          >
            Members
          </Link>
          <button className="px-4 py-3 font-medium bg-yellow-400 border-2 border-black border-b-0 -mb-0.5 font-bold">
            Settings
          </button>
        </div>
      </div>

      {/* Settings Content */}
      <div className="space-y-8">
        {/* General Settings */}
        <div className="bg-white border-2 border-black p-6 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
          <h2 className="text-xl font-bold mb-4">General Settings</h2>
          
          <form onSubmit={handleUpdateTeam} className="space-y-4">
            <div>
              <label htmlFor="team-name" className="block text-sm font-medium mb-2">
                Team Name
              </label>
              <input
                id="team-name"
                type="text"
                value={teamName}
                onChange={(e) => setTeamName(e.target.value)}
                className="w-full px-3 py-2 border-2 border-black focus:outline-none focus:shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]"
                disabled={!isAdmin || updating}
              />
              {!isAdmin && (
                <p className="text-sm text-gray-600 mt-1">Only team admins can change the team name</p>
              )}
            </div>

            {isAdmin && (
              <Button
                type="submit"
                disabled={updating || !teamName.trim() || teamName === team.name}
              >
                {updating ? 'Updating...' : 'Update Team Name'}
              </Button>
            )}
          </form>
        </div>

        {/* Team Info */}
        <div className="bg-gray-100 border-2 border-black p-6 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
          <h2 className="text-xl font-bold mb-4">Team Information</h2>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-600">Created</p>
              <p className="font-medium">{new Date(team.created_at).toLocaleDateString()}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Members</p>
              <p className="font-medium">{team.member_count}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Projects</p>
              <p className="font-medium">{team.project_count}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Team ID</p>
              <p className="font-mono text-sm">{team.id}</p>
            </div>
          </div>
        </div>

        {/* Danger Zone */}
        {isAdmin && (
          <div className="bg-red-50 border-2 border-red-500 p-6">
            <h2 className="text-xl font-bold mb-4 text-red-700">Danger Zone</h2>
            <p className="text-red-700 mb-4">
              {userTeamCount === 1 ? (
                <>You cannot delete your only team. Every user must belong to at least one team. Create another team first before deleting this one.</>
              ) : (
                <>Deleting a team will permanently remove all projects, environments, and secrets associated with it. This action cannot be undone.</>
              )}
            </p>
            
            {!deleteConfirm ? (
              <Button
                variant="secondary"
                className="border-red-600 text-red-600 hover:bg-red-50"
                onClick={() => setDeleteConfirm(true)}
                disabled={userTeamCount === 1}
              >
                Delete Team
              </Button>
            ) : (
              <div className="space-y-4">
                <p className="font-bold text-red-700">Are you absolutely sure?</p>
                <div className="flex gap-4">
                  <button
                    className="px-4 py-2 bg-red-600 text-white font-bold border-2 border-black hover:shadow-[2px_2px_0px_0px_rgba(0,0,0,1)] transition-all disabled:opacity-50"
                    onClick={handleDeleteTeam}
                    disabled={deleting}
                  >
                    {deleting ? 'Deleting...' : 'Yes, Delete Team'}
                  </button>
                  <Button
                    variant="secondary"
                    onClick={() => setDeleteConfirm(false)}
                    disabled={deleting}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}