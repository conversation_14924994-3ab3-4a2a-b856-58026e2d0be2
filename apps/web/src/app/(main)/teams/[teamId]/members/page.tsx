'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/atoms/Button'
import { InviteMemberModal } from '@/components/molecules/InviteMemberModal'
import { MembersList } from '@/components/molecules/MembersList'
import { InvitationsList } from '@/components/molecules/InvitationsList'
import { useTeamContext } from '@/contexts/TeamContext'
import { getUserTeamRole } from '@/lib/teams/api'
import { useTeamInvitations } from '@/lib/invitations/hooks'
import { getInvitationStatus } from '@/lib/invitations/api'
import { PermissionGuard } from '@/components/atoms/PermissionGuard'
import { PERMISSIONS } from '@/lib/permissions/types'

export default function TeamMembersPage() {
  const params = useParams()
  const router = useRouter()
  const teamId = params.teamId as string
  const { teams } = useTeamContext()
  const [userRole, setUserRole] = useState<string | null>(null)
  const [showInviteModal, setShowInviteModal] = useState(false)
  const { invitations, loading: invitationsLoading, invite, cancel, resend } = useTeamInvitations(teamId)
  
  // Find the team from context
  const team = teams.find(t => t.id === teamId)
  
  // Get user's role in this team
  useEffect(() => {
    async function fetchRole() {
      const role = await getUserTeamRole(teamId)
      setUserRole(role)
      
      // Redirect if user doesn't have access
      if (!role) {
        router.push('/teams')
      }
    }
    fetchRole()
  }, [teamId, router])

  if (!team || !userRole) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="inline-block w-16 h-16 border-4 border-black border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 font-bold">Loading team...</p>
        </div>
      </div>
    )
  }

  const pendingInvitations = invitations.filter(inv => getInvitationStatus(inv) === 'pending')

  return (
    <div>
      {/* Header with breadcrumb */}
      <div className="mb-8">
        <div className="flex items-center gap-2 text-sm text-gray-600 mb-4">
          <Link href="/teams" className="hover:underline">
            Teams
          </Link>
          <span>/</span>
          <span>{team.name}</span>
          <span>/</span>
          <span>Members</span>
        </div>
        <h1 className="text-3xl font-bold mb-2">Team Members</h1>
        <p className="text-gray-600">Manage who has access to {team.name}&apos;s projects.</p>
      </div>

      {/* Tabs */}
      <div className="border-b-2 border-black mb-6">
        <div className="flex gap-4">
          <button className="px-4 py-3 font-medium bg-yellow-400 border-2 border-black border-b-0 -mb-0.5 font-bold">
            Members
          </button>
          <Link
            href={`/teams/${teamId}/settings`}
            className="px-4 py-3 font-medium border-b-2 border-transparent hover:border-gray-400"
          >
            Settings
          </Link>
        </div>
      </div>

      {/* Invite button */}
      <PermissionGuard permission={PERMISSIONS.MEMBER_INVITE}>
        <div className="flex justify-end mb-6">
          <Button
            variant="primary"
            onClick={() => setShowInviteModal(true)}
          >
            Invite Member
          </Button>
        </div>
      </PermissionGuard>

      {/* Active Members */}
      <div className="mb-8">
        <h2 className="text-xl font-bold mb-4">
          Active Members ({team.member_count})
        </h2>
        <MembersList teamId={teamId} />
      </div>

      {/* Pending Invitations */}
      <PermissionGuard permission={PERMISSIONS.MEMBER_INVITE}>
        {pendingInvitations.length > 0 && (
          <div>
            <h2 className="text-xl font-bold mb-4">
              Pending Invitations ({pendingInvitations.length})
            </h2>
            <InvitationsList 
              invitations={pendingInvitations}
              loading={invitationsLoading}
              onCancel={cancel}
              onResend={resend}
            />
          </div>
        )}
      </PermissionGuard>

      {/* Empty state for invitations */}
      <PermissionGuard permission={PERMISSIONS.MEMBER_INVITE}>
        {pendingInvitations.length === 0 && !invitationsLoading && (
          <div className="bg-gray-100 border-2 border-gray-300 p-8 text-center">
            <p className="text-gray-600 mb-4">
              No pending invitations. Invite team members to collaborate on your projects.
            </p>
          </div>
        )}
      </PermissionGuard>

      {/* Invite Member Modal */}
      {showInviteModal && (
        <InviteMemberModal
          onClose={() => setShowInviteModal(false)}
          onInvite={invite}
        />
      )}
    </div>
  )
}