import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useRouter, useSearchParams } from 'next/navigation'
import TeamsPage from '../page'
import { useTeamContext } from '@/contexts/TeamContext'

// Mock the router and search params
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn()
}))

// Mock the TeamContext
jest.mock('@/contexts/TeamContext')

// Mock the CreateTeamModal component
jest.mock('@/components/molecules/CreateTeamModal', () => ({
  CreateTeamModal: ({ onClose }: { onClose: () => void }) => (
    <div data-testid="create-team-modal">
      <button onClick={onClose}>Close Modal</button>
    </div>
  )
}))

describe('TeamsPage', () => {
  const mockRouter = {
    replace: jest.fn()
  }

  const mockSearchParams = {
    get: jest.fn()
  }

  const mockTeamContext = {
    teams: [
      {
        id: 'team-1',
        name: 'Personal Team',
        role: 'admin',
        member_count: 1,
        project_count: 3,
        created_at: '2024-01-01',
        updated_at: '2024-01-01'
      },
      {
        id: 'team-2',
        name: 'Work Team',
        role: 'member',
        member_count: 5,
        project_count: 10,
        created_at: '2024-01-02',
        updated_at: '2024-01-02'
      }
    ],
    currentTeam: {
      id: 'team-1',
      name: 'Personal Team',
      role: 'admin',
      member_count: 1,
      project_count: 3,
      created_at: '2024-01-01',
      updated_at: '2024-01-01'
    },
    loading: false,
    error: null,
    switchTeam: jest.fn(),
    createTeam: jest.fn(),
    updateTeam: jest.fn(),
    refetch: jest.fn()
  }

  beforeEach(() => {
    jest.clearAllMocks()
    ;(useRouter as jest.Mock).mockReturnValue(mockRouter)
    ;(useSearchParams as jest.Mock).mockReturnValue(mockSearchParams)
    ;(useTeamContext as jest.Mock).mockReturnValue(mockTeamContext)
  })

  it('should render teams list', () => {
    render(<TeamsPage />)

    expect(screen.getByText('Team Settings')).toBeInTheDocument()
    expect(screen.getByText('Personal Team')).toBeInTheDocument()
    expect(screen.getByText('Work Team')).toBeInTheDocument()
  })

  it('should show loading state', () => {
    ;(useTeamContext as jest.Mock).mockReturnValue({
      ...mockTeamContext,
      loading: true
    })

    render(<TeamsPage />)

    expect(screen.getByText('Loading teams...')).toBeInTheDocument()
  })

  it('should show error state', () => {
    const error = new Error('Failed to load teams')
    ;(useTeamContext as jest.Mock).mockReturnValue({
      ...mockTeamContext,
      error
    })

    render(<TeamsPage />)

    expect(screen.getByText(`Error loading teams: ${error.message}`)).toBeInTheDocument()
  })

  it('should show current team selector', () => {
    render(<TeamsPage />)

    expect(screen.getByText('Current Team')).toBeInTheDocument()
    const selector = screen.getByRole('combobox')
    expect(selector).toHaveValue('team-1')
  })

  it('should handle team switching', async () => {
    mockSearchParams.get.mockReturnValue('team-2')

    render(<TeamsPage />)

    await waitFor(() => {
      expect(mockTeamContext.switchTeam).toHaveBeenCalledWith('team-2')
    })

    await waitFor(() => {
      expect(mockRouter.replace).toHaveBeenCalledWith('/teams')
    })
  })

  it('should show create team modal when button clicked', () => {
    render(<TeamsPage />)

    const createButton = screen.getAllByText('Create New Team')[0]
    fireEvent.click(createButton)

    expect(screen.getByTestId('create-team-modal')).toBeInTheDocument()
  })

  it('should show empty state for single team', () => {
    ;(useTeamContext as jest.Mock).mockReturnValue({
      ...mockTeamContext,
      teams: [mockTeamContext.teams[0]]
    })

    render(<TeamsPage />)

    expect(screen.getByText(/You're currently in your personal team/)).toBeInTheDocument()
  })

  it('should show admin badge for admin teams', () => {
    render(<TeamsPage />)

    const adminBadges = screen.getAllByText('Admin')
    expect(adminBadges.length).toBeGreaterThan(0)
  })

  it('should show member badge for member teams', () => {
    render(<TeamsPage />)

    const memberBadges = screen.getAllByText('Member')
    expect(memberBadges.length).toBeGreaterThan(0)
  })

  it('should show manage team button for admin teams', () => {
    render(<TeamsPage />)

    const manageButtons = screen.getAllByText('Manage Team')
    expect(manageButtons.length).toBe(1) // Only for admin team
  })

  it('should navigate to team members page when manage clicked', () => {
    // Mock window.location.href
    const mockHref = jest.fn()
    Object.defineProperty(window, 'location', {
      value: {
        get href() {
          return mockHref.mock.lastCall?.[0] || ''
        },
        set href(value: string) {
          mockHref(value)
        }
      },
      writable: true
    })

    render(<TeamsPage />)

    const manageButton = screen.getByText('Manage Team')
    fireEvent.click(manageButton)

    expect(mockHref).toHaveBeenCalledWith('/teams/team-1/members')
  })
})