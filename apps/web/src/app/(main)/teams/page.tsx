'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/atoms/Button'
import { CreateTeamModal } from '@/components/molecules/CreateTeamModal'
import { useTeamContext } from '@/contexts/TeamContext'
import { PendingInvitations } from '@/components/molecules/PendingInvitations'

export default function TeamsPage() {
  const { teams, currentTeam, loading, error, switchTeam } = useTeamContext()
  const [showCreateModal, setShowCreateModal] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()
  
  useEffect(() => {
    const switchTo = searchParams.get('switch')
    if (switchTo && switchTo !== currentTeam?.id) {
      // Only switch if it's a different team
      switchTeam(switchTo)
      // No need to replace route since switchTeam will reload the page
    }
  }, [searchParams, currentTeam, switchTeam])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="inline-block w-16 h-16 border-4 border-black border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 font-bold">Loading teams...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-100 border-2 border-red-500 p-4">
        <p className="text-red-700">Error loading teams: {error.message}</p>
      </div>
    )
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Team Settings</h1>
        <p className="text-gray-600">Manage your teams and collaborate with others.</p>
      </div>

      <PendingInvitations />

      {/* Current Team Selector */}
      <div className="mb-8 bg-gray-100 border-2 border-black p-6 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
        <h2 className="text-xl font-bold mb-4">Current Team</h2>
        <div className="flex items-center justify-between">
          <div>
            <p className="text-lg font-medium">{currentTeam?.name}</p>
            <p className="text-sm text-gray-600">
              {currentTeam?.role === 'admin' ? 'Admin' : currentTeam?.role === 'manager' ? 'Manager' : 'Member'} • 
              {currentTeam?.member_count} {currentTeam?.member_count === 1 ? 'member' : 'members'}
            </p>
          </div>
          {teams.length > 1 && (
            <select
              value={currentTeam?.id}
              onChange={(e) => {
                window.location.href = `/teams?switch=${e.target.value}`
              }}
              className="px-4 py-2 border-2 border-black bg-white font-medium focus:outline-none focus:shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]"
            >
              {teams.map((team) => (
                <option key={team.id} value={team.id}>
                  {team.name}
                </option>
              ))}
            </select>
          )}
        </div>
      </div>

      {/* Teams List */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold">Your Teams</h2>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {teams.map((team) => (
            <div
              key={team.id}
              className={`
                bg-white border-2 border-black p-6 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]
                ${team.id === currentTeam?.id ? 'ring-2 ring-blue-500' : ''}
              `}
            >
              <h3 className="text-lg font-bold mb-2">{team.name}</h3>
              <div className="text-sm text-gray-600 mb-4">
                <p>{team.member_count} {team.member_count === 1 ? 'member' : 'members'} • {team.project_count} {team.project_count === 1 ? 'project' : 'projects'}</p>
                <p className="mt-1">Created {new Date(team.created_at).toLocaleDateString()}</p>
              </div>
              <div className="flex items-center justify-between">
                <span className={`
                  px-3 py-1 text-sm font-medium
                  ${team.role === 'admin' ? 'bg-blue-100 text-blue-700' : team.role === 'manager' ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'}
                `}>
                  {team.role === 'admin' ? 'Admin' : team.role === 'manager' ? 'Manager' : 'Member'}
                </span>
                {(team.role === 'admin' || team.role === 'manager') && (
                  <Button
                    variant="secondary"
                    onClick={() => {
                      router.push(`/teams/${team.id}/members`)
                    }}
                  >
                    View Team
                  </Button>
                )}
              </div>
            </div>
          ))}

          {/* Create New Team Card */}
          <button
            onClick={() => setShowCreateModal(true)}
            className="bg-gray-100 border-2 border-dashed border-gray-400 p-6 hover:border-black hover:bg-gray-200 transition-all flex flex-col items-center justify-center text-gray-600 hover:text-black"
          >
            <svg className="w-12 h-12 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <p className="font-medium">Create New Team</p>
          </button>
        </div>
      </div>

      {/* Empty State */}
      {teams.length === 1 && (
        <div className="bg-gray-100 border-2 border-gray-300 p-8 text-center">
          <p className="text-gray-600 mb-4">
            You&apos;re currently in your personal team. Create additional teams to collaborate with others.
          </p>
          <Button onClick={() => setShowCreateModal(true)}>
            Create New Team
          </Button>
        </div>
      )}

      {/* Create Team Modal */}
      {showCreateModal && (
        <CreateTeamModal onClose={() => setShowCreateModal(false)} />
      )}
    </div>
  )
}