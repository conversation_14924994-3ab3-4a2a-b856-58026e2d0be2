'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSearchParams } from 'next/navigation'
import { Button } from '@/components/atoms/Button'
import { CreateTeamModal } from '@/components/molecules/CreateTeamModal'
import { useTeamContext } from '@/contexts/TeamContext'
import { PendingInvitations } from '@/components/molecules/PendingInvitations'
import { useToast } from '@/contexts/ToastContext'
import { TeamCard } from '@/components/molecules/TeamCard'
import { TeamCardSkeleton } from '@/components/molecules/TeamCardSkeleton'
import { Skeleton } from '@/components/atoms/Skeleton'
import { getRoleLabel, formatMemberCount } from '@/lib/teams/utils'

export default function TeamsPage() {
  const { teams, currentTeam, loading, error, switchTeam } = useTeamContext()
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [isSwitching, setIsSwitching] = useState(false)
  const searchParams = useSearchParams()
  const { showError, showInfo } = useToast()
  
  // Memoize the switch handler to prevent unnecessary re-renders
  const handleTeamSwitch = useCallback(async (teamId: string) => {
    if (teamId !== currentTeam?.id && !isSwitching) {
      try {
        setIsSwitching(true)
        showInfo('Switching team...')
        await switchTeam(teamId)
      } catch (err) {
        console.error('Failed to switch team:', err)
        showError('Failed to switch team. Please try again.')
        setIsSwitching(false)
      }
    }
  }, [currentTeam?.id, switchTeam, showError, showInfo, isSwitching])

  useEffect(() => {
    const switchTo = searchParams.get('switch')
    if (switchTo) {
      handleTeamSwitch(switchTo)
    }
  }, [searchParams, handleTeamSwitch])

  if (loading && teams.length === 0) {
    return (
      <div>
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Team Settings</h1>
          <p className="text-gray-600">Manage your teams and collaborate with others.</p>
        </div>
        
        <div className="mb-8 bg-gray-100 border-2 border-black p-6 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
          <Skeleton variant="text" width="30%" className="mb-4 h-6" />
          <Skeleton variant="text" width="50%" />
        </div>
        
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <TeamCardSkeleton />
          <TeamCardSkeleton />
          <TeamCardSkeleton />
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-100 border-2 border-red-500 p-4">
        <p className="text-red-700">Error loading teams: {error.message}</p>
      </div>
    )
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Team Settings</h1>
        <p className="text-gray-600">Manage your teams and collaborate with others.</p>
      </div>

      <PendingInvitations />

      {/* Current Team Selector */}
      <div className="mb-8 bg-gray-100 border-2 border-black p-6 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
        <h2 className="text-xl font-bold mb-4">Current Team</h2>
        <div className="flex items-center justify-between">
          <div>
            <p className="text-lg font-medium">{currentTeam?.name}</p>
            <p className="text-sm text-gray-600">
              {getRoleLabel(currentTeam?.role || '')} • {formatMemberCount(currentTeam?.member_count || 0)}
            </p>
          </div>
          {teams.length > 1 && (
            <select
              value={currentTeam?.id}
              onChange={(e) => {
                window.location.href = `/teams?switch=${e.target.value}`
              }}
              className="px-4 py-2 border-2 border-black bg-white font-medium focus:outline-none focus:shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]"
              aria-label="Switch team"
            >
              {teams.map((team) => (
                <option key={team.id} value={team.id}>
                  {team.name}
                </option>
              ))}
            </select>
          )}
        </div>
      </div>

      {/* Teams List */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold">Your Teams</h2>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {teams.map((team) => (
            <TeamCard
              key={team.id}
              team={team}
              isCurrentTeam={team.id === currentTeam?.id}
            />
          ))}

          {/* Create New Team Card */}
          <button
            onClick={() => setShowCreateModal(true)}
            className="bg-gray-100 border-2 border-dashed border-gray-400 p-6 hover:border-black hover:bg-gray-200 transition-all flex flex-col items-center justify-center text-gray-600 hover:text-black"
          >
            <svg className="w-12 h-12 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <p className="font-medium">Create New Team</p>
          </button>
        </div>
      </div>

      {/* Empty State */}
      {teams.length === 1 && (
        <div className="bg-gray-100 border-2 border-gray-300 p-8 text-center">
          <p className="text-gray-600 mb-4">
            You&apos;re currently in your personal team. Create additional teams to collaborate with others.
          </p>
          <Button onClick={() => setShowCreateModal(true)}>
            Create New Team
          </Button>
        </div>
      )}

      {/* Create Team Modal */}
      {showCreateModal && (
        <CreateTeamModal onClose={() => setShowCreateModal(false)} />
      )}
    </div>
  )
}