'use client'

import { useState, useMemo } from 'react'
import { But<PERSON> } from '@/components/atoms/Button'
import { ProjectList } from '@/components/molecules/ProjectList'
import { CreateProjectModal } from '@/components/molecules/CreateProjectModal'
import { useProjects } from '@/lib/projects/hooks'
import { PermissionGuard } from '@/components/atoms/PermissionGuard'
import { PERMISSIONS } from '@/lib/permissions/types'
import { PendingInvitations } from '@/components/molecules/PendingInvitations'

export default function DashboardPage() {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const { projects, loading, error, createProject } = useProjects()
  
  // Filter projects based on search query
  const filteredProjects = useMemo(() => {
    if (!searchQuery.trim()) return projects
    
    const query = searchQuery.toLowerCase()
    return projects.filter(project => 
      project.name.toLowerCase().includes(query) ||
      project.team?.name.toLowerCase().includes(query)
    )
  }, [projects, searchQuery])

  return (
    <div>
      <PendingInvitations />
      
      <div className="mb-8 flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold mb-2">My Projects</h1>
          <p className="text-gray-600">Organize your environment variables by project.</p>
        </div>
        <PermissionGuard permission={PERMISSIONS.PROJECT_CREATE}>
          <Button
            variant="primary"
            onClick={() => setIsCreateModalOpen(true)}
            className="flex items-center gap-2"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            Create New Project
          </Button>
        </PermissionGuard>
      </div>

      {/* Search bar */}
      {projects.length > 0 && !loading && (
        <div className="mb-6 max-w-md">
          <div className="relative">
            <svg 
              className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <input
              type="text"
              placeholder="Search projects..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="input-brutal"
              style={{ paddingLeft: '3rem' }}
            />
          </div>
        </div>
      )}

      {error && (
        <div className="mb-6 p-4 bg-red-50 border-2 border-red-500 text-red-700" role="alert">
          {error.message}
        </div>
      )}

      <ProjectList projects={filteredProjects} loading={loading} />
      
      {/* Show message when search has no results */}
      {!loading && searchQuery && filteredProjects.length === 0 && projects.length > 0 && (
        <div className="text-center py-8">
          <p className="text-gray-600">No projects found matching &quot;{searchQuery}&quot;</p>
          <button 
            onClick={() => setSearchQuery('')}
            className="mt-2 text-sm font-medium underline hover:no-underline"
          >
            Clear search
          </button>
        </div>
      )}

      <CreateProjectModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onCreateProject={createProject}
      />

    </div>
  )
}