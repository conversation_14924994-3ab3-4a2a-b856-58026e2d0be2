'use client'

import { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { getProject, deleteProject } from '@/lib/projects/api'
import { Project } from '@/lib/projects/types'
import { useEnvironments } from '@/lib/environments/hooks'
import { CreateEnvironmentModal } from '@/components/molecules/CreateEnvironmentModal'
import { useSecrets } from '@/lib/secrets/hooks'
import { SecretsList } from '@/components/molecules/SecretsList'
import { AddSecretModal } from '@/components/molecules/AddSecretModal'
import { ImportSecretsModal } from '@/components/molecules/ImportSecretsModal'
import { ProjectSettingsModal } from '@/components/molecules/ProjectSettingsModal'
import { Button } from '@/components/atoms/Button'
import { batchImportSecrets } from '@/lib/secrets/api'
import { ParsedEnvVar } from '@/lib/env/parser'
import { exportSecrets, downloadFile, generateExportFilename } from '@/lib/env/export'
import { PermissionGuard } from '@/components/atoms/PermissionGuard'
import { PERMISSIONS } from '@/lib/permissions/types'

export default function ProjectDetailPage() {
  const params = useParams()
  const projectId = params.id as string
  
  const [project, setProject] = useState<Project | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isCreateEnvModalOpen, setIsCreateEnvModalOpen] = useState(false)
  const [isAddSecretModalOpen, setIsAddSecretModalOpen] = useState(false)
  const [isImportModalOpen, setIsImportModalOpen] = useState(false)
  const [exportLoading, setExportLoading] = useState(false)
  const [deleteLoading, setDeleteLoading] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [showProjectSettings, setShowProjectSettings] = useState(false)
  const router = useRouter()
  
  const { 
    environments, 
    selectedEnvironment, 
    loading: envLoading,
    createEnvironment,
    selectEnvironment 
  } = useEnvironments(projectId)
  
  const {
    secrets,
    loading: secretsLoading,
    error: secretsError,
    createSecret,
    updateSecret,
    deleteSecret,
    decryptSecret,
    getDecryptedValue,
    clearDecryptedValue,
    refetch: refetchSecrets
  } = useSecrets(selectedEnvironment?.id || '')

  // Import handler
  const handleImportSecrets = async (
    variables: ParsedEnvVar[], 
    duplicateStrategy: 'replace' | 'skip'
  ) => {
    if (!selectedEnvironment) {
      return { 
        success: false, 
        imported: 0, 
        skipped: 0, 
        errors: ['No environment selected'] 
      }
    }

    const secretsToImport = variables.map(v => ({ 
      key: v.key, 
      value: v.value 
    }))

    const result = await batchImportSecrets(
      selectedEnvironment.id,
      secretsToImport,
      duplicateStrategy
    )

    if (result.success) {
      // Refresh secrets list
      refetchSecrets()
    }

    return result
  }

  // Export handler
  const handleExportSecrets = async () => {
    if (!selectedEnvironment || secrets.length === 0) return

    setExportLoading(true)
    try {
      const envContent = await exportSecrets(secrets, {
        environmentName: selectedEnvironment.name
      })
      
      const filename = generateExportFilename(
        project?.name || 'project',
        selectedEnvironment.name
      )
      
      downloadFile(envContent, filename)
    } catch (error) {
      console.error('Export failed:', error)
      // You might want to show an error message to the user
    }
    setExportLoading(false)
  }

  // Delete handler
  const handleDeleteProject = async () => {
    if (!project) return
    
    setDeleteLoading(true)
    const { error } = await deleteProject(project.id)
    
    if (error) {
      console.error('Delete failed:', error)
      // You might want to show an error message to the user
      alert(`Failed to delete project: ${error.message}`)
    } else {
      // Redirect to dashboard after successful deletion
      router.push('/dashboard')
    }
    
    setDeleteLoading(false)
    setShowDeleteConfirm(false)
  }

  useEffect(() => {
    async function fetchProject() {
      if (!projectId) return

      setLoading(true)
      const { data, error } = await getProject(projectId)
      
      if (error) {
        setError(error.message)
      } else if (data) {
        setProject(data)
      }
      
      setLoading(false)
    }

    fetchProject()
  }, [projectId])

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <div className="h-8 bg-gray-300 rounded w-64 mb-2 animate-pulse"></div>
          <div className="h-4 bg-gray-200 rounded w-48 animate-pulse"></div>
        </div>
        <div className="bg-white border-2 border-gray-300 p-6 animate-pulse">
          <div className="space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !project) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="bg-red-50 border-2 border-red-500 p-6 text-center">
          <h2 className="text-xl font-bold mb-2">Project Not Found</h2>
          <p className="text-red-700 mb-4">{error || 'The project you are looking for does not exist.'}</p>
          <Link href="/dashboard">
            <button className="btn-brutal">
              Back to Dashboard
            </button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-2 text-sm text-gray-600 mb-4">
          <Link href="/dashboard" className="hover:underline">
            Dashboard
          </Link>
          <span>/</span>
          <span>{project.name}</span>
        </div>
        <h1 className="text-4xl font-bold mb-2">{project.name}</h1>
        <div className="flex gap-4 text-gray-600">
          {project.team && (
            <p>
              <span className="font-medium">Team:</span> {project.team.name}
            </p>
          )}
          <p>
            <span className="font-medium">Created:</span> {new Date(project.created_at).toLocaleDateString()}
          </p>
        </div>
      </div>

      {/* Environments section */}
      <div className="mb-8">
        <div className="border-b-2 border-black mb-6">
          <div className="flex items-center gap-4 overflow-x-auto">
            {/* Environment tabs */}
            {environments.map((env) => (
              <button
                key={env.id}
                onClick={() => selectEnvironment(env.id)}
                className={`
                  px-4 py-3 font-medium transition-all
                  ${selectedEnvironment?.id === env.id
                    ? 'bg-yellow-400 border-2 border-black border-b-0 -mb-0.5 font-bold'
                    : 'border-b-2 border-transparent hover:border-gray-400'
                  }
                `}
              >
                {env.name}
              </button>
            ))}
            
            {/* Add environment button */}
            <PermissionGuard permission={PERMISSIONS.ENVIRONMENT_CREATE}>
              <button
                onClick={() => setIsCreateEnvModalOpen(true)}
                className="px-4 py-3 font-medium border-b-2 border-transparent hover:border-gray-400 flex items-center gap-2 text-gray-600 hover:text-black transition-colors"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                Add Environment
              </button>
            </PermissionGuard>
          </div>
        </div>

        {/* Environment content area */}
        {envLoading ? (
          <div className="bg-white border-2 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] p-6">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        ) : selectedEnvironment ? (
          <div className="bg-white border-2 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold">
                {selectedEnvironment.name} Environment
              </h3>
              <span className="text-sm text-gray-600">
                Created {new Date(selectedEnvironment.created_at).toLocaleDateString()}
              </span>
            </div>
            
            {/* Secrets section */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-md font-bold">Secrets</h4>
                <div className="flex gap-2">
                  <PermissionGuard permission={PERMISSIONS.SECRET_IMPORT}>
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => setIsImportModalOpen(true)}
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                      </svg>
                      Import
                    </Button>
                  </PermissionGuard>
                  <PermissionGuard permission={PERMISSIONS.SECRET_EXPORT}>
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={handleExportSecrets}
                      loading={exportLoading}
                      disabled={secrets.length === 0}
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                      </svg>
                      Export
                    </Button>
                  </PermissionGuard>
                  <PermissionGuard permission={PERMISSIONS.SECRET_CREATE}>
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={() => setIsAddSecretModalOpen(true)}
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                      </svg>
                      Add Secret
                    </Button>
                  </PermissionGuard>
                </div>
              </div>
              
              <SecretsList
                secrets={secrets}
                loading={secretsLoading}
                onUpdateSecret={updateSecret}
                onDeleteSecret={deleteSecret}
                onDecryptSecret={decryptSecret}
                getDecryptedValue={getDecryptedValue}
                clearDecryptedValue={clearDecryptedValue}
              />
              
              {secretsError && (
                <div className="mt-4 p-3 bg-red-50 border-2 border-red-500 text-red-700 text-sm">
                  {secretsError.message}
                </div>
              )}
            </div>
          </div>
        ) : environments.length === 0 ? (
          <div className="bg-white border-2 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] p-6">
            <div className="empty-state-card">
              <div className="text-center">
                <div className="mb-4">
                  <svg className="w-16 h-16 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                  </svg>
                </div>
                <h3 className="text-lg font-bold mb-2">No environments yet</h3>
                <p className="text-gray-600 mb-4">
                  Create your first environment to start managing secrets
                </p>
                <PermissionGuard permission={PERMISSIONS.ENVIRONMENT_CREATE}>
                  <button 
                    onClick={() => setIsCreateEnvModalOpen(true)}
                    className="btn-brutal-primary"
                  >
                    Create Environment
                  </button>
                </PermissionGuard>
              </div>
            </div>
          </div>
        ) : null}
      </div>

      {/* Project actions */}
      <PermissionGuard permission={PERMISSIONS.PROJECT_DELETE}>
        <div className="flex gap-4">
          <button 
            className="btn-brutal" 
            onClick={() => setShowProjectSettings(true)}
          >
            <svg className="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            Project Settings
          </button>
          <button 
            className="btn-brutal text-red-600 border-red-600" 
            onClick={() => setShowDeleteConfirm(true)}
            disabled={deleteLoading}
          >
            <svg className="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            {deleteLoading ? 'Deleting...' : 'Delete Project'}
          </button>
        </div>
      </PermissionGuard>

      {/* Create Environment Modal */}
      <CreateEnvironmentModal
        isOpen={isCreateEnvModalOpen}
        onClose={() => setIsCreateEnvModalOpen(false)}
        onCreateEnvironment={createEnvironment}
        projectId={projectId}
      />

      {/* Add Secret Modal */}
      <AddSecretModal
        isOpen={isAddSecretModalOpen}
        onClose={() => setIsAddSecretModalOpen(false)}
        onCreateSecret={createSecret}
      />

      {/* Import Secrets Modal */}
      <ImportSecretsModal
        isOpen={isImportModalOpen}
        onClose={() => setIsImportModalOpen(false)}
        onImport={handleImportSecrets}
        existingKeys={secrets.map(s => s.key)}
      />

      {/* Project Settings Modal */}
      {project && (
        <ProjectSettingsModal
          isOpen={showProjectSettings}
          onClose={() => setShowProjectSettings(false)}
          project={project}
        />
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white border-2 border-black p-6 max-w-md w-full shadow-[8px_8px_0px_0px_rgba(0,0,0,1)]">
            <h3 className="text-xl font-bold mb-4">Delete Project</h3>
            <p className="mb-6">
              Are you sure you want to delete &quot;{project?.name}&quot;? This action cannot be undone and will delete all environments and secrets.
            </p>
            <div className="flex gap-4">
              <button
                className="px-4 py-2 bg-red-600 text-white font-bold border-2 border-black hover:shadow-[2px_2px_0px_0px_rgba(0,0,0,1)] transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                onClick={handleDeleteProject}
                disabled={deleteLoading}
              >
                {deleteLoading ? 'Deleting...' : 'Yes, Delete Project'}
              </button>
              <Button
                variant="secondary"
                onClick={() => setShowDeleteConfirm(false)}
                disabled={deleteLoading}
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}