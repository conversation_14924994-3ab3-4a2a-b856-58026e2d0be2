'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/atoms/Button'
import { CreateProjectModal } from '@/components/molecules/CreateProjectModal'
import { getProjects, createProject } from '@/lib/projects/api'
import { Project } from '@/lib/projects/types'
import { useTeamContext } from '@/contexts/TeamContext'

export default function ProjectsPage() {
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const { currentTeam } = useTeamContext()

  useEffect(() => {
    async function fetchProjects() {
      setLoading(true)
      const { data, error } = await getProjects(currentTeam?.id)
      
      if (error) {
        setError(error.message)
      } else if (data) {
        setProjects(data)
      }
      
      setLoading(false)
    }

    if (currentTeam) {
      fetchProjects()
    }
  }, [currentTeam])

  const handleCreateProject = () => {
    // Refresh the projects list
    getProjects(currentTeam?.id).then(({ data }) => {
      if (data) setProjects(data)
    })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="inline-block w-16 h-16 border-4 border-black border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 font-bold">Loading projects...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-100 border-2 border-red-500 p-4">
        <p className="text-red-700">Error loading projects: {error}</p>
      </div>
    )
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Projects</h1>
        <p className="text-gray-600">Manage your projects and their environments.</p>
      </div>

      {/* Current Team Info */}
      <div className="mb-6 bg-gray-100 border-2 border-black p-4 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">Current Team</p>
            <p className="text-lg font-bold">{currentTeam?.name}</p>
          </div>
          {currentTeam?.role === 'admin' && (
            <Button onClick={() => setShowCreateModal(true)}>
              Create New Project
            </Button>
          )}
        </div>
      </div>

      {/* Projects Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {projects.map((project) => (
          <Link
            key={project.id}
            href={`/projects/${project.id}`}
            className="block bg-white border-2 border-black p-6 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transition-all"
          >
            <h3 className="text-lg font-bold mb-2">{project.name}</h3>
            <div className="text-sm text-gray-600">
              <p>Created {new Date(project.created_at).toLocaleDateString()}</p>
            </div>
          </Link>
        ))}

        {/* Create New Project Card (for admins) */}
        {currentTeam?.role === 'admin' && (
          <button
            onClick={() => setShowCreateModal(true)}
            className="bg-gray-100 border-2 border-dashed border-gray-400 p-6 hover:border-black hover:bg-gray-200 transition-all flex flex-col items-center justify-center text-gray-600 hover:text-black"
          >
            <svg className="w-12 h-12 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <p className="font-medium">Create New Project</p>
          </button>
        )}
      </div>

      {/* Empty State */}
      {projects.length === 0 && (
        <div className="bg-gray-100 border-2 border-gray-300 p-8 text-center">
          <svg className="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
          </svg>
          <p className="text-gray-600 mb-4">
            No projects yet. {currentTeam?.role === 'admin' ? 'Create your first project to get started.' : 'Ask your team admin to create a project.'}
          </p>
          {currentTeam?.role === 'admin' && (
            <Button onClick={() => setShowCreateModal(true)}>
              Create First Project
            </Button>
          )}
        </div>
      )}

      {/* Non-admin message */}
      {currentTeam?.role !== 'admin' && projects.length > 0 && (
        <div className="mt-6 bg-blue-50 border-2 border-blue-300 p-4">
          <p className="text-blue-700 text-sm">
            <strong>Note:</strong> Only team admins can create new projects. Contact your team admin if you need a new project.
          </p>
        </div>
      )}

      {/* Create Project Modal */}
      <CreateProjectModal 
        isOpen={showCreateModal}
        onClose={() => {
          setShowCreateModal(false)
          handleCreateProject()
        }}
        onCreateProject={async (name: string, teamId?: string) => {
          const { error } = await createProject(name, teamId || currentTeam?.id)
          if (error) {
            return { success: false, error }
          }
          return { success: true }
        }}
      />
    </div>
  )
}