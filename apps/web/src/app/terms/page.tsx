'use client'

import Link from 'next/link'
import { Button } from '@/components/atoms/Button'
import { useState } from 'react'

export default function TermsPage() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  
  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="border-b-2 border-black sticky top-0 bg-white z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="flex items-center">
                <span className="text-2xl font-bold">EzEnv</span>
              </Link>
            </div>
            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center gap-4">
              <Link href="/docs" className="font-medium hover:underline">
                Documentation
              </Link>
              <Link href="/pricing" className="font-medium hover:underline">
                Pricing
              </Link>
              <Link href="/login">
                <Button variant="secondary">Sign In</Button>
              </Link>
              <Link href="/signup">
                <Button>Get Started</Button>
              </Link>
            </div>
            
            {/* Mobile Menu Button */}
            <button
              className="md:hidden p-2 hover:bg-gray-100 rounded-md"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {mobileMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>
        
        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div className="md:hidden border-t-2 border-black bg-white">
            <div className="px-4 py-4 space-y-3">
              <Link href="/docs" className="block font-medium hover:underline">
                Documentation
              </Link>
              <Link href="/pricing" className="block font-medium hover:underline">
                Pricing
              </Link>
              <Link href="/login" className="block">
                <Button variant="secondary" className="w-full">Sign In</Button>
              </Link>
              <Link href="/signup" className="block">
                <Button className="w-full">Get Started</Button>
              </Link>
            </div>
          </div>
        )}
      </nav>

      <div className="max-w-4xl mx-auto px-4 py-8 sm:py-16">
        <h1 className="text-3xl sm:text-4xl font-bold mb-6 sm:mb-8">Terms of Service</h1>
        
        <div className="prose prose-lg max-w-none space-y-8">
          <section>
            <p className="text-gray-600 mb-8">Last updated: January 21, 2025</p>
            
            <p className="mb-6">
              Welcome to EzEnv. These Terms of Service (&quot;Terms&quot;) govern your use of the EzEnv
              platform and services (&quot;Service&quot;) operated by EzEnv (&quot;us&quot;, &quot;we&quot;, or &quot;our&quot;).
            </p>
            
            <p className="mb-6">
              By accessing or using our Service, you agree to be bound by these Terms. If you 
              disagree with any part of these terms, then you may not access the Service.
            </p>
          </section>

          <section>
            <h2 className="text-xl sm:text-2xl font-bold mb-4">1. Acceptable Use</h2>
            <p className="mb-4">You agree to use the Service only for lawful purposes and in accordance with these Terms. You agree not to:</p>
            <ul className="list-disc list-inside space-y-2 mb-6">
              <li>Use the Service in any way that violates any applicable federal, state, local, or international law or regulation</li>
              <li>Transmit any material that is defamatory, offensive, or otherwise objectionable</li>
              <li>Attempt to gain unauthorized access to any portion of the Service or any other systems or networks</li>
              <li>Use the Service to store or transmit malicious code</li>
              <li>Interfere with or disrupt the Service or servers or networks connected to the Service</li>
            </ul>
          </section>

          <section>
            <h2 className="text-xl sm:text-2xl font-bold mb-4">2. User Accounts</h2>
            <p className="mb-4">
              When you create an account with us, you must provide information that is accurate, 
              complete, and current at all times. You are responsible for safeguarding the password 
              and for all activities that occur under your account.
            </p>
            <p className="mb-6">
              You agree to notify us immediately of any unauthorized access to or use of your 
              account or any other breach of security.
            </p>
          </section>

          <section>
            <h2 className="text-xl sm:text-2xl font-bold mb-4">3. Intellectual Property</h2>
            <p className="mb-6">
              The Service and its original content, features, and functionality are and will remain 
              the exclusive property of EzEnv and its licensors. The Service is protected by 
              copyright, trademark, and other laws. Our trademarks and trade dress may not be used 
              in connection with any product or service without our prior written consent.
            </p>
          </section>

          <section>
            <h2 className="text-xl sm:text-2xl font-bold mb-4">4. Privacy</h2>
            <p className="mb-6">
              Your use of the Service is also governed by our Privacy Policy. Please review our 
              <Link href="/privacy" className="text-blue-600 underline ml-1">Privacy Policy</Link>, 
              which also governs the Service and informs users of our data collection practices.
            </p>
          </section>

          <section>
            <h2 className="text-xl sm:text-2xl font-bold mb-4">5. Data Security</h2>
            <p className="mb-4">
              We implement industry-standard security measures to protect your data, including:
            </p>
            <ul className="list-disc list-inside space-y-2 mb-6">
              <li>AES-256 encryption for all stored secrets</li>
              <li>TLS 1.3 for all data in transit</li>
              <li>Regular security audits and updates</li>
              <li>Role-based access control</li>
            </ul>
            <p className="mb-6">
              However, no method of transmission over the Internet or electronic storage is 100% 
              secure, and we cannot guarantee absolute security.
            </p>
          </section>

          <section>
            <h2 className="text-xl sm:text-2xl font-bold mb-4">6. Service Level Agreement</h2>
            <p className="mb-4">For paid plans, we provide the following service level commitments:</p>
            <ul className="list-disc list-inside space-y-2 mb-6">
              <li>Team Plan: 99.9% uptime SLA</li>
              <li>Enterprise Plan: 99.99% uptime SLA</li>
            </ul>
            <p className="mb-6">
              Service credits may be available for downtime exceeding these commitments. Please 
              contact support for more information.
            </p>
          </section>

          <section>
            <h2 className="text-xl sm:text-2xl font-bold mb-4">7. Payment Terms</h2>
            <p className="mb-4">
              Paid subscriptions will be billed in advance on a monthly or annual basis. You agree 
              to pay all charges incurred in connection with your subscription.
            </p>
            <p className="mb-6">
              Subscription fees are non-refundable except as required by law or as explicitly 
              stated in these Terms.
            </p>
          </section>

          <section>
            <h2 className="text-xl sm:text-2xl font-bold mb-4">8. Termination</h2>
            <p className="mb-6">
              We may terminate or suspend your account immediately, without prior notice or 
              liability, for any reason whatsoever, including without limitation if you breach 
              the Terms. Upon termination, your right to use the Service will cease immediately.
            </p>
          </section>

          <section>
            <h2 className="text-xl sm:text-2xl font-bold mb-4">9. Disclaimer</h2>
            <p className="mb-6">
              THE SERVICE IS PROVIDED ON AN &quot;AS IS&quot; AND &quot;AS AVAILABLE&quot; BASIS. THE SERVICE IS
              PROVIDED WITHOUT WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING, BUT 
              NOT LIMITED TO, IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR 
              PURPOSE, NON-INFRINGEMENT, OR COURSE OF PERFORMANCE.
            </p>
          </section>

          <section>
            <h2 className="text-xl sm:text-2xl font-bold mb-4">10. Limitation of Liability</h2>
            <p className="mb-6">
              IN NO EVENT SHALL EZENV, NOR ITS DIRECTORS, EMPLOYEES, PARTNERS, AGENTS, SUPPLIERS, 
              OR AFFILIATES, BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR 
              PUNITIVE DAMAGES, INCLUDING WITHOUT LIMITATION, LOSS OF PROFITS, DATA, USE, GOODWILL, 
              OR OTHER INTANGIBLE LOSSES, RESULTING FROM YOUR USE OF THE SERVICE.
            </p>
          </section>

          <section>
            <h2 className="text-xl sm:text-2xl font-bold mb-4">11. Changes to Terms</h2>
            <p className="mb-6">
              We reserve the right to modify or replace these Terms at any time. If a revision is 
              material, we will try to provide at least 30 days notice prior to any new terms 
              taking effect.
            </p>
          </section>

          <section>
            <h2 className="text-xl sm:text-2xl font-bold mb-4">12. Contact Information</h2>
            <p className="mb-6">
              If you have any questions about these Terms, please contact us at:
            </p>
            <div className="bg-gray-100 border-2 border-black p-4 sm:p-6 shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
              <p className="font-medium">EzEnv Support</p>
              <p>Email: <a href="mailto:<EMAIL>" className="text-blue-600 underline"><EMAIL></a></p>
            </div>
          </section>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-4 mt-8 sm:mt-16">
        <div className="max-w-7xl mx-auto text-center">
          <p>&copy; 2025 EzEnv. All rights reserved.</p>
        </div>
      </footer>
    </div>
  )
}