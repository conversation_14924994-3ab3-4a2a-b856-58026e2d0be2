'use client'

import { useEffect, useState, useRef, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/atoms/Button'
import { acceptInvitation, getInvitationByToken } from '@/lib/invitations/api'
import { createClient } from '@/lib/supabase/client'
import type { TeamInvitation } from '@/lib/invitations/types'

function AcceptInvitationContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const token = searchParams.get('token')
  
  const [invitation, setInvitation] = useState<TeamInvitation | null>(null)
  const [loading, setLoading] = useState(true)
  const [accepting, setAccepting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const acceptanceStartedRef = useRef(false)

  useEffect(() => {
    checkAuthAndInvitation()
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [token])

  async function checkAuthAndInvitation() {
    if (!token) {
      setError('Invalid invitation link')
      setLoading(false)
      return
    }

    const supabase = createClient()
    
    // Check if user is authenticated
    const { data: { user } } = await supabase.auth.getUser()
    setIsAuthenticated(!!user)

    // Get invitation details
    try {
      const inv = await getInvitationByToken(token)
      if (!inv) {
        setError('Invalid or expired invitation')
      } else if (inv.accepted_at) {
        setError('This invitation has already been accepted')
      } else if (new Date(inv.expires_at) < new Date()) {
        setError('This invitation has expired')
      } else {
        setInvitation(inv)
        
        // If authenticated and email matches, auto-accept
        // Use ref to prevent race conditions
        if (user && user.email === inv.email && !acceptanceStartedRef.current) {
          acceptanceStartedRef.current = true
          handleAccept()
        }
      }
    } catch {
      setError('Failed to load invitation')
    } finally {
      setLoading(false)
    }
  }

  async function handleAccept() {
    if (!token || accepting) return

    try {
      setAccepting(true)
      setError(null)
      
      const result = await acceptInvitation(token)
      
      // Redirect to the specific team's page
      if (result && result.team_id) {
        router.push(`/teams/${result.team_id}`)
      } else {
        router.push(`/teams`)
      }
    } catch (err) {
      setError((err as Error).message || 'Failed to accept invitation')
      setAccepting(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div 
            className="inline-block w-16 h-16 border-4 border-black border-t-transparent rounded-full animate-spin"
            role="status"
            aria-label="Loading invitation"
          ></div>
          <p className="mt-4 font-bold">Loading invitation...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center p-8">
        <div className="max-w-md w-full">
          <div className="bg-red-100 border-2 border-red-500 p-6 text-center">
            <h1 className="text-2xl font-bold mb-2">Invalid Invitation</h1>
            <p className="text-red-700 mb-4">{error}</p>
            <Link href="/login">
              <Button variant="secondary">Go to Login</Button>
            </Link>
          </div>
        </div>
      </div>
    )
  }

  if (!invitation) {
    return null
  }

  // If not authenticated, redirect to login/signup
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center p-8">
        <div className="max-w-md w-full">
          <div className="bg-white border-2 border-black p-8 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)]">
            <h1 className="text-3xl font-bold mb-4">Team Invitation</h1>
            <p className="text-gray-600 mb-6">
              You&apos;ve been invited to join a team on EzEnv as a {invitation.role}.
            </p>
            <p className="mb-6">
              Please sign in or create an account with <strong>{invitation.email}</strong> to accept this invitation.
            </p>
            <div className="space-y-4">
              <Link href={`/login?redirect=/invitations/accept?token=${token}`}>
                <Button variant="primary" className="w-full">
                  Sign In
                </Button>
              </Link>
              <Link href={`/signup?redirect=/invitations/accept?token=${token}`}>
                <Button variant="secondary" className="w-full">
                  Create Account
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Authenticated - show accept button
  return (
    <div className="min-h-screen flex items-center justify-center p-8">
      <div className="max-w-md w-full">
        <div className="bg-white border-2 border-black p-8 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)]">
          <h1 className="text-3xl font-bold mb-4">Accept Team Invitation</h1>
          <p className="text-gray-600 mb-6">
            You&apos;ve been invited to join a team on EzEnv as a {invitation.role}.
          </p>
          <div className="space-y-4">
            <Button
              variant="primary"
              className="w-full"
              onClick={handleAccept}
              disabled={accepting}
            >
              {accepting ? 'Accepting...' : 'Accept Invitation'}
            </Button>
            <Link href="/dashboard">
              <Button variant="secondary" className="w-full">
                Cancel
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function AcceptInvitationPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div 
            className="inline-block w-16 h-16 border-4 border-black border-t-transparent rounded-full animate-spin"
            role="status"
            aria-label="Loading"
          ></div>
          <p className="mt-4 font-bold">Loading...</p>
        </div>
      </div>
    }>
      <AcceptInvitationContent />
    </Suspense>
  )
}