'use client'

import { useState, Suspense } from 'react'
import Link from 'next/link'
import { useRouter, useSearchParams } from 'next/navigation'
import { Input } from '@/components/atoms/Input'
import { Button } from '@/components/atoms/Button'
import { signUp } from '@/lib/supabase/auth'

interface FormData {
  email: string
  password: string
}

interface FormErrors {
  email?: string
  password?: string
}

function SignUpContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const redirect = searchParams.get('redirect')
  const [formData, setFormData] = useState<FormData>({ email: '', password: '' })
  const [errors, setErrors] = useState<FormErrors>({})
  const [loading, setLoading] = useState(false)
  const [successMessage, setSuccessMessage] = useState('')

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!formData.email) {
      newErrors.email = 'Email is required'
    } else if (!emailRegex.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required'
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setLoading(true)
    setErrors({})

    try {
      const { success, error } = await signUp(formData.email, formData.password)

      if (success) {
        setSuccessMessage('Account created successfully! Check your email to verify your account.')
        // Redirect to login after 3 seconds
        setTimeout(() => {
          router.push(redirect ? `/login?redirect=${encodeURIComponent(redirect)}` : '/login')
        }, 3000)
      } else if (error) {
        setErrors({ email: error.message })
      }
    } catch {
      setErrors({ email: 'An unexpected error occurred. Please try again.' })
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    // Clear error for the field being edited
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [name]: undefined }))
    }
  }

  if (successMessage) {
    return (
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-4">Success!</h2>
        <p className="mb-4 text-green-600">{successMessage}</p>
        <p className="text-sm text-gray-600">Redirecting to login...</p>
      </div>
    )
  }

  return (
    <>
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold mb-2">Create your account</h1>
        <p className="text-gray-600">
          Already have an account?{' '}
          <Link 
            href={redirect ? `/login?redirect=${encodeURIComponent(redirect)}` : '/login'} 
            className="font-bold underline hover:no-underline"
          >
            Sign in
          </Link>
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <Input
          id="email"
          name="email"
          type="email"
          label="Email address"
          placeholder="<EMAIL>"
          value={formData.email}
          onChange={handleChange}
          error={errors.email}
          required
          autoComplete="email"
        />

        <Input
          id="password"
          name="password"
          type="password"
          label="Password"
          placeholder="At least 6 characters"
          value={formData.password}
          onChange={handleChange}
          error={errors.password}
          required
          autoComplete="new-password"
        />

        <Button
          type="submit"
          variant="primary"
          loading={loading}
          className="w-full"
        >
          Create account
        </Button>
      </form>
    </>
  )
}

export default function SignUpPage() {
  return (
    <Suspense fallback={
      <div className="text-center">
        <div className="inline-block w-16 h-16 border-4 border-black border-t-transparent rounded-full animate-spin"></div>
        <p className="mt-4 font-bold">Loading...</p>
      </div>
    }>
      <SignUpContent />
    </Suspense>
  )
}