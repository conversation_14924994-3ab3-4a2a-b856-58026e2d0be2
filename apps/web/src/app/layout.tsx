import type { Metadata } from 'next'
import { IBM_Plex_Mono } from 'next/font/google'
import { AuthProvider } from '@/contexts/AuthContext'
import './globals.css'

const ibmPlexMono = IBM_Plex_Mono({ 
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
})

export const metadata: Metadata = {
  title: 'EzEnv - Secure Environment Variable Management for Development Teams',
  description: 'Stop sharing .env files via Slack or email. EzEnv provides a centralized, secure platform for managing application secrets and environment configurations throughout your development lifecycle.',
  keywords: ['environment variables', 'secrets management', 'development tools', 'security', 'devops', 'configuration management'],
  authors: [{ name: 'EzEnv Team' }],
  creator: 'EzEnv',
  publisher: 'EzEnv',
  metadataBase: new URL('https://ezenv.dev'),
  // Open Graph tags for Facebook, LinkedIn, and general social sharing
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://ezenv.dev',
    siteName: 'EzEnv',
    title: 'EzEnv - Secure Environment Variable Management for Development Teams',
    description: 'Stop sharing .env files via Slack or email. EzEnv provides a centralized, secure platform for managing application secrets and environment configurations.',
    images: [
      {
        url: '/og_image.png',
        width: 1200,
        height: 630,
        alt: 'EzEnv - Secure Environment Variable Management',
        type: 'image/png',
      },
    ],
  },

  // Twitter/X Card tags
  twitter: {
    card: 'summary_large_image',
    site: '@ezenv_dev', // Replace with your actual Twitter handle
    creator: '@ezenv_dev', // Replace with your actual Twitter handle
    title: 'EzEnv - Secure Environment Variable Management for Development Teams',
    description: 'Stop sharing .env files via Slack or email. Centralized, secure platform for managing application secrets and environment configurations.',
    images: ['/og_image.png'],
  },

  // Additional meta tags
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },

  // Verification tags (add your actual verification codes)
  verification: {
    // google: 'your-google-verification-code',
    // yandex: 'your-yandex-verification-code',
    // yahoo: 'your-yahoo-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={`${ibmPlexMono.className} antialiased`}>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  )
}