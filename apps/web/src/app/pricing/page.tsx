'use client'

import Link from 'next/link'
import { Button } from '@/components/atoms/Button'
import { useState } from 'react'

export default function PricingPage() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  
  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="border-b-2 border-black sticky top-0 bg-white z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="flex items-center">
                <span className="text-2xl font-bold">EzEnv</span>
              </Link>
            </div>
            
            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center gap-4">
              <Link href="/docs" className="font-medium hover:underline">
                Documentation
              </Link>
              <Link href="/pricing" className="font-medium hover:underline">
                Pricing
              </Link>
              <Link href="/login">
                <Button variant="secondary">Sign In</Button>
              </Link>
              <Link href="/signup">
                <Button>Get Started</Button>
              </Link>
            </div>
            
            {/* Mobile Menu Button */}
            <button
              className="md:hidden p-2 hover:bg-gray-100 rounded-md"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {mobileMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>
        
        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div className="md:hidden border-t-2 border-black bg-white">
            <div className="px-4 py-4 space-y-3">
              <Link href="/docs" className="block font-medium hover:underline">
                Documentation
              </Link>
              <Link href="/pricing" className="block font-medium hover:underline">
                Pricing
              </Link>
              <Link href="/login" className="block">
                <Button variant="secondary" className="w-full">Sign In</Button>
              </Link>
              <Link href="/signup" className="block">
                <Button className="w-full">Get Started</Button>
              </Link>
            </div>
          </div>
        )}
      </nav>

      {/* Hero Section */}
      <section className="py-12 sm:py-16 px-4">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6">
            Simple, Transparent Pricing
          </h1>
          <p className="text-lg sm:text-xl text-gray-700 max-w-3xl mx-auto">
            Start free and scale as you grow. No hidden fees, no surprises.
          </p>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="py-8 sm:py-16 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-3 gap-6 md:gap-8 max-w-6xl mx-auto">
            
            {/* Free Tier */}
            <div className="bg-white border-2 border-black p-6 sm:p-8 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] relative">
              <div className="mb-8">
                <h3 className="text-xl sm:text-2xl font-bold mb-2">Free</h3>
                <p className="text-gray-600 mb-4">Perfect for side projects</p>
                <div className="text-3xl sm:text-4xl font-bold mb-2">
                  $0
                  <span className="text-base sm:text-lg font-normal text-gray-600">/month</span>
                </div>
              </div>
              
              <ul className="space-y-4 mb-8">
                <li className="flex items-start">
                  <svg className="w-6 h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Up to 2 projects</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-6 h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Up to 5 team members</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-6 h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>All core features</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-6 h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Community support</span>
                </li>
              </ul>
              
              <Link href="/signup">
                <Button className="w-full">
                  Start Free
                </Button>
              </Link>
            </div>

            {/* Team Tier */}
            <div className="bg-yellow-50 border-2 border-black p-6 sm:p-8 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] relative lg:transform lg:scale-105">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-yellow-400 border-2 border-black px-4 py-1 text-sm font-bold">
                  MOST POPULAR
                </span>
              </div>
              
              <div className="mb-8">
                <h3 className="text-xl sm:text-2xl font-bold mb-2">Team</h3>
                <p className="text-gray-600 mb-4">For growing teams</p>
                <div className="text-3xl sm:text-4xl font-bold mb-2">
                  $4
                  <span className="text-base sm:text-lg font-normal text-gray-600">/member/month</span>
                </div>
              </div>
              
              <ul className="space-y-4 mb-8">
                <li className="flex items-start">
                  <svg className="w-6 h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span className="font-medium">Unlimited projects</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-6 h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span className="font-medium">Unlimited environments</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-6 h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Advanced permissions</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-6 h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Priority email support</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-6 h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>99.9% uptime SLA</span>
                </li>
              </ul>
              
              <Link href="/signup">
                <Button className="w-full bg-yellow-400 text-black border-2 border-black hover:shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
                  Start 14-Day Trial
                </Button>
              </Link>
            </div>

            {/* Enterprise Tier */}
            <div className="bg-white border-2 border-black p-6 sm:p-8 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] relative">
              <div className="mb-8">
                <h3 className="text-xl sm:text-2xl font-bold mb-2">Enterprise</h3>
                <p className="text-gray-600 mb-4">For large organizations</p>
                <div className="text-3xl sm:text-4xl font-bold mb-2">
                  Custom
                  <span className="text-base sm:text-lg font-normal text-gray-600 block">Contact for pricing</span>
                </div>
              </div>
              
              <ul className="space-y-4 mb-8">
                <li className="flex items-start">
                  <svg className="w-6 h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Everything in Team</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-6 h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>SSO/SAML integration</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-6 h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Audit logs</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-6 h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>24/7 phone support</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-6 h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Custom integrations</span>
                </li>
              </ul>
              
              <Link href="/contact">
                <Button variant="secondary" className="w-full">
                  Contact Sales
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Feature Comparison */}
      <section className="py-12 sm:py-16 px-4 bg-gray-100 border-y-2 border-black">
        <div className="max-w-5xl mx-auto">
          <h2 className="text-2xl sm:text-3xl font-bold mb-8 sm:mb-12 text-center">Compare Plans</h2>
          
          <div className="overflow-x-auto -mx-4 px-4">
            <table className="w-full border-2 border-black bg-white min-w-[600px]">
              <thead>
                <tr className="border-b-2 border-black">
                  <th className="text-left p-3 sm:p-4 font-bold text-sm sm:text-base">Features</th>
                  <th className="text-center p-3 sm:p-4 font-bold text-sm sm:text-base">Free</th>
                  <th className="text-center p-3 sm:p-4 font-bold bg-yellow-50 text-sm sm:text-base">Team</th>
                  <th className="text-center p-3 sm:p-4 font-bold text-sm sm:text-base">Enterprise</th>
                </tr>
              </thead>
              <tbody className="text-sm sm:text-base">
                <tr className="border-b border-gray-300">
                  <td className="p-3 sm:p-4">Projects</td>
                  <td className="text-center p-3 sm:p-4">2</td>
                  <td className="text-center p-3 sm:p-4 bg-yellow-50">Unlimited</td>
                  <td className="text-center p-3 sm:p-4">Unlimited</td>
                </tr>
                <tr className="border-b border-gray-300">
                  <td className="p-3 sm:p-4">Team Members</td>
                  <td className="text-center p-3 sm:p-4">5</td>
                  <td className="text-center p-3 sm:p-4 bg-yellow-50">Unlimited</td>
                  <td className="text-center p-3 sm:p-4">Unlimited</td>
                </tr>
                <tr className="border-b border-gray-300">
                  <td className="p-3 sm:p-4">Environments per Project</td>
                  <td className="text-center p-3 sm:p-4">3</td>
                  <td className="text-center p-3 sm:p-4 bg-yellow-50">Unlimited</td>
                  <td className="text-center p-3 sm:p-4">Unlimited</td>
                </tr>
                <tr className="border-b border-gray-300">
                  <td className="p-3 sm:p-4">API Access</td>
                  <td className="text-center p-3 sm:p-4">✓</td>
                  <td className="text-center p-3 sm:p-4 bg-yellow-50">✓</td>
                  <td className="text-center p-3 sm:p-4">✓</td>
                </tr>
                <tr className="border-b border-gray-300">
                  <td className="p-3 sm:p-4">Role-Based Access Control</td>
                  <td className="text-center p-3 sm:p-4">✓</td>
                  <td className="text-center p-3 sm:p-4 bg-yellow-50">✓</td>
                  <td className="text-center p-3 sm:p-4">✓</td>
                </tr>
                <tr className="border-b border-gray-300">
                  <td className="p-3 sm:p-4">Support</td>
                  <td className="text-center p-3 sm:p-4">Community</td>
                  <td className="text-center p-3 sm:p-4 bg-yellow-50">Email</td>
                  <td className="text-center p-3 sm:p-4">24/7 Phone</td>
                </tr>
                <tr className="border-b border-gray-300">
                  <td className="p-3 sm:p-4">SLA</td>
                  <td className="text-center p-3 sm:p-4">-</td>
                  <td className="text-center p-3 sm:p-4 bg-yellow-50">99.9%</td>
                  <td className="text-center p-3 sm:p-4">99.99%</td>
                </tr>
                <tr className="border-b border-gray-300">
                  <td className="p-3 sm:p-4">SSO/SAML</td>
                  <td className="text-center p-3 sm:p-4">-</td>
                  <td className="text-center p-3 sm:p-4 bg-yellow-50">-</td>
                  <td className="text-center p-3 sm:p-4">✓</td>
                </tr>
                <tr className="border-b border-gray-300">
                  <td className="p-3 sm:p-4">Audit Logs</td>
                  <td className="text-center p-3 sm:p-4">-</td>
                  <td className="text-center p-3 sm:p-4 bg-yellow-50">-</td>
                  <td className="text-center p-3 sm:p-4">✓</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-12 sm:py-16 px-4">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-2xl sm:text-3xl font-bold mb-8 sm:mb-12 text-center">Frequently Asked Questions</h2>
          
          <div className="space-y-6">
            <div className="border-2 border-black p-4 sm:p-6 shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
              <h3 className="text-lg sm:text-xl font-bold mb-2">Can I change plans anytime?</h3>
              <p className="text-gray-700">
                Yes! You can upgrade or downgrade your plan at any time. Changes take effect immediately,
                and we&apos;ll prorate any charges or credits.
              </p>
            </div>
            
            <div className="border-2 border-black p-4 sm:p-6 shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
              <h3 className="text-lg sm:text-xl font-bold mb-2">What payment methods do you accept?</h3>
              <p className="text-gray-700">
                We accept all major credit cards (Visa, MasterCard, American Express) and ACH transfers
                for enterprise customers.
              </p>
            </div>
            
            <div className="border-2 border-black p-4 sm:p-6 shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
              <h3 className="text-lg sm:text-xl font-bold mb-2">Do you offer discounts for nonprofits?</h3>
              <p className="text-gray-700">
                Yes! We offer 50% off our Team plan for registered nonprofits. Contact our sales team
                with your nonprofit verification.
              </p>
            </div>
            
            <div className="border-2 border-black p-4 sm:p-6 shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
              <h3 className="text-lg sm:text-xl font-bold mb-2">What happens if I exceed my plan limits?</h3>
              <p className="text-gray-700">
                We&apos;ll notify you when you&apos;re approaching your limits. For the Free plan, you&apos;ll need to
                upgrade to add more projects or team members. Team plan users have unlimited usage.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 sm:py-20 px-4 bg-yellow-400 border-t-2 border-black">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl sm:text-4xl font-bold mb-6">
            Ready to Secure Your Environment Variables?
          </h2>
          <p className="text-lg sm:text-xl mb-8">
            Start with our free plan and upgrade as you grow.
          </p>
          <Link href="/signup">
            <Button className="text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 bg-black text-white hover:bg-gray-800 border-2 border-black">
              Start Free Trial →
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <p>&copy; 2025 EzEnv. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}