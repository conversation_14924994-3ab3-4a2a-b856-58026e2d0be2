'use client'

import Link from 'next/link'
import { Button } from '@/components/atoms/Button'
import { CodeBlock } from '@/components/atoms/CodeBlock'
import { useState } from 'react'

export default function DocsPage() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false)
  
  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="border-b-2 border-black sticky top-0 bg-white z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-4">
              <button
                className="lg:hidden p-2 hover:bg-gray-100 rounded-md"
                onClick={() => setMobileSidebarOpen(!mobileSidebarOpen)}
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
              <Link href="/" className="flex items-center">
                <span className="text-2xl font-bold">EzEnv</span>
              </Link>
            </div>
            
            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center gap-4">
              <Link href="/docs" className="font-medium hover:underline">
                Documentation
              </Link>
              <Link href="/pricing" className="font-medium hover:underline">
                Pricing
              </Link>
              <Link href="/login">
                <Button variant="secondary">Sign In</Button>
              </Link>
              <Link href="/signup">
                <Button>Get Started</Button>
              </Link>
            </div>
            
            {/* Mobile Menu Button */}
            <button
              className="md:hidden p-2 hover:bg-gray-100 rounded-md"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {mobileMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>
        
        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div className="md:hidden border-t-2 border-black bg-white">
            <div className="px-4 py-4 space-y-3">
              <Link href="/docs" className="block font-medium hover:underline">
                Documentation
              </Link>
              <Link href="/pricing" className="block font-medium hover:underline">
                Pricing
              </Link>
              <Link href="/login" className="block">
                <Button variant="secondary" className="w-full">Sign In</Button>
              </Link>
              <Link href="/signup" className="block">
                <Button className="w-full">Get Started</Button>
              </Link>
            </div>
          </div>
        )}
      </nav>

      <div className="flex relative">
        {/* Mobile Sidebar Overlay */}
        {mobileSidebarOpen && (
          <div
            className="lg:hidden fixed inset-0 bg-black/50 z-40"
            onClick={() => setMobileSidebarOpen(false)}
          />
        )}
        
        {/* Sidebar */}
        <aside className={`
          ${mobileSidebarOpen ? 'translate-x-0' : '-translate-x-full'}
          lg:translate-x-0 transition-transform duration-300
          fixed lg:static
          w-64 border-r-2 border-black bg-white
          min-h-screen p-6 
          lg:sticky top-16 h-[calc(100vh-4rem)] overflow-y-auto
          z-50
        `}>
          <nav className="space-y-6">
            <div>
              <h3 className="font-bold mb-2">Getting Started</h3>
              <ul className="space-y-2 text-gray-700">
                <li><a href="#introduction" className="hover:text-black">Introduction</a></li>
                <li><a href="#quickstart" className="hover:text-black">Quickstart</a></li>
                <li><a href="#concepts" className="hover:text-black">Core Concepts</a></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-bold mb-2">Web Platform</h3>
              <ul className="space-y-2 text-gray-700">
                <li><a href="#projects" className="hover:text-black">Projects</a></li>
                <li><a href="#environments" className="hover:text-black">Environments</a></li>
                <li><a href="#secrets" className="hover:text-black">Managing Secrets</a></li>
                <li><a href="#teams" className="hover:text-black">Team Management</a></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-bold mb-2">SDK</h3>
              <ul className="space-y-2 text-gray-700">
                <li><a href="#sdk-installation" className="hover:text-black">Installation</a></li>
                <li><a href="#sdk-configuration" className="hover:text-black">Configuration</a></li>
                <li><a href="#sdk-usage" className="hover:text-black">Usage Examples</a></li>
                <li><a href="#sdk-api" className="hover:text-black">API Reference</a></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-bold mb-2">Security</h3>
              <ul className="space-y-2 text-gray-700">
                <li><a href="#encryption" className="hover:text-black">Encryption</a></li>
                <li><a href="#api-keys" className="hover:text-black">API Keys</a></li>
                <li><a href="#rbac" className="hover:text-black">Access Control</a></li>
              </ul>
            </div>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 w-full lg:max-w-4xl mx-auto p-4 sm:p-6 lg:p-8 overflow-x-hidden">
          {/* Introduction */}
          <section id="introduction" className="mb-16">
            <h1 className="text-3xl sm:text-4xl font-bold mb-6">EzEnv Documentation</h1>
            <p className="text-base sm:text-lg lg:text-xl text-gray-700 mb-4">
              Welcome to the EzEnv documentation. Learn how to securely manage your environment
              variables across all your projects and environments.
            </p>
            <div className="bg-yellow-100 border-2 border-black p-3 sm:p-4 shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
              <p className="font-medium">
                🚀 New to EzEnv? Start with our <a href="#quickstart" className="underline">Quickstart Guide</a>
              </p>
            </div>
          </section>

          {/* Quickstart */}
          <section id="quickstart" className="mb-16">
            <h2 className="text-2xl sm:text-3xl font-bold mb-6">Quickstart</h2>
            
            <div className="space-y-6">
              <div>
                <h3 className="text-lg sm:text-xl font-bold mb-3">1. Create an Account</h3>
                <p className="mb-4">Sign up for a free EzEnv account at <Link href="/signup" className="text-blue-600 underline">ezenv.io/signup</Link></p>
              </div>

              <div>
                <h3 className="text-lg sm:text-xl font-bold mb-3">2. Create Your First Project</h3>
                <p className="mb-4">After signing in, click &quot;Create New Project&quot; and give it a name:</p>
                <CodeBlock code="Project Name: my-awesome-app" language="text" />
              </div>

              <div>
                <h3 className="text-lg sm:text-xl font-bold mb-3">3. Add Environment Variables</h3>
                <p className="mb-4">Create an environment (e.g., &quot;development&quot;) and add your secrets:</p>
                <CodeBlock code={`DATABASE_URL=postgres://localhost:5432/myapp
API_KEY=sk_test_123456789
NODE_ENV=development`} language="env" />
              </div>

              <div>
                <h3 className="text-lg sm:text-xl font-bold mb-3">4. Install the SDK</h3>
                <p className="mb-4">Install the EzEnv SDK in your project:</p>
                <CodeBlock code="npm install ezenv-sdk" />
              </div>

              <div>
                <h3 className="text-lg sm:text-xl font-bold mb-3">5. Use in Your Code</h3>
                <p className="mb-4">Load your environment variables:</p>
                <CodeBlock 
                  code={`const { createClient } = require('ezenv-sdk');

const client = createClient('ezenv_your_api_key');
await client.load('my-awesome-app', 'development');

// Your secrets are now in process.env
console.log(process.env.DATABASE_URL);`}
                  language="javascript"
                />
              </div>
            </div>
          </section>

          {/* Core Concepts */}
          <section id="concepts" className="mb-16">
            <h2 className="text-2xl sm:text-3xl font-bold mb-6">Core Concepts</h2>
            
            <div className="space-y-4 sm:space-y-6">
              <div className="border-2 border-black p-4 sm:p-6 shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
                <h3 className="text-lg sm:text-xl font-bold mb-3">Projects</h3>
                <p className="text-sm sm:text-base text-gray-700">
                  A project is a container for your application&apos;s configuration. Each project can have
                  multiple environments and team members with different access levels.
                </p>
              </div>
              
              <div className="border-2 border-black p-4 sm:p-6 shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
                <h3 className="text-lg sm:text-xl font-bold mb-3">Environments</h3>
                <p className="text-sm sm:text-base text-gray-700">
                  Environments represent different deployment contexts (development, staging, production).
                  Each environment has its own set of secrets that can be managed independently.
                </p>
              </div>
              
              <div className="border-2 border-black p-4 sm:p-6 shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
                <h3 className="text-lg sm:text-xl font-bold mb-3">Secrets</h3>
                <p className="text-sm sm:text-base text-gray-700">
                  Secrets are key-value pairs that store your sensitive configuration data. All secrets
                  are encrypted at rest using AES-256 encryption.
                </p>
              </div>
              
              <div className="border-2 border-black p-4 sm:p-6 shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
                <h3 className="text-lg sm:text-xl font-bold mb-3">Teams</h3>
                <p className="text-sm sm:text-base text-gray-700">
                  Teams allow you to collaborate with others. Team members can have different roles
                  (Admin, Manager, Member) with corresponding permissions.
                </p>
              </div>
            </div>
          </section>

          {/* Projects Section */}
          <section id="projects" className="mb-16">
            <h2 className="text-2xl sm:text-3xl font-bold mb-6">Working with Projects</h2>
            
            <h3 className="text-lg sm:text-xl font-bold mb-4">Creating a Project</h3>
            <ol className="list-decimal list-inside space-y-2 mb-6 text-sm sm:text-base">
              <li>Navigate to your dashboard</li>
              <li>Click &quot;Create New Project&quot;</li>
              <li>Enter a unique project name</li>
              <li>Click &quot;Create&quot;</li>
            </ol>
            
            <h3 className="text-lg sm:text-xl font-bold mb-4">Project Settings</h3>
            <p className="mb-4">To access project settings:</p>
            <ul className="list-disc list-inside space-y-2 mb-6 text-sm sm:text-base">
              <li>Click on the project name in your dashboard</li>
              <li>Click the &quot;Project Settings&quot; button</li>
              <li>Update project name or manage team access</li>
            </ul>
            
            <h3 className="text-lg sm:text-xl font-bold mb-4">Deleting a Project</h3>
            <div className="bg-red-50 border-2 border-red-500 p-3 sm:p-4 mb-6">
              <p className="text-red-700">
                ⚠️ <strong>Warning:</strong> Deleting a project will permanently remove all environments
                and secrets. This action cannot be undone.
              </p>
            </div>
          </section>

          {/* SDK Installation */}
          <section id="sdk-installation" className="mb-16">
            <h2 className="text-2xl sm:text-3xl font-bold mb-6">SDK Installation</h2>
            
            <h3 className="text-lg sm:text-xl font-bold mb-4">NPM</h3>
            <CodeBlock code="npm install ezenv-sdk" />
            
            <h3 className="text-lg sm:text-xl font-bold mb-4">Yarn</h3>
            <CodeBlock code="yarn add ezenv-sdk" />
            
            <h3 className="text-lg sm:text-xl font-bold mb-4">PNPM</h3>
            <CodeBlock code="pnpm add ezenv-sdk" />
          </section>

          {/* SDK Configuration */}
          <section id="sdk-configuration" className="mb-16">
            <h2 className="text-3xl font-bold mb-6">SDK Configuration</h2>
            
            <h3 className="text-lg sm:text-xl font-bold mb-4">Getting Your API Key</h3>
            <ol className="list-decimal list-inside space-y-2 mb-6 text-sm sm:text-base">
              <li>Log into your EzEnv account</li>
              <li>Navigate to Settings → API Keys</li>
              <li>Click &quot;Generate New Key&quot;</li>
              <li>Copy the key (it won&apos;t be shown again)</li>
            </ol>
            
            <h3 className="text-lg sm:text-xl font-bold mb-4">Configuration Options</h3>
            <CodeBlock 
              code={`const client = new EzEnv({
  apiKey: 'ezenv_your_api_key',
  baseUrl: 'https://ezenv.dev', // Optional
});`}
              language="javascript"
            />
          </section>

          {/* SDK Usage */}
          <section id="sdk-usage" className="mb-16">
            <h2 className="text-3xl font-bold mb-6">SDK Usage Examples</h2>
            
            <h3 className="text-lg sm:text-xl font-bold mb-4">Basic Usage</h3>
            <CodeBlock 
              code={`// Fetch secrets
const secrets = await client.get('project-name', 'environment');
console.log(secrets.DATABASE_URL);

// Load into process.env
await client.load('project-name', 'environment');
console.log(process.env.DATABASE_URL);`}
              language="javascript"
            />
            
            <h3 className="text-lg sm:text-xl font-bold mb-4">With Options</h3>
            <CodeBlock 
              code={`// Bypass cache
const secrets = await client.get('project', 'env', {
  noCache: true
});

// Override existing env vars
await client.load('project', 'env', {
  override: true
});`}
              language="javascript"
            />
            
            <h3 className="text-lg sm:text-xl font-bold mb-4">Error Handling</h3>
            <CodeBlock 
              code={`try {
  await client.load('project', 'environment');
} catch (error) {
  if (error.name === 'AuthError') {
    console.error('Invalid API key');
  } else if (error.name === 'NotFoundError') {
    console.error('Project or environment not found');
  }
}`}
              language="javascript"
            />
          </section>

          {/* Security */}
          <section id="encryption" className="mb-16">
            <h2 className="text-3xl font-bold mb-6">Security</h2>
            
            <h3 className="text-lg sm:text-xl font-bold mb-4">Encryption</h3>
            <p className="mb-4">All secrets are encrypted using:</p>
            <ul className="list-disc list-inside space-y-2 mb-6 text-sm sm:text-base">
              <li>AES-256 encryption at rest</li>
              <li>TLS 1.3 for data in transit</li>
              <li>Encrypted database backups</li>
            </ul>
            
            <h3 className="text-xl font-bold mb-4" id="api-keys">API Keys</h3>
            <p className="mb-4">Best practices for API keys:</p>
            <ul className="list-disc list-inside space-y-2 mb-6 text-sm sm:text-base">
              <li>Never commit API keys to version control</li>
              <li>Use environment variables to store keys</li>
              <li>Rotate keys regularly</li>
              <li>Revoke unused keys immediately</li>
            </ul>
            
            <h3 className="text-xl font-bold mb-4" id="rbac">Role-Based Access Control</h3>
            <div className="space-y-3 sm:space-y-4">
              <div className="border-2 border-black p-3 sm:p-4">
                <h4 className="font-bold">Admin</h4>
                <p className="text-sm sm:text-base">Full access to create, read, update, and delete all resources</p>
              </div>
              <div className="border-2 border-black p-3 sm:p-4">
                <h4 className="font-bold">Manager</h4>
                <p className="text-sm sm:text-base">Can view and edit existing environments and secrets</p>
              </div>
              <div className="border-2 border-black p-3 sm:p-4">
                <h4 className="font-bold">Member</h4>
                <p className="text-sm sm:text-base">Read-only access to view secrets and generate API keys</p>
              </div>
            </div>
          </section>

          {/* Help */}
          <section className="mb-16 bg-gray-100 border-2 border-black p-4 sm:p-6 lg:p-8 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
            <h2 className="text-xl sm:text-2xl font-bold mb-4">Need Help?</h2>
            <p className="mb-4">We&apos;re here to help you get the most out of EzEnv:</p>
            <ul className="space-y-2 text-sm sm:text-base">
              <li>📧 Email: <a href="mailto:<EMAIL>" className="underline"><EMAIL></a></li>
              <li>💬 Community: <a href="#" className="underline">Join our Discord</a></li>
              <li>🐛 Issues: <a href="#" className="underline">GitHub Issues</a></li>
            </ul>
          </section>
        </main>
      </div>
    </div>
  )
}