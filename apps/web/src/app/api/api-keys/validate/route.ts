import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import bcrypt from 'bcryptjs'
import { isValidApiKey } from '@/lib/api-keys/generator'

export async function POST(request: NextRequest) {
  try {
    // Get API key from Authorization header
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Missing or invalid authorization header' }, { status: 401 })
    }

    const apiKey = authHeader.substring(7) // Remove 'Bearer ' prefix

    // Validate key format
    if (!isValidApiKey(apiKey)) {
      return NextResponse.json({ error: 'Invalid API key format' }, { status: 401 })
    }

    const supabase = await createClient()

    // Find all non-revoked keys (we need to check hash against each one)
    const { data: apiKeys, error } = await supabase
      .from('api_keys')
      .select('id, user_id, key_hash')
      .is('revoked_at', null)

    if (error) {
      console.error('Failed to fetch API keys:', error)
      return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
    }

    // Check each key hash
    let validKey = null
    for (const key of apiKeys || []) {
      const isValid = await bcrypt.compare(apiKey, key.key_hash)
      if (isValid) {
        validKey = key
        break
      }
    }

    if (!validKey) {
      return NextResponse.json({ error: 'Invalid API key' }, { status: 401 })
    }

    // Update last_used_at
    await supabase
      .from('api_keys')
      .update({ last_used_at: new Date().toISOString() })
      .eq('id', validKey.id)

    // Return user_id for further authentication
    return NextResponse.json({
      user_id: validKey.user_id,
      key_id: validKey.id
    })
  } catch (error) {
    console.error('Error validating API key:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}