import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { randomBytes } from 'crypto'
import bcrypt from 'bcryptjs'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Check authentication
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get request body
    const { name } = await request.json()
    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      return NextResponse.json({ error: 'API key name is required' }, { status: 400 })
    }

    // Check for duplicate names
    const { data: existingKeys } = await supabase
      .from('api_keys')
      .select('id')
      .eq('user_id', user.id)
      .eq('name', name.trim())
      .is('revoked_at', null)
      .single()

    if (existingKeys) {
      return NextResponse.json({ error: 'You already have a key with this name' }, { status: 400 })
    }

    // Generate secure random key
    const buffer = randomBytes(32)
    const key = `ezenv_${buffer.toString('base64url')}`
    
    // Hash for storage
    const hash = await bcrypt.hash(key, 10)
    
    // Prefix for display
    const prefix = key.substring(0, 14)

    // Store in database
    const { data, error } = await supabase
      .from('api_keys')
      .insert({
        user_id: user.id,
        name: name.trim(),
        key_hash: hash,
        key_prefix: prefix
      })
      .select()
      .single()

    if (error) {
      console.error('Failed to create API key:', error)
      return NextResponse.json({ error: 'Failed to create API key' }, { status: 500 })
    }

    return NextResponse.json({
      key,
      key_id: data.id,
      name: name.trim()
    })
  } catch (error) {
    console.error('Error generating API key:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}