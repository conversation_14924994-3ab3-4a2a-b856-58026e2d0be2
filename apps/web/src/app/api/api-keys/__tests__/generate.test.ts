import { POST } from '../generate/route'
import { NextRequest } from 'next/server'

// Mock Supabase
jest.mock('@/lib/supabase/server', () => ({
  createClient: jest.fn(() => ({
    auth: {
      getUser: jest.fn()
    },
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          eq: jest.fn(() => ({
            is: jest.fn(() => ({
              single: jest.fn()
            }))
          }))
        }))
      })),
      insert: jest.fn(() => ({
        select: jest.fn(() => ({
          single: jest.fn()
        }))
      }))
    }))
  }))
}))

// Type for the mock Supabase client
type MockSupabaseClient = {
  auth: {
    getUser: jest.Mock
  }
  from: jest.Mock
}

describe('POST /api/api-keys/generate', () => {
  let mockSupabase: MockSupabaseClient

  beforeEach(() => {
    jest.clearAllMocks()
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const { createClient } = require('@/lib/supabase/server')
    mockSupabase = {
      auth: {
        getUser: jest.fn()
      },
      from: jest.fn()
    }
    createClient.mockReturnValue(mockSupabase)
  })

  it('should return 401 if user is not authenticated', async () => {
    mockSupabase.auth.getUser.mockResolvedValue({ data: { user: null } })

    const request = new NextRequest('http://localhost:3000/api/api-keys/generate', {
      method: 'POST',
      body: JSON.stringify({ name: 'Test Key' })
    })

    const response = await POST(request)
    expect(response.status).toBe(401)
    const data = await response.json()
    expect(data.error).toBe('Unauthorized')
  })

  it('should return 400 if name is missing', async () => {
    mockSupabase.auth.getUser.mockResolvedValue({ 
      data: { user: { id: 'user-123' } } 
    })

    const request = new NextRequest('http://localhost:3000/api/api-keys/generate', {
      method: 'POST',
      body: JSON.stringify({})
    })

    const response = await POST(request)
    expect(response.status).toBe(400)
    const data = await response.json()
    expect(data.error).toBe('API key name is required')
  })

  it('should return 400 if name already exists', async () => {
    mockSupabase.auth.getUser.mockResolvedValue({ 
      data: { user: { id: 'user-123' } } 
    })

    const fromMock = {
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      is: jest.fn().mockReturnThis(),
      single: jest.fn().mockResolvedValue({ data: { id: 'existing-key' } })
    }
    mockSupabase.from.mockReturnValue(fromMock)

    const request = new NextRequest('http://localhost:3000/api/api-keys/generate', {
      method: 'POST',
      body: JSON.stringify({ name: 'Existing Key' })
    })

    const response = await POST(request)
    expect(response.status).toBe(400)
    const data = await response.json()
    expect(data.error).toBe('You already have a key with this name')
  })

  it('should successfully generate a new API key', async () => {
    mockSupabase.auth.getUser.mockResolvedValue({ 
      data: { user: { id: 'user-123' } } 
    })

    // Mock checking for existing keys
    const selectMock = {
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      is: jest.fn().mockReturnThis(),
      single: jest.fn().mockResolvedValue({ data: null })
    }

    // Mock inserting new key
    const insertMock = {
      insert: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      single: jest.fn().mockResolvedValue({ 
        data: { id: 'new-key-id' } 
      })
    }

    mockSupabase.from.mockImplementation((table: string) => {
      if (table === 'api_keys' && insertMock.insert.mock.calls.length > 0) {
        return insertMock
      }
      return selectMock
    })

    const request = new NextRequest('http://localhost:3000/api/api-keys/generate', {
      method: 'POST',
      body: JSON.stringify({ name: 'New Test Key' })
    })

    const response = await POST(request)
    expect(response.status).toBe(200)
    const data = await response.json()
    
    expect(data).toHaveProperty('key')
    expect(data).toHaveProperty('key_id', 'new-key-id')
    expect(data).toHaveProperty('name', 'New Test Key')
    expect(data.key).toMatch(/^ezenv_[A-Za-z0-9_-]{43}$/)
  })
})