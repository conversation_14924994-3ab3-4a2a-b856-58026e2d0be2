import { NextRequest, NextResponse } from 'next/server'
import { decrypt } from '@/lib/crypto/encryption'

export async function POST(request: NextRequest) {
  try {
    const { encrypted, iv } = await request.json()
    
    if (!encrypted || !iv) {
      return NextResponse.json(
        { error: 'Encrypted text and IV are required' },
        { status: 400 }
      )
    }
    
    const decrypted = decrypt(encrypted, iv)
    
    return NextResponse.json({ decrypted })
  } catch (error) {
    console.error('Decryption API error:', error)
    return NextResponse.json(
      { error: 'Failed to decrypt data' },
      { status: 500 }
    )
  }
}