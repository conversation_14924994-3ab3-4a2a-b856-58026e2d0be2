import { NextRequest, NextResponse } from 'next/server'
import { encrypt } from '@/lib/crypto/encryption'

export async function POST(request: NextRequest) {
  try {
    const { text } = await request.json()
    
    if (!text) {
      return NextResponse.json(
        { error: 'Text is required' },
        { status: 400 }
      )
    }
    
    const result = encrypt(text)
    
    return NextResponse.json(result)
  } catch (error) {
    console.error('Encryption API error:', error)
    return NextResponse.json(
      { error: 'Failed to encrypt data' },
      { status: 500 }
    )
  }
}