import { POST, OPTIONS } from '../route'
import { decrypt } from '@/lib/crypto/encryption'
import { NextRequest } from 'next/server'

// Mock dependencies
jest.mock('@/lib/supabase/server', () => ({
  createClient: jest.fn()
}))

jest.mock('bcryptjs', () => ({
  compare: jest.fn()
}))

jest.mock('@/lib/crypto/encryption', () => ({
  decrypt: jest.fn()
}))

// Import bcryptjs at the top level
import bcrypt from 'bcryptjs'

// Type bcrypt.compare as a jest mock
const mockBcryptCompare = bcrypt.compare as jest.Mock

// Type for the mock Supabase client
type MockSupabaseClient = {
  from: jest.Mock
}

describe('Secrets API Endpoint', () => {
  let mockSupabase: MockSupabaseClient

  beforeEach(() => {
    jest.clearAllMocks()
    const { createClient } = jest.requireMock('@/lib/supabase/server')
    mockSupabase = {
      from: jest.fn()
    }
    createClient.mockResolvedValue(mockSupabase)
  })

  describe('OPTIONS', () => {
    it('should return CORS headers', async () => {
      const response = await OPTIONS()
      expect(response.status).toBe(200)
      expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*')
      expect(response.headers.get('Access-Control-Allow-Methods')).toBe('POST, OPTIONS')
    })
  })

  describe('POST', () => {
    it('should return 401 if no authorization header', async () => {
      const request = new NextRequest('http://localhost/api/v1/secrets', {
        method: 'POST',
        body: JSON.stringify({ projectName: 'test', environmentName: 'dev' })
      })

      const response = await POST(request)
      expect(response.status).toBe(401)
      const data = await response.json()
      expect(data.error).toBe('Missing API key')
    })

    it('should return 401 for invalid API key format', async () => {
      const request = new NextRequest('http://localhost/api/v1/secrets', {
        method: 'POST',
        headers: { authorization: 'Bearer invalid-key' },
        body: JSON.stringify({ projectName: 'test', environmentName: 'dev' })
      })

      const response = await POST(request )
      expect(response.status).toBe(401)
      const data = await response.json()
      expect(data.error).toBe('Invalid API key format')
    })

    it('should return 400 if projectName is missing', async () => {
      const request = new NextRequest('http://localhost/api/v1/secrets', {
        method: 'POST',
        headers: { authorization: 'Bearer ezenv_1234567890abcdefghijklmnopqrstuvwxyz1234567' },
        body: JSON.stringify({ environmentName: 'dev' })
      })

      const response = await POST(request )
      expect(response.status).toBe(400)
      const data = await response.json()
      expect(data.error).toBe('Missing projectName or environmentName')
    })

    it('should return 401 for invalid API key', async () => {
      const request = new NextRequest('http://localhost/api/v1/secrets', {
        method: 'POST',
        headers: { authorization: 'Bearer ezenv_1234567890abcdefghijklmnopqrstuvwxyz1234567' },
        body: JSON.stringify({ projectName: 'test', environmentName: 'dev' })
      })

      // Mock API key lookup
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        is: jest.fn().mockResolvedValue({ data: [], error: null })
      })

      const response = await POST(request )
      expect(response.status).toBe(401)
      const data = await response.json()
      expect(data.error).toBe('Invalid API key')
    })

    it('should return 404 if project not found', async () => {
      const request = new NextRequest('http://localhost/api/v1/secrets', {
        method: 'POST',
        headers: { authorization: 'Bearer ezenv_1234567890abcdefghijklmnopqrstuvwxyz1234567' },
        body: JSON.stringify({ projectName: 'nonexistent', environmentName: 'dev' })
      })

      // Mock valid API key
      const apiKeyMock = {
        select: jest.fn().mockReturnThis(),
        is: jest.fn().mockResolvedValue({
          data: [{ id: 'key-1', user_id: 'user-123', key_hash: 'hash' }],
          error: null
        })
      }

      // Mock successful key validation
      mockBcryptCompare.mockResolvedValueOnce(true)

      // Mock update for last_used_at
      const updateMock = {
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({ error: null })
      }

      // Mock project not found
      const projectMock = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: null, error: 'Not found' })
      }

      mockSupabase.from.mockImplementation((table: string) => {
        if (table === 'api_keys') {
          return updateMock.update.mock.calls.length > 0 ? updateMock : apiKeyMock
        }
        if (table === 'projects') {
          return projectMock
        }
        return { select: jest.fn().mockReturnThis() }
      })

      const response = await POST(request )
      expect(response.status).toBe(404)
      const data = await response.json()
      expect(data.error).toBe('Project or environment not found')
    })

    it('should successfully return decrypted secrets', async () => {
      const request = new NextRequest('http://localhost/api/v1/secrets', {
        method: 'POST',
        headers: { authorization: 'Bearer ezenv_1234567890abcdefghijklmnopqrstuvwxyz1234567' },
        body: JSON.stringify({ projectName: 'my-app', environmentName: 'production' })
      })

      // Mock valid API key
      const apiKeyMock = {
        select: jest.fn().mockReturnThis(),
        is: jest.fn().mockResolvedValue({
          data: [{ id: 'key-1', user_id: 'user-123', key_hash: 'hash' }],
          error: null
        })
      }

      // Mock successful key validation
      mockBcryptCompare.mockResolvedValueOnce(true)

      // Mock update for last_used_at
      const updateMock = {
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({ error: null })
      }

      // Mock project found with access
      const projectMock = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: { id: 'project-1', team_id: 'team-1' },
          error: null
        })
      }

      // Mock environment found
      const environmentMock = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: { id: 'env-1' },
          error: null
        })
      }

      // Mock secrets
      const secretsMock = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({
          data: [
            { key: 'DATABASE_URL', encrypted_value: 'encrypted1', iv: 'iv1' },
            { key: 'API_KEY', encrypted_value: 'encrypted2', iv: 'iv2' }
          ],
          error: null
        })
      }

      mockSupabase.from.mockImplementation((table: string) => {
        if (table === 'api_keys') {
          return updateMock.update.mock.calls.length > 0 ? updateMock : apiKeyMock
        }
        if (table === 'projects') {
          return projectMock
        }
        if (table === 'environments') {
          return environmentMock
        }
        if (table === 'secrets') {
          return secretsMock
        }
        return { select: jest.fn().mockReturnThis() }
      })

      // Mock decryption
      ;(decrypt as jest.Mock).mockImplementation((encrypted) => {
        if (encrypted === 'encrypted1') return 'postgresql://localhost:5432/db'
        if (encrypted === 'encrypted2') return 'sk_test_123'
        throw new Error('Unknown encrypted value')
      })

      const response = await POST(request )
      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data.secrets).toEqual({
        DATABASE_URL: 'postgresql://localhost:5432/db',
        API_KEY: 'sk_test_123'
      })
    })

    it('should return empty object if no secrets exist', async () => {
      const request = new NextRequest('http://localhost/api/v1/secrets', {
        method: 'POST',
        headers: { authorization: 'Bearer ezenv_1234567890abcdefghijklmnopqrstuvwxyz1234567' },
        body: JSON.stringify({ projectName: 'my-app', environmentName: 'staging' })
      })

      // Mock valid API key
      const apiKeyMock = {
        select: jest.fn().mockReturnThis(),
        is: jest.fn().mockResolvedValue({
          data: [{ id: 'key-1', user_id: 'user-123', key_hash: 'hash' }],
          error: null
        })
      }

      mockBcryptCompare.mockResolvedValueOnce(true)

      // Mock everything found but no secrets
      const projectMock = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: { id: 'project-1', team_id: 'team-1' },
          error: null
        })
      }

      const environmentMock = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: { id: 'env-1' },
          error: null
        })
      }

      const secretsMock = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({
          data: [],
          error: null
        })
      }

      mockSupabase.from.mockImplementation((table: string) => {
        if (table === 'api_keys') {
          return apiKeyMock
        }
        if (table === 'projects') {
          return projectMock
        }
        if (table === 'environments') {
          return environmentMock
        }
        if (table === 'secrets') {
          return secretsMock
        }
        return { 
          update: jest.fn().mockReturnThis(), 
          eq: jest.fn().mockResolvedValue({ error: null }) 
        }
      })

      const response = await POST(request )
      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data.secrets).toEqual({})
    })

    it('should continue if some secrets fail to decrypt', async () => {
      const request = new NextRequest('http://localhost/api/v1/secrets', {
        method: 'POST',
        headers: { authorization: 'Bearer ezenv_1234567890abcdefghijklmnopqrstuvwxyz1234567' },
        body: JSON.stringify({ projectName: 'my-app', environmentName: 'production' })
      })

      // Set up mocks similar to successful case
      mockBcryptCompare.mockResolvedValueOnce(true)

      mockSupabase.from.mockImplementation((table: string) => {
        if (table === 'api_keys') {
          return {
            select: jest.fn().mockReturnThis(),
            is: jest.fn().mockResolvedValue({
              data: [{ id: 'key-1', user_id: 'user-123', key_hash: 'hash' }],
              error: null
            })
          }
        }
        if (table === 'projects') {
          return {
            select: jest.fn().mockReturnThis(),
            eq: jest.fn().mockReturnThis(),
            single: jest.fn().mockResolvedValue({
              data: { id: 'project-1', team_id: 'team-1' },
              error: null
            })
          }
        }
        if (table === 'environments') {
          return {
            select: jest.fn().mockReturnThis(),
            eq: jest.fn().mockReturnThis(),
            single: jest.fn().mockResolvedValue({
              data: { id: 'env-1' },
              error: null
            })
          }
        }
        if (table === 'secrets') {
          return {
            select: jest.fn().mockReturnThis(),
            eq: jest.fn().mockResolvedValue({
              data: [
                { key: 'DATABASE_URL', encrypted_value: 'encrypted1', iv: 'iv1' },
                { key: 'API_KEY', encrypted_value: 'encrypted2', iv: 'iv2' },
                { key: 'CORRUPT_SECRET', encrypted_value: 'bad-data', iv: 'bad-iv' }
              ],
              error: null
            })
          }
        }
        return { 
          update: jest.fn().mockReturnThis(), 
          eq: jest.fn().mockResolvedValue({ error: null }) 
        }
      })

      // Mock decryption with one failure
      ;(decrypt as jest.Mock).mockImplementation((encrypted) => {
        if (encrypted === 'encrypted1') return 'postgresql://localhost:5432/db'
        if (encrypted === 'encrypted2') return 'sk_test_123'
        if (encrypted === 'bad-data') throw new Error('Decryption failed')
        throw new Error('Unknown encrypted value')
      })

      const response = await POST(request )
      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data.secrets).toEqual({
        DATABASE_URL: 'postgresql://localhost:5432/db',
        API_KEY: 'sk_test_123'
        // CORRUPT_SECRET should be omitted
      })
    })
  })
})