import { NextRequest, NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/service'
import bcrypt from 'bcryptjs'
import { isValidApiKey } from '@/lib/api-keys/generator'
import { decrypt } from '@/lib/crypto/encryption'
import { SupabaseClient } from '@supabase/supabase-js'

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Headers': 'authorization, content-type',
}

// Security headers
const securityHeaders = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
}

export async function OPTIONS() {
  return NextResponse.json({}, { headers: corsHeaders })
}

export async function POST(request: NextRequest) {
  try {
    // Extract API key from Authorization header
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Missing API key' },
        { status: 401, headers: { ...corsHeaders, ...securityHeaders } }
      )
    }

    const apiKey = authHeader.substring(7) // Remove 'Bearer ' prefix

    // Validate key format
    if (!isValidApiKey(apiKey)) {
      return NextResponse.json(
        { error: 'Invalid API key format' },
        { status: 401, headers: { ...corsHeaders, ...securityHeaders } }
      )
    }

    // Parse request body
    const body = await request.json()
    const { projectName, environmentName } = body

    // Validate inputs
    if (!projectName || !environmentName) {
      return NextResponse.json(
        { error: 'Missing projectName or environmentName' },
        { status: 400, headers: { ...corsHeaders, ...securityHeaders } }
      )
    }

    // Use service client for API key validation (bypasses RLS)
    const serviceClient = createServiceClient()
    
    // Validate API key and get user
    const user = await validateApiKey(serviceClient, apiKey)
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid API key' },
        { status: 401, headers: { ...corsHeaders, ...securityHeaders } }
      )
    }

    // Get secrets with permission check
    const secrets = await getSecretsForUser(
      serviceClient, // Use service client to bypass RLS for queries
      user.id,
      projectName,
      environmentName
    )

    if (secrets === null) {
      return NextResponse.json(
        { error: 'Project or environment not found' },
        { status: 404, headers: { ...corsHeaders, ...securityHeaders } }
      )
    }

    if (secrets === false) {
      return NextResponse.json(
        { error: 'Permission denied' },
        { status: 403, headers: { ...corsHeaders, ...securityHeaders } }
      )
    }

    return NextResponse.json(
      { secrets },
      { status: 200, headers: { ...corsHeaders, ...securityHeaders } }
    )

  } catch (error) {
    console.error('Error in secrets API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500, headers: { ...corsHeaders, ...securityHeaders } }
    )
  }
}

async function validateApiKey(supabase: SupabaseClient, apiKey: string): Promise<{ id: string } | null> {
  // Find all non-revoked keys (we need to check hash against each one)
  const { data: apiKeys, error } = await supabase
    .from('api_keys')
    .select('id, user_id, key_hash')
    .is('revoked_at', null)

  if (error || !apiKeys) {
    console.error('Failed to fetch API keys:', error)
    return null
  }

  // Check each key hash
  for (const key of apiKeys) {
    try {
      const isValid = await bcrypt.compare(apiKey, key.key_hash)
      if (isValid) {
        // Update last_used_at
        await supabase
          .from('api_keys')
          .update({ last_used_at: new Date().toISOString() })
          .eq('id', key.id)

        return { id: key.user_id }
      }
    } catch (compareError) {
      console.error('Error comparing key hash:', compareError)
    }
  }
  return null
}

async function getSecretsForUser(
  supabase: SupabaseClient,
  userId: string,
  projectName: string,
  environmentName: string
): Promise<Record<string, string> | null | false> {
  // Get project with team access check
  const { data: project, error: projectError } = await supabase
    .from('projects')
    .select(`
      id,
      team_id,
      teams!inner (
        id,
        team_members!inner (
          user_id,
          role
        )
      )
    `)
    .eq('name', projectName)
    .eq('teams.team_members.user_id', userId)
    .single()

  if (projectError || !project) {
    console.log('Project not found or access denied:', projectError)
    return null
  }

  // Get environment
  const { data: environment, error: envError } = await supabase
    .from('environments')
    .select('id')
    .eq('project_id', project.id)
    .eq('name', environmentName)
    .single()

  if (envError || !environment) {
    console.log('Environment not found:', envError)
    return null
  }

  // Get secrets
  const { data: secrets, error: secretsError } = await supabase
    .from('secrets')
    .select('key, encrypted_value, iv')
    .eq('environment_id', environment.id)

  if (secretsError) {
    console.error('Failed to fetch secrets:', secretsError)
    return null
  }

  if (!secrets || secrets.length === 0) {
    return {}
  }

  // Decrypt secrets
  const decrypted: Record<string, string> = {}
  for (const secret of secrets) {
    try {
      decrypted[secret.key] = decrypt(secret.encrypted_value, secret.iv)
    } catch (error) {
      console.error(`Failed to decrypt ${secret.key}:`, error)
      // Continue with other secrets instead of failing entirely
    }
  }

  return decrypted
}