@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #000000;
}

body {
  background: var(--background);
  color: var(--foreground);
}

/* Background utilities */
.bg-gray-50 {
  background-color: #f9fafb;
}

.bg-white {
  background-color: #ffffff;
}

.bg-red-50 {
  background-color: #fef2f2;
}

.bg-yellow-400 {
  background-color: #facc15;
}

/* Neobrutalist button styles */
.btn-brutal {
  padding: 0.75rem 1.5rem;
  font-weight: 700;
  border: 2px solid #000000;
  background-color: #ffffff;
  color: #000000;
  transition: all 200ms;
  box-shadow: 4px 4px 0px 0px rgba(0,0,0,1);
  cursor: pointer;
}

.btn-brutal:hover {
  box-shadow: none;
  transform: translate(4px, 4px);
}

.btn-brutal:active {
  box-shadow: none;
  transform: translate(4px, 4px);
}

.btn-brutal-primary {
  padding: 0.75rem 1.5rem;
  font-weight: 700;
  border: 2px solid #000000;
  background-color: #FFFF00;
  color: #000000;
  transition: all 200ms;
  box-shadow: 4px 4px 0px 0px rgba(0,0,0,1);
  cursor: pointer;
}

.btn-brutal-primary:hover {
  background-color: #E6E600;
  box-shadow: none;
  transform: translate(4px, 4px);
}

.btn-brutal-primary:active {
  box-shadow: none;
  transform: translate(4px, 4px);
}

/* Card styles */
.card-brutal {
  background-color: #ffffff;
  border: 2px solid #000000;
  box-shadow: 4px 4px 0px 0px rgba(0,0,0,1);
  padding: 1.5rem;
}

/* Input styles */
.input-brutal {
  width: 100%;
  padding: 0.5rem 1rem;
  border: 2px solid #000000;
  background-color: #ffffff;
  color: #000000;
  transition: box-shadow 200ms;
}

.input-brutal:focus {
  outline: none;
  box-shadow: 4px 4px 0px 0px rgba(0,0,0,1);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
}

/* Focus styles */
*:focus {
  outline: none;
}

*:focus-visible {
  outline: 2px solid #000000;
  outline-offset: 2px;
}

/* Text color utilities */
.text-red-500 {
  color: #ef4444;
}

.text-red-700 {
  color: #b91c1c;
}

.text-green-600 {
  color: #16a34a;
}

.text-gray-600 {
  color: #4b5563;
}

/* Border utilities */
.border-red-500 {
  border-color: #ef4444;
}

/* Layout utilities */
.space-y-6 > * + * {
  margin-top: 1.5rem;
}

/* Opacity utilities */
.opacity-50 {
  opacity: 0.5;
}

.opacity-25 {
  opacity: 0.25;
}

.opacity-75 {
  opacity: 0.75;
}

/* Cursor utilities */
.cursor-not-allowed {
  cursor: not-allowed;
}

/* Animation utilities */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes slide-up {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-up {
  animation: slide-up 0.3s ease-out;
}

/* Utility classes */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-gray-500 {
  color: #6b7280;
}

/* Empty state card */
.empty-state-card {
  padding: 3rem 1.5rem;
  background-color: #ffffff;
  border: 2px dashed #9ca3af;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

/* Layout utilities */
.space-y-3 > * + * {
  margin-top: 0.75rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

/* Background utilities */
.bg-gray-100 {
  background-color: #f3f4f6;
}

.bg-gray-200 {
  background-color: #e5e7eb;
}

.bg-gray-300 {
  background-color: #d1d5db;
}

.bg-black {
  background-color: #000000;
}

/* Text utilities */
.text-white {
  color: #ffffff;
}

.text-black {
  color: #000000;
}

.text-red-600 {
  color: #dc2626;
}

/* Layout utilities */
.space-y-2 > * + * {
  margin-top: 0.5rem;
}

/* Radio button styles */
.radio-brutal {
  appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid #000000;
  background-color: #ffffff;
  cursor: pointer;
  position: relative;
}

.radio-brutal:checked {
  background-color: #FFFF00;
}

.radio-brutal:checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 0.5rem;
  height: 0.5rem;
  background-color: #000000;
}

/* Additional background utilities */
.bg-green-50 {
  background-color: #f0fdf4;
}

.bg-yellow-50 {
  background-color: #fefce8;
}

/* Additional text utilities */
.text-green-700 {
  color: #15803d;
}

.text-yellow-600 {
  color: #ca8a04;
}

/* Additional border utilities */
.border-green-500 {
  border-color: #22c55e;
}

.border-yellow-500 {
  border-color: #eab308;
}