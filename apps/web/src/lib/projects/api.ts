import { createClient } from '@/lib/supabase/client'
import { Project, ProjectError } from './types'

export async function createProject(name: string, teamId?: string): Promise<{ data?: Project; error?: ProjectError }> {
  const supabase = createClient()
  
  try {
    // Get the user's current team
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return { error: { message: 'User not authenticated' } }
    }

    let targetTeamId = teamId
    
    // If no team ID provided, get the current team from localStorage
    if (!targetTeamId) {
      targetTeamId = localStorage.getItem('currentTeamId') || undefined
    }
    
    // If still no team ID, get the user's first team
    if (!targetTeamId) {
      const { data: teamMembers, error: teamError } = await supabase
        .from('team_members')
        .select('team_id, role')
        .eq('user_id', user.id)
        .order('created_at')
        .limit(1)

      if (teamError || !teamMembers || teamMembers.length === 0) {
        return { error: { message: 'No team found for user' } }
      }
      
      targetTeamId = teamMembers[0].team_id
    }
    
    // Check if user is admin of the target team
    const { data: teamMember, error: memberError } = await supabase
      .from('team_members')
      .select('role')
      .eq('user_id', user.id)
      .eq('team_id', targetTeamId)
      .single()
      
    if (memberError || !teamMember) {
      return { error: { message: 'You are not a member of this team' } }
    }

    // Check if user is admin
    if (teamMember.role !== 'admin') {
      return { error: { message: 'Only team admins can create projects' } }
    }

    // Create the project
    const { data: project, error: createError } = await supabase
      .from('projects')
      .insert({
        name: name.trim(),
        team_id: targetTeamId,
      })
      .select()
      .single()

    if (createError) {
      // Handle duplicate name error
      if (createError.code === '23505') {
        return { error: { message: 'A project with this name already exists in this team' } }
      }
      return { error: { message: createError.message } }
    }

    return { data: project }
  } catch (error) {
    console.error('Error creating project:', error)
    return { error: { message: 'Failed to create project. Please try again.' } }
  }
}

export async function getProjects(teamId?: string): Promise<{ data?: Project[]; error?: ProjectError }> {
  const supabase = createClient()
  
  try {
    let query = supabase
      .from('projects')
      .select(`
        *,
        team:teams (
          id,
          name
        )
      `)
      
    // Filter by team if provided
    if (teamId) {
      query = query.eq('team_id', teamId)
    } else {
      // If no team ID, try to get from localStorage
      const currentTeamId = localStorage.getItem('currentTeamId')
      if (currentTeamId) {
        query = query.eq('team_id', currentTeamId)
      }
    }
    
    const { data: projects, error } = await query
      .order('created_at', { ascending: false })

    if (error) {
      return { error: { message: error.message } }
    }

    return { data: projects || [] }
  } catch (error) {
    console.error('Error fetching projects:', error)
    return { error: { message: 'Failed to fetch projects. Please try again.' } }
  }
}

export async function getProject(id: string): Promise<{ data?: Project; error?: ProjectError }> {
  const supabase = createClient()
  
  try {
    const { data: project, error } = await supabase
      .from('projects')
      .select(`
        *,
        team:teams (
          id,
          name
        )
      `)
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return { error: { message: 'Project not found' } }
      }
      return { error: { message: error.message } }
    }

    return { data: project }
  } catch (error) {
    console.error('Error fetching project:', error)
    return { error: { message: 'Failed to fetch project. Please try again.' } }
  }
}

export async function deleteProject(id: string): Promise<{ error?: ProjectError }> {
  const supabase = createClient()
  
  try {
    // Get the user
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return { error: { message: 'User not authenticated' } }
    }

    // Get the project to check team ownership
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('team_id')
      .eq('id', id)
      .single()

    if (projectError || !project) {
      return { error: { message: 'Project not found' } }
    }

    // Check if user is admin of the team
    const { data: teamMember, error: memberError } = await supabase
      .from('team_members')
      .select('role')
      .eq('user_id', user.id)
      .eq('team_id', project.team_id)
      .single()

    if (memberError || !teamMember) {
      return { error: { message: 'You are not a member of this team' } }
    }

    if (teamMember.role !== 'admin') {
      return { error: { message: 'Only team admins can delete projects' } }
    }

    // Delete the project (cascade will handle related records)
    const { error: deleteError } = await supabase
      .from('projects')
      .delete()
      .eq('id', id)

    if (deleteError) {
      return { error: { message: deleteError.message } }
    }

    return {}
  } catch (error) {
    console.error('Error deleting project:', error)
    return { error: { message: 'Failed to delete project. Please try again.' } }
  }
}