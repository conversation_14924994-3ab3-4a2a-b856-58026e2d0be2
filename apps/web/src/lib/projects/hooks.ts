'use client'

import { useState, useEffect } from 'react'
import { Project, ProjectError } from './types'
import { createProject as createProject<PERSON><PERSON>, getProjects as getProjectsApi } from './api'

export function useProjects() {
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<ProjectError | null>(null)

  const fetchProjects = async () => {
    setLoading(true)
    setError(null)
    
    const { data, error } = await getProjectsApi()
    
    if (error) {
      setError(error)
    } else {
      setProjects(data || [])
    }
    
    setLoading(false)
  }

  useEffect(() => {
    fetchProjects()
  }, [])

  const createProject = async (name: string, teamId?: string) => {
    setError(null)
    
    const { data, error } = await createProjectApi(name, teamId)
    
    if (error) {
      setError(error)
      return { success: false, error }
    }
    
    if (data) {
      // Optimistically add the new project to the list
      setProjects(prev => [data, ...prev])
    }
    
    return { success: true, data }
  }

  return {
    projects,
    loading,
    error,
    createProject,
    refetch: fetchProjects,
  }
}