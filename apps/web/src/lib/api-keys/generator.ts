import bcrypt from 'bcryptjs'

export async function generateApi<PERSON>ey(): Promise<{
  key: string
  hash: string
  prefix: string
}> {
  // Generate 32 bytes of random data using Web Crypto API
  const array = new Uint8Array(32)
  if (typeof window !== 'undefined' && window.crypto) {
    // Browser environment
    window.crypto.getRandomValues(array)
  } else {
    // Node.js environment
    const crypto = await import('crypto')
    crypto.randomFillSync(array)
  }
  
  // Convert to base64url
  const base64 = btoa(String.fromCharCode(...array))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '')
  
  const key = `ezenv_${base64}`
  
  // Hash for storage
  const hash = await bcrypt.hash(key, 10)
  
  // Prefix for display (first 14 chars: "ezenv_" + 8 chars)
  const prefix = key.substring(0, 14)
  
  return { key, hash, prefix }
}

// Validate key format
export function isValid<PERSON><PERSON><PERSON>ey(key: string): boolean {
  // ezenv_ prefix + base64url encoded 32 bytes (43-44 characters)
  return /^ezenv_[A-Za-z0-9_-]{43,44}$/.test(key)
}

// Compare key with hash
export async function verifyApiKey(key: string, hash: string): Promise<boolean> {
  return bcrypt.compare(key, hash)
}