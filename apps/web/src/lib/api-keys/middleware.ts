import { NextRequest } from 'next/server'

interface ApiKeyValidationResult {
  user_id: string
  key_id: string
}

export async function validate<PERSON><PERSON><PERSON><PERSON>(request: NextRequest): Promise<ApiKeyValidationResult | null> {
  const authHeader = request.headers.get('authorization')
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null
  }

  try {
    const response = await fetch(new URL('/api/api-keys/validate', request.url), {
      method: 'POST',
      headers: {
        'Authorization': authHeader
      }
    })

    if (!response.ok) {
      return null
    }

    return await response.json()
  } catch (error) {
    console.error('Failed to validate API key:', error)
    return null
  }
}