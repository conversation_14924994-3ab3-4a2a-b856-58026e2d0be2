import { createClient } from '@/lib/supabase/client'
import type { <PERSON><PERSON><PERSON><PERSON>, GenerateApiKeyResponse } from './types'

export async function getApiKeys(): Promise<ApiKey[]> {
  const supabase = createClient()
  const { data, error } = await supabase
    .from('api_keys')
    .select('*')
    .is('revoked_at', null)
    .order('created_at', { ascending: false })

  if (error) {
    throw new Error(`Failed to fetch API keys: ${error.message}`)
  }

  return data || []
}

export async function generateApiKey(name: string): Promise<GenerateApiKeyResponse> {
  const response = await fetch('/api/api-keys/generate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ name }),
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || 'Failed to generate API key')
  }

  return response.json()
}

export async function revokeApi<PERSON><PERSON>(keyId: string): Promise<void> {
  const supabase = createClient()
  const { error } = await supabase
    .from('api_keys')
    .update({ revoked_at: new Date().toISOString() })
    .eq('id', keyId)

  if (error) {
    throw new Error(`Failed to revoke API key: ${error.message}`)
  }
}