import { generate<PERSON><PERSON><PERSON><PERSON>, is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, verify<PERSON><PERSON><PERSON><PERSON> } from '../generator'
import bcrypt from 'bcryptjs'

describe('API Key Generator', () => {
  describe('generateApiKey', () => {
    it('should generate a unique API key with correct format', async () => {
      const result1 = await generateApiKey()
      const result2 = await generateApiKey()

      // Check format
      expect(result1.key).toMatch(/^ezenv_[A-Za-z0-9_-]{43,44}$/)
      expect(result2.key).toMatch(/^ezenv_[A-Za-z0-9_-]{43,44}$/)

      // Check uniqueness
      expect(result1.key).not.toBe(result2.key)
      expect(result1.hash).not.toBe(result2.hash)
    })

    it('should generate correct prefix', async () => {
      const result = await generateApiKey()
      
      expect(result.prefix).toBe(result.key.substring(0, 14))
      expect(result.prefix).toMatch(/^ezenv_[A-Za-z0-9_-]{8}$/)
    })

    it('should generate a valid bcrypt hash', async () => {
      const result = await generateApiKey()
      
      // Verify the hash is valid bcrypt format
      expect(result.hash).toMatch(/^\$2[aby]\$\d{2}\$.{53}$/)
      
      // Verify the hash matches the key
      const isValid = await bcrypt.compare(result.key, result.hash)
      expect(isValid).toBe(true)
    })
  })

  describe('isValidApiKey', () => {
    it('should validate correct API key format', () => {
      const validKeys = [
        'ezenv_1234567890abcdefghijklmnopqrstuvwxyz1234567',
        'ezenv_ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890abcdef',
        'ezenv___________________________________________',
        'ezenv_-------------------------------------------'
      ]

      validKeys.forEach(key => {
        expect(isValidApiKey(key)).toBe(true)
      })
    })

    it('should reject invalid API key formats', () => {
      const invalidKeys = [
        'invalid_1234567890abcdefghijklmnopqrstuvwxyz1234567', // Wrong prefix
        'ezenv_123', // Too short
        'ezenv_1234567890abcdefghijklmnopqrstuvwxyz123456789', // Too long
        'ezenv_!@#$%^&*()abcdefghijklmnopqrstuvwxyz1234567', // Invalid characters
        'ezenv_', // Missing body
        '', // Empty
        'ezenv', // No underscore
      ]

      invalidKeys.forEach(key => {
        expect(isValidApiKey(key)).toBe(false)
      })
    })
  })

  describe('verifyApiKey', () => {
    it('should verify a valid key-hash pair', async () => {
      const { key, hash } = await generateApiKey()
      const isValid = await verifyApiKey(key, hash)
      expect(isValid).toBe(true)
    })

    it('should reject an invalid key-hash pair', async () => {
      const { hash } = await generateApiKey()
      const differentKey = 'ezenv_differentkey1234567890abcdefghijklmnopqrs'
      const isValid = await verifyApiKey(differentKey, hash)
      expect(isValid).toBe(false)
    })
  })
})