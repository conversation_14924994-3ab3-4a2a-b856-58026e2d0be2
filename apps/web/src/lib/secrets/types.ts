export interface Secret {
  id: string
  environment_id: string
  key: string
  encrypted_value: string
  iv: string
  created_by?: string
  created_at: string
  updated_at: string
  // Client-side only - not stored in DB
  decrypted_value?: string
  isDecrypted?: boolean
  isVisible?: boolean
}

export interface CreateSecretInput {
  environment_id: string
  key: string
  value: string // Plain text value that will be encrypted
}

export interface UpdateSecretInput {
  id: string
  value: string // Plain text value that will be encrypted
}

export interface SecretError {
  message: string
  code?: string
}