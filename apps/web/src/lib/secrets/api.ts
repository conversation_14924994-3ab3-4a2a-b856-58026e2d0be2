import { createClient } from '@/lib/supabase/client'
import { Secret, CreateSecretInput, UpdateSecretInput, SecretError } from './types'
import { encryptClient, decryptClient } from '@/lib/crypto/encryption'

// Validate secret key format
export function validateSecretKey(key: string): string | null {
  const trimmedKey = key.trim()
  
  if (!trimmedKey) {
    return 'Key is required'
  }

  if (trimmedKey.length < 1) {
    return 'Key must be at least 1 character'
  }

  if (trimmedKey.length > 50) {
    return 'Key must be less than 50 characters'
  }

  // Must start with a letter and contain only uppercase letters, numbers, and underscores
  const validKeyRegex = /^[A-Z][A-Z0-9_]*$/
  if (!validKeyRegex.test(trimmedKey)) {
    return 'Key must start with a letter and contain only uppercase letters, numbers, and underscores'
  }

  return null
}

export async function getSecrets(environmentId: string): Promise<{ data?: Secret[]; error?: SecretError }> {
  const supabase = createClient()
  
  try {
    const { data: secrets, error } = await supabase
      .from('secrets')
      .select('*')
      .eq('environment_id', environmentId)
      .order('key', { ascending: true })

    if (error) {
      return { error: { message: error.message } }
    }

    // Return secrets without decrypted values
    return { data: secrets || [] }
  } catch (error) {
    console.error('Error fetching secrets:', error)
    return { error: { message: 'Failed to fetch secrets. Please try again.' } }
  }
}

export async function createSecret(input: CreateSecretInput): Promise<{ data?: Secret; error?: SecretError }> {
  const supabase = createClient()
  
  // Validate key
  const keyError = validateSecretKey(input.key)
  if (keyError) {
    return { error: { message: keyError } }
  }

  try {
    // Encrypt the value
    const { encrypted, iv } = await encryptClient(input.value)

    // Save to database
    const { data: secret, error } = await supabase
      .from('secrets')
      .insert({
        environment_id: input.environment_id,
        key: input.key.trim().toUpperCase(),
        encrypted_value: encrypted,
        iv: iv,
      })
      .select()
      .single()

    if (error) {
      if (error.code === '23505') {
        return { error: { message: 'A secret with this key already exists' } }
      }
      if (error.code === '42501') {
        return { error: { message: 'You don\'t have permission to create secrets' } }
      }
      return { error: { message: error.message } }
    }

    return { data: secret }
  } catch (error) {
    console.error('Error creating secret:', error)
    return { error: { message: 'Failed to create secret. Please try again.' } }
  }
}

export async function updateSecret(input: UpdateSecretInput): Promise<{ data?: Secret; error?: SecretError }> {
  const supabase = createClient()

  try {
    // Encrypt the new value
    const { encrypted, iv } = await encryptClient(input.value)

    // Update in database
    const { data: secret, error } = await supabase
      .from('secrets')
      .update({
        encrypted_value: encrypted,
        iv: iv,
        updated_at: new Date().toISOString(),
      })
      .eq('id', input.id)
      .select()
      .single()

    if (error) {
      if (error.code === '42501') {
        return { error: { message: 'You don\'t have permission to update secrets' } }
      }
      return { error: { message: error.message } }
    }

    return { data: secret }
  } catch (error) {
    console.error('Error updating secret:', error)
    return { error: { message: 'Failed to update secret. Please try again.' } }
  }
}

export async function deleteSecret(id: string): Promise<{ success: boolean; error?: SecretError }> {
  const supabase = createClient()
  
  try {
    const { error } = await supabase
      .from('secrets')
      .delete()
      .eq('id', id)

    if (error) {
      if (error.code === '42501') {
        return { success: false, error: { message: 'You don\'t have permission to delete secrets' } }
      }
      return { success: false, error: { message: error.message } }
    }

    return { success: true }
  } catch (error) {
    console.error('Error deleting secret:', error)
    return { success: false, error: { message: 'Failed to delete secret. Please try again.' } }
  }
}

// Decrypt a single secret value
export async function decryptSecretValue(secret: Secret): Promise<string> {
  try {
    return await decryptClient(secret.encrypted_value, secret.iv)
  } catch (error) {
    console.error('Error decrypting secret:', error)
    throw new Error('Failed to decrypt secret value')
  }
}

// Batch import secrets
export async function batchImportSecrets(
  environmentId: string,
  secrets: Array<{ key: string; value: string }>,
  strategy: 'replace' | 'skip' = 'skip'
): Promise<{ 
  success: boolean; 
  imported: number; 
  skipped: number; 
  errors: string[] 
}> {
  const supabase = createClient()
  const errors: string[] = []
  let imported = 0
  let skipped = 0

  try {
    // Get existing keys
    const { data: existingSecrets } = await supabase
      .from('secrets')
      .select('key')
      .eq('environment_id', environmentId)
    
    const existingKeys = new Set(existingSecrets?.map(s => s.key) || [])

    // Prepare secrets for import
    const toImport: Array<{
      environment_id: string
      key: string
      encrypted_value: string
      iv: string
    }> = []

    for (const secret of secrets) {
      // Validate key
      const keyError = validateSecretKey(secret.key)
      if (keyError) {
        errors.push(`${secret.key}: ${keyError}`)
        continue
      }

      const key = secret.key.trim().toUpperCase()
      
      // Check duplicate handling
      if (existingKeys.has(key) && strategy === 'skip') {
        skipped++
        continue
      }

      try {
        // Encrypt the value
        const { encrypted, iv } = await encryptClient(secret.value)
        toImport.push({
          environment_id: environmentId,
          key,
          encrypted_value: encrypted,
          iv
        })
      } catch {
        errors.push(`${key}: Failed to encrypt value`)
      }
    }

    // Batch insert/update
    if (toImport.length > 0) {
      const { error } = await supabase
        .from('secrets')
        .upsert(toImport, { 
          onConflict: 'environment_id,key',
          ignoreDuplicates: strategy === 'skip'
        })

      if (error) {
        console.error('Batch import error:', error)
        errors.push('Failed to import secrets to database')
      } else {
        imported = toImport.length
      }
    }

    return {
      success: errors.length === 0,
      imported,
      skipped,
      errors
    }
  } catch (error) {
    console.error('Error in batch import:', error)
    return {
      success: false,
      imported: 0,
      skipped: 0,
      errors: ['An unexpected error occurred during import']
    }
  }
}