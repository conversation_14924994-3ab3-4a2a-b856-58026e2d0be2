'use client'

import { useState, useEffect, useCallback } from 'react'
import { Secret, SecretError } from './types'
import { 
  getSecrets, 
  createSecret as createSecret<PERSON><PERSON>, 
  updateSecret as updateSecret<PERSON><PERSON>,
  deleteSecret as deleteSecret<PERSON>pi,
  decryptSecretValue 
} from './api'

export function useSecrets(environmentId: string) {
  const [secrets, setSecrets] = useState<Secret[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<SecretError | null>(null)
  const [decryptedValues, setDecryptedValues] = useState<Map<string, string>>(new Map())

  const fetchSecrets = useCallback(async () => {
    if (!environmentId) return
    
    setLoading(true)
    setError(null)
    
    const { data, error } = await getSecrets(environmentId)
    
    if (error) {
      setError(error)
    } else if (data) {
      setSecrets(data)
    }
    
    setLoading(false)
  }, [environmentId])

  useEffect(() => {
    fetchSecrets()
    // Clear decrypted values when environment changes
    setDecryptedValues(new Map())
  }, [fetchSecrets])

  const createSecret = async (key: string, value: string) => {
    setError(null)
    
    const { data, error } = await createSecretApi({
      environment_id: environmentId,
      key,
      value
    })
    
    if (error) {
      setError(error)
      return { success: false, error }
    }
    
    if (data) {
      // Add the new secret to the list
      setSecrets(prev => [...prev, data].sort((a, b) => a.key.localeCompare(b.key)))
    }
    
    return { success: true, data }
  }

  const updateSecret = async (secretId: string, value: string) => {
    setError(null)
    
    const { data, error } = await updateSecretApi({
      id: secretId,
      value
    })
    
    if (error) {
      setError(error)
      return { success: false, error }
    }
    
    if (data) {
      // Update the secret in the list
      setSecrets(prev => prev.map(s => s.id === secretId ? data : s))
      // Clear the decrypted value so it needs to be re-decrypted
      setDecryptedValues(prev => {
        const newMap = new Map(prev)
        newMap.delete(secretId)
        return newMap
      })
    }
    
    return { success: true, data }
  }

  const deleteSecret = async (secretId: string) => {
    setError(null)
    
    const { success, error } = await deleteSecretApi(secretId)
    
    if (error) {
      setError(error)
      return { success: false, error }
    }
    
    if (success) {
      // Remove the secret from the list
      setSecrets(prev => prev.filter(s => s.id !== secretId))
      // Remove from decrypted values
      setDecryptedValues(prev => {
        const newMap = new Map(prev)
        newMap.delete(secretId)
        return newMap
      })
    }
    
    return { success }
  }

  const decryptSecret = useCallback(async (secret: Secret) => {
    // Check if already decrypted
    if (decryptedValues.has(secret.id)) {
      return decryptedValues.get(secret.id)!
    }

    try {
      const decrypted = await decryptSecretValue(secret)
      // Cache the decrypted value
      setDecryptedValues(prev => new Map(prev).set(secret.id, decrypted))
      return decrypted
    } catch (error) {
      console.error('Failed to decrypt secret:', error)
      throw error
    }
  }, [decryptedValues])

  const getDecryptedValue = (secretId: string): string | undefined => {
    return decryptedValues.get(secretId)
  }

  const clearDecryptedValue = (secretId: string) => {
    setDecryptedValues(prev => {
      const newMap = new Map(prev)
      newMap.delete(secretId)
      return newMap
    })
  }

  return {
    secrets,
    loading,
    error,
    createSecret,
    updateSecret,
    deleteSecret,
    decryptSecret,
    getDecryptedValue,
    clearDecryptedValue,
    refetch: fetchSecrets,
  }
}