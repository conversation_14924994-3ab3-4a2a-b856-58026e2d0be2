import crypto from 'crypto'

const algorithm = 'aes-256-cbc'

// Get encryption key from environment variable
function getEncryptionKey(): Buffer {
  const key = process.env.NEXT_PUBLIC_ENCRYPTION_KEY || process.env.ENCRYPTION_KEY
  
  if (!key) {
    throw new Error('ENCRYPTION_KEY environment variable is not set')
  }
  
  // Ensure key is 32 bytes (64 hex characters)
  if (key.length !== 64) {
    throw new Error('ENCRYPTION_KEY must be 32 bytes (64 hex characters)')
  }
  
  return Buffer.from(key, 'hex')
}

export function encrypt(text: string): { encrypted: string; iv: string } {
  try {
    const key = getEncryptionKey()
    const iv = crypto.randomBytes(16)
    const cipher = crypto.createCipheriv(algorithm, key, iv)
    
    let encrypted = cipher.update(text, 'utf8', 'hex')
    encrypted += cipher.final('hex')
    
    return {
      encrypted,
      iv: iv.toString('hex')
    }
  } catch (error) {
    console.error('Encryption error:', error)
    throw new Error('Failed to encrypt data')
  }
}

export function decrypt(encryptedText: string, ivHex: string): string {
  try {
    const key = getEncryptionKey()
    const iv = Buffer.from(ivHex, 'hex')
    const decipher = crypto.createDecipheriv(algorithm, key, iv)
    
    let decrypted = decipher.update(encryptedText, 'hex', 'utf8')
    decrypted += decipher.final('utf8')
    
    return decrypted
  } catch (error) {
    console.error('Decryption error:', error)
    throw new Error('Failed to decrypt data')
  }
}

// Client-side encryption functions that work in the browser
export async function encryptClient(text: string): Promise<{ encrypted: string; iv: string }> {
  // For client-side, we'll need to make an API call to encrypt
  // This is because crypto.createCipheriv is not available in the browser
  const response = await fetch('/api/crypto/encrypt', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ text }),
  })
  
  if (!response.ok) {
    throw new Error('Failed to encrypt data')
  }
  
  return response.json()
}

export async function decryptClient(encrypted: string, iv: string): Promise<string> {
  // For client-side, we'll need to make an API call to decrypt
  const response = await fetch('/api/crypto/decrypt', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ encrypted, iv }),
  })
  
  if (!response.ok) {
    throw new Error('Failed to decrypt data')
  }
  
  const { decrypted } = await response.json()
  return decrypted
}