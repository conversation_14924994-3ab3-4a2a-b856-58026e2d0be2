export type TeamRole = 'admin' | 'manager' | 'member'

export interface Team {
  id: string
  name: string
  created_at: string
  updated_at: string
}

export interface TeamMember {
  team_id: string
  user_id: string
  role: TeamRole
  created_at: string
}

export interface TeamWithDetails extends Team {
  role: TeamRole
  member_count: number
  project_count: number
}

export interface CreateTeamInput {
  name: string
}

export interface UpdateTeamInput {
  name: string
}

export interface TeamContextType {
  currentTeam: TeamWithDetails | null
  teams: TeamWithDetails[]
  switchTeam: (teamId: string) => Promise<void>
  createTeam: (input: CreateTeamInput) => Promise<Team>
  updateTeam: (teamId: string, input: UpdateTeamInput) => Promise<Team>
  loading: boolean
  error: Error | null
  refetch: () => Promise<void>
}