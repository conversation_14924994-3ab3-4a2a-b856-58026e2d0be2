import { TEAM_ROLES, TEAM_ROLE_LABELS, TEAM_ROLE_COLORS, type TeamRole } from './constants'

export function getRoleLabel(role: string): string {
  if (isValidTeamRole(role)) {
    return TEAM_ROLE_LABELS[role]
  }
  return 'Unknown'
}

export function getRoleColors(role: string): { bg: string; text: string } {
  if (isValidTeamRole(role)) {
    return TEAM_ROLE_COLORS[role]
  }
  return { bg: 'bg-gray-100', text: 'text-gray-700' }
}

export function isValidTeamRole(role: string): role is TeamRole {
  return Object.values(TEAM_ROLES).includes(role as TeamRole)
}

export function canManageTeam(role: string): boolean {
  return role === TEAM_ROLES.ADMIN || role === TEAM_ROLES.MANAGER
}

export function canDeleteTeam(role: string): boolean {
  return role === TEAM_ROLES.ADMIN
}

export function formatMemberCount(count: number): string {
  return `${count} ${count === 1 ? 'member' : 'members'}`
}

export function formatProjectCount(count: number): string {
  return `${count} ${count === 1 ? 'project' : 'projects'}`
}

export function validateTeamName(name: string): { isValid: boolean; error?: string } {
  const trimmed = name.trim()
  
  if (!trimmed) {
    return { isValid: false, error: 'Team name is required' }
  }
  
  if (trimmed.length > 50) {
    return { isValid: false, error: 'Team name must be 50 characters or less' }
  }
  
  return { isValid: true }
}