export const TEAM_ROLES = {
  ADMIN: 'admin',
  MANA<PERSON><PERSON>: 'manager',
  MEMBER: 'member',
} as const

export type TeamRole = typeof TEAM_ROLES[keyof typeof TEAM_ROLES]

export const TEAM_ROLE_LABELS: Record<TeamRole, string> = {
  [TEAM_ROLES.ADMIN]: 'Admin',
  [TEAM_ROLES.MANAGER]: 'Manager',
  [TEAM_ROLES.MEMBER]: 'Member',
}

export const TEAM_ROLE_COLORS: Record<TeamRole, { bg: string; text: string }> = {
  [TEAM_ROLES.ADMIN]: { bg: 'bg-blue-100', text: 'text-blue-700' },
  [TEAM_ROLES.MANAGER]: { bg: 'bg-green-100', text: 'text-green-700' },
  [TEAM_ROLES.MEMBER]: { bg: 'bg-gray-100', text: 'text-gray-700' },
}

export const TEAM_MESSAGES = {
  CREATE_SUCCESS: 'Team created successfully',
  CREATE_ERROR: 'Failed to create team',
  UPDATE_SUCCESS: 'Team updated successfully',
  UPDATE_ERROR: 'Failed to update team',
  DELETE_SUCCESS: 'Team deleted successfully',
  DELETE_ERROR: 'Failed to delete team',
  SWITCH_ERROR: 'Failed to switch team',
  NAME_REQUIRED: 'Team name is required',
  DUPLICATE_NAME: 'You already have a team with this name',
  LAST_TEAM_ERROR: 'You cannot delete your last team',
  NO_PERMISSION: "You don't have permission to manage this team",
  NOT_AUTHENTICATED: 'You must be logged in to perform this action',
} as const

export const TEAM_STORAGE_KEYS = {
  CURRENT_TEAM_ID: 'currentTeamId',
} as const

export const TEAM_LIMITS = {
  MAX_NAME_LENGTH: 50,
  MIN_NAME_LENGTH: 1,
} as const