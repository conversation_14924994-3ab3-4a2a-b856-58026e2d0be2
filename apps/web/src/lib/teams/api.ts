import { createClient } from '@/lib/supabase/client'
import type { TeamWithDetails, CreateTeamInput, UpdateTeamInput } from './types'

export async function getUserTeams(): Promise<TeamWithDetails[]> {
  const supabase = createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) throw new Error('Not authenticated')
  
  // First get the team memberships
  const { data: memberships, error: membershipError } = await supabase
    .from('team_members')
    .select(`
      team_id,
      role,
      created_at,
      teams (
        id,
        name,
        created_at
      )
    `)
    .eq('user_id', user.id)
    .order('created_at', { ascending: true })
    
  if (membershipError) throw membershipError
  
  if (!memberships || memberships.length === 0) {
    return []
  }
  
  // Get member and project counts for each team
  const teamIds = memberships.map(m => m.team_id)
  
  // Get member counts
  const { data: memberCounts } = await supabase
    .from('team_members')
    .select('team_id')
    .in('team_id', teamIds)
  
  // Get project counts
  const { data: projectCounts } = await supabase
    .from('projects')
    .select('team_id')
    .in('team_id', teamIds)
  
  // Map the data to TeamWithDetails format
  const teams: TeamWithDetails[] = memberships.map(membership => {
    const memberCount = memberCounts?.filter(m => m.team_id === membership.team_id).length || 0
    const projectCount = projectCounts?.filter(p => p.team_id === membership.team_id).length || 0
    
    // teams is a nested object, not an array
    const team = membership.teams as unknown as { id: string; name: string; created_at: string } | null
    
    if (!team) {
      throw new Error('Team data missing for membership')
    }
    
    return {
      id: team.id,
      name: team.name,
      created_at: team.created_at,
      updated_at: team.created_at, // teams table doesn't have updated_at, use created_at
      role: membership.role,
      member_count: memberCount,
      project_count: projectCount
    }
  })
  
  return teams
}

export async function createTeam(input: CreateTeamInput): Promise<TeamWithDetails> {
  const supabase = createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) throw new Error('Not authenticated')
  
  // Create the team
  const { data: team, error: teamError } = await supabase
    .from('teams')
    .insert({ name: input.name })
    .select()
    .single()
    
  if (teamError) throw teamError
  
  // The user is automatically added as admin via database trigger
  // Return the team with initial details
  const teamDetails: TeamWithDetails = {
    id: team.id,
    name: team.name,
    created_at: team.created_at,
    updated_at: team.created_at,
    role: 'admin',
    member_count: 1,
    project_count: 0
  }
  
  return teamDetails
}

export async function updateTeam(teamId: string, input: UpdateTeamInput): Promise<TeamWithDetails> {
  const supabase = createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) throw new Error('Not authenticated')
  
  // Check if user is admin
  const { data: member } = await supabase
    .from('team_members')
    .select('role, created_at')
    .eq('team_id', teamId)
    .eq('user_id', user.id)
    .single()
    
  if (!member || member.role !== 'admin') {
    throw new Error('Only team admins can update team settings')
  }
  
  // Update the team
  const { error: updateError } = await supabase
    .from('teams')
    .update({ name: input.name })
    .eq('id', teamId)
    
  if (updateError) throw updateError
  
  // Fetch the updated team
  const { data: team, error: teamError } = await supabase
    .from('teams')
    .select('*')
    .eq('id', teamId)
    .single()
    
  if (teamError) throw teamError
  
  // Get counts
  const { data: memberCount } = await supabase
    .from('team_members')
    .select('id')
    .eq('team_id', teamId)
    
  const { data: projectCount } = await supabase
    .from('projects')
    .select('id')
    .eq('team_id', teamId)
  
  // Return updated team details
  const teamDetails: TeamWithDetails = {
    id: team.id,
    name: team.name,
    created_at: team.created_at,
    updated_at: team.created_at,
    role: member.role,
    member_count: memberCount?.length || 0,
    project_count: projectCount?.length || 0
  }
  
  return teamDetails
}

export async function getUserTeamRole(teamId: string): Promise<string | null> {
  const supabase = createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) return null
  
  const { data, error } = await supabase
    .from('team_members')
    .select('role')
    .eq('team_id', teamId)
    .eq('user_id', user.id)
    .single()
    
  if (error || !data) return null
  return data.role
}

export async function deleteTeam(teamId: string): Promise<{ error?: { message: string } }> {
  const supabase = createClient()
  
  try {
    // Get the current user
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return { error: { message: 'You must be logged in to delete a team' } }
    }

    // Check if user is admin of the team
    const { data: memberData, error: memberError } = await supabase
      .from('team_members')
      .select('role')
      .eq('team_id', teamId)
      .eq('user_id', user.id)
      .single()

    if (memberError || !memberData || memberData.role !== 'admin') {
      return { error: { message: 'Only team admins can delete teams' } }
    }

    // Check if this is the user's last team
    const { data: userTeams, error: teamsError } = await supabase
      .from('team_members')
      .select('team_id')
      .eq('user_id', user.id)
    
    if (teamsError) {
      return { error: { message: 'Failed to check team memberships' } }
    }

    if (!userTeams || userTeams.length === 1) {
      return { error: { message: 'You cannot delete your last team. Every user must belong to at least one team.' } }
    }

    // Use the delete_team_cascade function
    const { error: deleteError } = await supabase.rpc('delete_team_cascade', {
      p_team_id: teamId
    })

    if (deleteError) {
      // If RPC doesn't exist, fall back to direct deletion
      if (deleteError.code === '42883') { // function does not exist
        // Delete the team directly - CASCADE will handle related records
        const { error } = await supabase
          .from('teams')
          .delete()
          .eq('id', teamId)

        if (error) {
          return { error: { message: error.message } }
        }
      } else {
        return { error: { message: deleteError.message } }
      }
    }

    // Switch to another team
    const otherTeamId = userTeams.find(t => t.team_id !== teamId)?.team_id
    if (otherTeamId) {
      localStorage.setItem('currentTeamId', otherTeamId)
    }

    return {}
  } catch (err) {
    console.error('Error deleting team:', err)
    return { error: { message: 'Failed to delete team' } }
  }
}