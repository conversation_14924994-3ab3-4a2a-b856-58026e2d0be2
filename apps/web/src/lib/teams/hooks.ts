'use client'

import { useState, useEffect, useCallback } from 'react'
import { getUserTeams, createTeam as createTeamApi, updateTeam as updateTeamApi } from './api'
import type { TeamWithDetails, CreateTeamInput, UpdateTeamInput, Team } from './types'
import { TEAM_MESSAGES, TEAM_STORAGE_KEYS } from './constants'

export function useTeams() {
  const [teams, setTeams] = useState<TeamWithDetails[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  const fetchTeams = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const data = await getUserTeams()
      setTeams(data)
    } catch (err) {
      setError(err instanceof Error ? err : new Error(TEAM_MESSAGES.UPDATE_ERROR))
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchTeams()
  }, [fetchTeams])

  const createTeam = useCallback(async (input: CreateTeamInput): Promise<Team> => {
    try {
      setError(null)
      const newTeam = await createTeamApi(input)
      await fetchTeams() // Refresh the list
      return newTeam
    } catch (err) {
      const error = err instanceof Error ? err : new Error(TEAM_MESSAGES.CREATE_ERROR)
      setError(error)
      throw error
    }
  }, [fetchTeams])

  const updateTeam = useCallback(async (teamId: string, input: UpdateTeamInput): Promise<Team> => {
    try {
      setError(null)
      const updatedTeam = await updateTeamApi(teamId, input)
      await fetchTeams() // Refresh the list
      return updatedTeam
    } catch (err) {
      const error = err instanceof Error ? err : new Error(TEAM_MESSAGES.UPDATE_ERROR)
      setError(error)
      throw error
    }
  }, [fetchTeams])

  return {
    teams,
    loading,
    error,
    refetch: fetchTeams,
    createTeam,
    updateTeam
  }
}

export function useCurrentTeam() {
  const [currentTeamId, setCurrentTeamId] = useState<string | null>(null)

  useEffect(() => {
    // Load from localStorage
    const savedTeamId = localStorage.getItem(TEAM_STORAGE_KEYS.CURRENT_TEAM_ID)
    if (savedTeamId) {
      setCurrentTeamId(savedTeamId)
    }
  }, [])

  const switchTeam = useCallback((teamId: string) => {
    try {
      setCurrentTeamId(teamId)
      localStorage.setItem(TEAM_STORAGE_KEYS.CURRENT_TEAM_ID, teamId)
      // Navigate to current path without query params to refresh with new team context
      window.location.href = window.location.pathname
    } catch (err) {
      console.error('Failed to switch team:', err)
      throw new Error(TEAM_MESSAGES.SWITCH_ERROR)
    }
  }, [])

  return {
    currentTeamId,
    switchTeam
  }
}