export class TeamError extends <PERSON>rror {
  constructor(
    message: string,
    public code?: string,
    public statusCode?: number
  ) {
    super(message)
    this.name = 'TeamError'
  }
}

export function isTeamError(error: unknown): error is TeamError {
  return error instanceof TeamError
}

export function parseSupabaseError(error: unknown): TeamError {
  // Type guard for error object
  const err = error as { code?: string; message?: string }
  
  // Handle common Supabase error patterns
  if (err?.code === '23505') {
    // Unique constraint violation
    return new TeamError('A team with this name already exists', 'DUPLICATE_NAME', 409)
  }
  
  if (err?.code === '42501') {
    // Insufficient privilege
    return new TeamError('You do not have permission to perform this action', 'NO_PERMISSION', 403)
  }
  
  if (err?.code === 'PGRST116') {
    // No rows returned
    return new TeamError('Team not found', 'NOT_FOUND', 404)
  }
  
  // Default error
  return new TeamError(
    err?.message || 'An unexpected error occurred',
    err?.code,
    (err as { statusCode?: number })?.statusCode || 500
  )
}