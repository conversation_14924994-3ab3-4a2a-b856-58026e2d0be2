import { getUserTeams, createTeam, updateTeam, getUserTeamRole } from '../api'
import { TEAM_MESSAGES } from '../constants'
import { TeamError } from '../errors'

// Mock the Supabase client
jest.mock('@/lib/supabase/client')

describe('Teams API', () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let mockSupabase: any
  
  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>'
  }

  beforeEach(() => {
    jest.clearAllMocks()
    
    // Create a fresh mock for each test
    mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser } })
      },
      from: jest.fn(),
      select: jest.fn(),
      eq: jest.fn(),
      in: jest.fn(),
      order: jest.fn(),
      single: jest.fn(),
      insert: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      rpc: jest.fn()
    }

    // Setup method chaining by default
    const chainableMethods = ['from', 'select', 'eq', 'in', 'order', 'single', 'insert', 'update', 'delete']
    chainableMethods.forEach(method => {
      mockSupabase[method].mockReturnValue(mockSupabase)
    })

    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const { createClient } = require('@/lib/supabase/client')
    ;(createClient as jest.Mock).mockReturnValue(mockSupabase)
  })

  describe('getUserTeams', () => {
    it('should fetch user teams successfully', async () => {
      const mockMemberships = [
        {
          team_id: 'team-1',
          role: 'admin',
          created_at: '2024-01-01',
          teams: {
            id: 'team-1',
            name: 'Team 1',
            created_at: '2024-01-01'
          }
        }
      ]

      const mockMemberCounts = [
        { team_id: 'team-1' },
        { team_id: 'team-1' },
        { team_id: 'team-1' }
      ]

      const mockProjectCounts = [
        { team_id: 'team-1' },
        { team_id: 'team-1' },
        { team_id: 'team-1' },
        { team_id: 'team-1' },
        { team_id: 'team-1' }
      ]

      // Mock the team memberships query
      mockSupabase.order.mockResolvedValueOnce({
        data: mockMemberships,
        error: null
      })

      // Mock subsequent queries
      let fromCallCount = 0
      mockSupabase.from.mockImplementation(() => {
        fromCallCount++
        if (fromCallCount === 2) {
          // Member counts query
          mockSupabase.in.mockReturnValueOnce({
            data: mockMemberCounts
          })
        } else if (fromCallCount === 3) {
          // Project counts query
          mockSupabase.in.mockReturnValueOnce({
            data: mockProjectCounts
          })
        }
        return mockSupabase
      })

      const result = await getUserTeams()

      expect(mockSupabase.from).toHaveBeenCalledWith('team_members')
      expect(result).toHaveLength(1)
      expect(result[0]).toMatchObject({
        id: 'team-1',
        name: 'Team 1',
        role: 'admin',
        member_count: 3,
        project_count: 5
      })
    })

    it('should throw error when user is not authenticated', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({ data: { user: null } })

      await expect(getUserTeams()).rejects.toThrow(TEAM_MESSAGES.NOT_AUTHENTICATED)
    })

    it('should return empty array when user has no teams', async () => {
      mockSupabase.order.mockResolvedValueOnce({
        data: [],
        error: null
      })

      const result = await getUserTeams()
      expect(result).toEqual([])
    })
  })

  describe('createTeam', () => {
    it('should create team successfully', async () => {
      const teamName = 'New Team'
      const newTeam = {
        id: 'new-team-123',
        name: teamName,
        created_at: '2024-01-01'
      }

      mockSupabase.single.mockResolvedValueOnce({
        data: newTeam,
        error: null
      })

      const result = await createTeam({ name: teamName })

      expect(mockSupabase.from).toHaveBeenCalledWith('teams')
      expect(mockSupabase.insert).toHaveBeenCalledWith({ name: teamName })
      expect(result).toMatchObject({
        id: newTeam.id,
        name: newTeam.name,
        role: 'admin',
        member_count: 1,
        project_count: 0
      })
    })

    it('should throw error for duplicate team name', async () => {
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { code: '23505' }
      })

      await expect(createTeam({ name: 'Duplicate' })).rejects.toThrow(TeamError)
    })
  })

  describe('updateTeam', () => {
    it('should update team successfully', async () => {
      const teamId = 'team-123'
      const updatedName = 'Updated Team'

      // Track call sequence
      let callSequence = 0
      
      // Override methods to handle the specific call sequence
      mockSupabase.from.mockImplementation(() => {
        callSequence++
        return mockSupabase
      })

      mockSupabase.single.mockImplementation(() => {
        if (callSequence === 1) {
          // First single() call - member check
          return Promise.resolve({
            data: { role: 'admin', created_at: '2024-01-01' },
            error: null
          })
        } else if (callSequence === 3) {
          // Second single() call - team fetch
          return Promise.resolve({
            data: { id: teamId, name: updatedName, created_at: '2024-01-01' },
            error: null
          })
        }
        return mockSupabase
      })

      // Mock the update operation
      mockSupabase.eq.mockImplementation(() => {
        if (callSequence === 2) {
          // After update().eq()
          return { error: null }
        }
        return mockSupabase
      })

      // Mock counts - eq returns the data directly for count queries
      mockSupabase.eq.mockImplementation(() => {
        if (callSequence === 2) {
          // After update().eq()
          return { error: null }
        } else if (callSequence === 4) {
          // Member count query
          return {
            data: [1, 2, 3]
          }
        } else if (callSequence === 5) {
          // Project count query
          return {
            data: [1, 2, 3, 4, 5]
          }
        }
        return mockSupabase
      })

      const result = await updateTeam(teamId, { name: updatedName })

      expect(mockSupabase.from).toHaveBeenCalledWith('teams')
      expect(mockSupabase.update).toHaveBeenCalledWith({ name: updatedName })
      expect(result.name).toBe(updatedName)
    })

    it('should throw error when user is not admin', async () => {
      mockSupabase.single.mockResolvedValueOnce({
        data: { role: 'member' },
        error: null
      })

      await expect(updateTeam('team-123', { name: 'New Name' })).rejects.toThrow(
        TEAM_MESSAGES.NO_PERMISSION
      )
    })
  })

  describe('getUserTeamRole', () => {
    it('should return user role in team', async () => {
      const teamId = 'team-123'
      const role = 'admin'

      mockSupabase.single.mockResolvedValueOnce({
        data: { role },
        error: null
      })

      const result = await getUserTeamRole(teamId)

      expect(mockSupabase.from).toHaveBeenCalledWith('team_members')
      expect(mockSupabase.eq).toHaveBeenCalledWith('team_id', teamId)
      expect(mockSupabase.eq).toHaveBeenCalledWith('user_id', mockUser.id)
      expect(result).toBe(role)
    })

    it('should return null when user is not in team', async () => {
      mockSupabase.single.mockResolvedValueOnce({
        data: null,
        error: { code: 'PGRST116' } // No rows returned
      })

      const result = await getUserTeamRole('team-123')
      expect(result).toBeNull()
    })

    it('should return null when user is not authenticated', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({ data: { user: null } })

      const result = await getUserTeamRole('team-123')
      expect(result).toBeNull()
    })
  })
})