import { getUserTeams, createTeam, updateTeam, getUserTeamRole } from '../api'

// Mock the Supabase client
jest.mock('@/lib/supabase/client')

describe('Teams API', () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const mockSupabase: any = {
    auth: {
      getUser: jest.fn()
    },
    from: jest.fn(),
    select: jest.fn(),
    eq: jest.fn(),
    order: jest.fn(),
    single: jest.fn(),
    insert: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    rpc: jest.fn()
  }

  // Set up method chaining
  mockSupabase.from.mockReturnValue(mockSupabase)
  mockSupabase.select.mockReturnValue(mockSupabase)
  mockSupabase.eq.mockReturnValue(mockSupabase)
  mockSupabase.order.mockReturnValue(mockSupabase)
  mockSupabase.single.mockReturnValue(mockSupabase)
  mockSupabase.insert.mockReturnValue(mockSupabase)
  mockSupabase.update.mockReturnValue(mockSupabase)
  mockSupabase.delete.mockReturnValue(mockSupabase)

  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>'
  }

  beforeEach(() => {
    jest.clearAllMocks()
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const { createClient } = require('@/lib/supabase/client')
    ;(createClient as jest.Mock).mockReturnValue(mockSupabase)
    mockSupabase.auth.getUser.mockResolvedValue({ data: { user: mockUser } })
  })

  describe('getUserTeams', () => {
    it('should fetch user teams successfully', async () => {
      const mockTeams = [
        {
          id: 'team-1',
          name: 'Team 1',
          role: 'admin',
          member_count: 3,
          project_count: 5,
          created_at: '2024-01-01',
          updated_at: '2024-01-01'
        }
      ]

      mockSupabase.order.mockResolvedValue({
        data: mockTeams,
        error: null
      })

      const result = await getUserTeams()

      expect(mockSupabase.from).toHaveBeenCalledWith('team_details')
      expect(mockSupabase.select).toHaveBeenCalledWith('*')
      expect(mockSupabase.eq).toHaveBeenCalledWith('user_id', mockUser.id)
      expect(result).toEqual(mockTeams)
    })

    it('should throw error when user is not authenticated', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({ data: { user: null } })

      await expect(getUserTeams()).rejects.toThrow('User not authenticated')
    })

    it('should throw error when database query fails', async () => {
      mockSupabase.order.mockResolvedValue({
        data: null,
        error: { message: 'Database error' }
      })

      await expect(getUserTeams()).rejects.toThrow('Failed to fetch teams: Database error')
    })
  })

  describe('createTeam', () => {
    it('should create team successfully', async () => {
      const newTeamId = 'new-team-123'
      const teamName = 'New Team'

      mockSupabase.rpc.mockResolvedValue({
        data: newTeamId,
        error: null
      })

      mockSupabase.single.mockResolvedValue({
        data: { id: newTeamId, name: teamName },
        error: null
      })

      const result = await createTeam({ name: teamName })

      expect(mockSupabase.rpc).toHaveBeenCalledWith('create_team_with_admin', {
        team_name: teamName,
        admin_user_id: mockUser.id
      })
      expect(result).toEqual({ id: newTeamId, name: teamName })
    })

    it('should throw error for duplicate team name', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: { message: 'already have a team with this name' }
      })

      await expect(createTeam({ name: 'Duplicate' })).rejects.toThrow(
        'You already have a team with this name'
      )
    })
  })

  describe('updateTeam', () => {
    it('should update team successfully', async () => {
      const teamId = 'team-123'
      const updatedName = 'Updated Team'

      mockSupabase.single.mockResolvedValue({
        data: { id: teamId, name: updatedName },
        error: null
      })

      const result = await updateTeam(teamId, { name: updatedName })

      expect(mockSupabase.from).toHaveBeenCalledWith('teams')
      expect(mockSupabase.update).toHaveBeenCalledWith({
        name: updatedName,
        updated_at: expect.any(String)
      })
      expect(result).toEqual({ id: teamId, name: updatedName })
    })
  })

  describe('getUserTeamRole', () => {
    it('should return user role in team', async () => {
      const teamId = 'team-123'
      const role = 'admin'

      mockSupabase.rpc.mockResolvedValue({
        data: role,
        error: null
      })

      const result = await getUserTeamRole(teamId)

      expect(mockSupabase.rpc).toHaveBeenCalledWith('get_user_team_role', {
        check_team_id: teamId,
        check_user_id: mockUser.id
      })
      expect(result).toBe(role)
    })

    it('should return null when user is not in team', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: null
      })

      const result = await getUserTeamRole('team-123')
      expect(result).toBeNull()
    })
  })
})