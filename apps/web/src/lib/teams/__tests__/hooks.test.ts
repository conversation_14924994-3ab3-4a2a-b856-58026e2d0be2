import { renderHook, act, waitFor } from '@testing-library/react'
import { useTeams, useCurrentTeam } from '../hooks'
import * as api from '../api'

// Mock the API module
jest.mock('../api')

describe('Team Hooks', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Clear localStorage
    localStorage.clear()
  })

  describe('useTeams', () => {
    const mockTeams = [
      {
        id: 'team-1',
        name: 'Team 1',
        role: 'admin' as const,
        member_count: 3,
        project_count: 5,
        created_at: '2024-01-01',
        updated_at: '2024-01-01'
      },
      {
        id: 'team-2',
        name: 'Team 2',
        role: 'member' as const,
        member_count: 10,
        project_count: 8,
        created_at: '2024-01-02',
        updated_at: '2024-01-02'
      }
    ]

    it('should fetch teams on mount', async () => {
      (api.getUserTeams as jest.Mock).mockResolvedValue(mockTeams)

      const { result } = renderHook(() => useTeams())

      expect(result.current.loading).toBe(true)
      expect(result.current.teams).toEqual([])

      await waitFor(() => {
        expect(result.current.loading).toBe(false)
      })

      expect(result.current.teams).toEqual(mockTeams)
      expect(result.current.error).toBeNull()
      expect(api.getUserTeams).toHaveBeenCalledWith(false)
    })

    it('should handle fetch error', async () => {
      const error = new Error('Failed to fetch teams')
      ;(api.getUserTeams as jest.Mock).mockRejectedValue(error)

      const { result } = renderHook(() => useTeams())

      await waitFor(() => {
        expect(result.current.loading).toBe(false)
      })

      expect(result.current.teams).toEqual([])
      expect(result.current.error).toEqual(error)
    })

    it('should create team and refresh list', async () => {
      const newTeam = { id: 'team-3', name: 'New Team' }
      ;(api.getUserTeams as jest.Mock).mockResolvedValue(mockTeams)
      ;(api.createTeam as jest.Mock).mockResolvedValue(newTeam)

      const { result } = renderHook(() => useTeams())

      await waitFor(() => {
        expect(result.current.loading).toBe(false)
      })

      let createdTeam
      await act(async () => {
        createdTeam = await result.current.createTeam({ name: 'New Team' })
      })

      expect(createdTeam).toEqual(newTeam)
      expect(api.createTeam).toHaveBeenCalledWith({ name: 'New Team' }, false)
      expect(api.getUserTeams).toHaveBeenCalledTimes(2) // Initial fetch + refresh
    })

    it('should handle create team error', async () => {
      const error = new Error('Failed to create team')
      ;(api.getUserTeams as jest.Mock).mockResolvedValue(mockTeams)
      ;(api.createTeam as jest.Mock).mockRejectedValue(error)

      const { result } = renderHook(() => useTeams())

      await waitFor(() => {
        expect(result.current.loading).toBe(false)
      })

      await expect(
        act(async () => {
          await result.current.createTeam({ name: 'New Team' })
        })
      ).rejects.toThrow(error)

      expect(result.current.error).toEqual(error)
    })

    it('should update team and refresh list', async () => {
      const updatedTeam = { id: 'team-1', name: 'Updated Team' }
      ;(api.getUserTeams as jest.Mock).mockResolvedValue(mockTeams)
      ;(api.updateTeam as jest.Mock).mockResolvedValue(updatedTeam)

      const { result } = renderHook(() => useTeams())

      await waitFor(() => {
        expect(result.current.loading).toBe(false)
      })

      let updated
      await act(async () => {
        updated = await result.current.updateTeam('team-1', { name: 'Updated Team' })
      })

      expect(updated).toEqual(updatedTeam)
      expect(api.updateTeam).toHaveBeenCalledWith('team-1', { name: 'Updated Team' }, false)
      expect(api.getUserTeams).toHaveBeenCalledTimes(2)
    })
  })

  describe('useCurrentTeam', () => {
    it('should load team ID from localStorage', () => {
      const savedTeamId = 'saved-team-123'
      localStorage.setItem('currentTeamId', savedTeamId)

      const { result } = renderHook(() => useCurrentTeam())

      expect(result.current.currentTeamId).toBe(savedTeamId)
    })

    it('should have null team ID when localStorage is empty', () => {
      const { result } = renderHook(() => useCurrentTeam())

      expect(result.current.currentTeamId).toBeNull()
    })

    it('should switch team and save to localStorage', () => {
      const originalReload = window.location.reload
      window.location.reload = jest.fn()

      const { result } = renderHook(() => useCurrentTeam())

      act(() => {
        result.current.switchTeam('new-team-123')
      })

      expect(localStorage.getItem('currentTeamId')).toBe('new-team-123')
      expect(window.location.reload).toHaveBeenCalled()

      window.location.reload = originalReload
    })
  })
})