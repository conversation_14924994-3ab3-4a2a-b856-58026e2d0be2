'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { getUserTeams, createTeam as createTeam<PERSON>pi, updateTeam as updateTeamApi } from './api'
import type { TeamWithDetails, CreateTeamInput, UpdateTeamInput, Team } from './types'
import { TEAM_MESSAGES, TEAM_STORAGE_KEYS } from './constants'

// Simple cache implementation
const teamsCache = {
  data: null as TeamWithDetails[] | null,
  timestamp: 0,
  CACHE_DURATION: 5 * 60 * 1000 // 5 minutes
}

export function useTeamsOptimized() {
  const [teams, setTeams] = useState<TeamWithDetails[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const abortControllerRef = useRef<AbortController | null>(null)

  const fetchTeams = useCallback(async (force = false) => {
    // Check cache first
    if (!force && teamsCache.data && Date.now() - teamsCache.timestamp < teamsCache.CACHE_DURATION) {
      setTeams(teamsCache.data)
      setLoading(false)
      return
    }

    // Cancel any in-flight requests
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    // Create new abort controller for this request
    abortControllerRef.current = new AbortController()

    try {
      setLoading(true)
      setError(null)
      
      const data = await getUserTeams()
      
      // Update cache
      teamsCache.data = data
      teamsCache.timestamp = Date.now()
      
      setTeams(data)
    } catch (err) {
      // Ignore abort errors
      if (err instanceof Error && err.name === 'AbortError') {
        return
      }
      setError(err instanceof Error ? err : new Error(TEAM_MESSAGES.UPDATE_ERROR))
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchTeams()

    // Cleanup function
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [fetchTeams])

  const createTeam = useCallback(async (input: CreateTeamInput): Promise<Team> => {
    try {
      setError(null)
      const newTeam = await createTeamApi(input)
      
      // Invalidate cache
      teamsCache.data = null
      await fetchTeams(true)
      
      return newTeam
    } catch (err) {
      const error = err instanceof Error ? err : new Error(TEAM_MESSAGES.CREATE_ERROR)
      setError(error)
      throw error
    }
  }, [fetchTeams])

  const updateTeam = useCallback(async (teamId: string, input: UpdateTeamInput): Promise<Team> => {
    try {
      setError(null)
      const updatedTeam = await updateTeamApi(teamId, input)
      
      // Optimistically update local state
      setTeams(prev => prev.map(team => 
        team.id === teamId ? { ...team, ...input } : team
      ))
      
      // Invalidate cache and refetch
      teamsCache.data = null
      await fetchTeams(true)
      
      return updatedTeam
    } catch (err) {
      // Revert optimistic update on error
      await fetchTeams(true)
      
      const error = err instanceof Error ? err : new Error(TEAM_MESSAGES.UPDATE_ERROR)
      setError(error)
      throw error
    }
  }, [fetchTeams])

  return {
    teams,
    loading,
    error,
    refetch: () => fetchTeams(true),
    createTeam,
    updateTeam
  }
}

export function useCurrentTeamOptimized() {
  const [currentTeamId, setCurrentTeamId] = useState<string | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)

  useEffect(() => {
    // Load from localStorage only once
    if (!isInitialized) {
      const savedTeamId = localStorage.getItem(TEAM_STORAGE_KEYS.CURRENT_TEAM_ID)
      if (savedTeamId) {
        setCurrentTeamId(savedTeamId)
      }
      setIsInitialized(true)
    }
  }, [isInitialized])

  const switchTeam = useCallback((teamId: string) => {
    try {
      // Update state immediately for responsive UI
      setCurrentTeamId(teamId)
      localStorage.setItem(TEAM_STORAGE_KEYS.CURRENT_TEAM_ID, teamId)
      
      // Use requestIdleCallback for non-critical navigation
      if ('requestIdleCallback' in window) {
        requestIdleCallback(() => {
          window.location.href = window.location.pathname
        })
      } else {
        // Fallback for browsers that don't support requestIdleCallback
        setTimeout(() => {
          window.location.href = window.location.pathname
        }, 0)
      }
    } catch (err) {
      console.error('Failed to switch team:', err)
      throw new Error(TEAM_MESSAGES.SWITCH_ERROR)
    }
  }, [])

  return {
    currentTeamId,
    switchTeam,
    isReady: isInitialized
  }
}