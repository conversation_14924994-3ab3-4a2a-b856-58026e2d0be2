import { TeamR<PERSON> } from '@/lib/teams/types'
import { 
  Permission, 
  PERMISSIONS, 
  ROLE_PERMISSIONS, 
  PermissionSet 
} from './types'

/**
 * Check if a role has a specific permission
 */
export function hasPermission(
  role: TeamRole | null | undefined, 
  permission: Permission
): boolean {
  if (!role) return false
  return ROLE_PERMISSIONS[role]?.includes(permission) ?? false
}

/**
 * Check if a role has all specified permissions
 */
export function hasAllPermissions(
  role: TeamR<PERSON> | null | undefined,
  permissions: Permission[]
): boolean {
  if (!role) return false
  return permissions.every(permission => hasPermission(role, permission))
}

/**
 * Check if a role has any of the specified permissions
 */
export function hasAnyPermission(
  role: TeamRole | null | undefined,
  permissions: Permission[]
): boolean {
  if (!role) return false
  return permissions.some(permission => hasPermission(role, permission))
}

/**
 * Get all permissions for a role
 */
export function getRolePermissions(role: TeamRole | null | undefined): Permission[] {
  if (!role) return []
  return ROLE_PERMISSIONS[role] ?? []
}

/**
 * Get a complete permission set for a role
 */
export function getPermissionSet(role: TeamRole | null | undefined): PermissionSet {
  if (!role) {
    // Return all false for no role
    return {
      canViewProjects: false,
      canCreateProject: false,
      canDeleteProject: false,
      canViewEnvironments: false,
      canCreateEnvironment: false,
      canDeleteEnvironment: false,
      canViewSecrets: false,
      canCreateSecret: false,
      canEditSecret: false,
      canDeleteSecret: false,
      canExportSecrets: false,
      canImportSecrets: false,
      canViewTeam: false,
      canManageTeam: false,
      canInviteMembers: false,
      canRemoveMembers: false,
      canEditMemberRoles: false,
      canGenerateApiKey: false,
      canViewApiKeys: false,
      canDeleteApiKey: false,
      role: 'member'
    }
  }

  return {
    canViewProjects: hasPermission(role, PERMISSIONS.PROJECT_VIEW),
    canCreateProject: hasPermission(role, PERMISSIONS.PROJECT_CREATE),
    canDeleteProject: hasPermission(role, PERMISSIONS.PROJECT_DELETE),
    canViewEnvironments: hasPermission(role, PERMISSIONS.ENVIRONMENT_VIEW),
    canCreateEnvironment: hasPermission(role, PERMISSIONS.ENVIRONMENT_CREATE),
    canDeleteEnvironment: hasPermission(role, PERMISSIONS.ENVIRONMENT_DELETE),
    canViewSecrets: hasPermission(role, PERMISSIONS.SECRET_VIEW),
    canCreateSecret: hasPermission(role, PERMISSIONS.SECRET_CREATE),
    canEditSecret: hasPermission(role, PERMISSIONS.SECRET_EDIT),
    canDeleteSecret: hasPermission(role, PERMISSIONS.SECRET_DELETE),
    canExportSecrets: hasPermission(role, PERMISSIONS.SECRET_EXPORT),
    canImportSecrets: hasPermission(role, PERMISSIONS.SECRET_IMPORT),
    canViewTeam: hasPermission(role, PERMISSIONS.TEAM_VIEW),
    canManageTeam: hasPermission(role, PERMISSIONS.TEAM_MANAGE),
    canInviteMembers: hasPermission(role, PERMISSIONS.MEMBER_INVITE),
    canRemoveMembers: hasPermission(role, PERMISSIONS.MEMBER_REMOVE),
    canEditMemberRoles: hasPermission(role, PERMISSIONS.MEMBER_EDIT_ROLE),
    canGenerateApiKey: hasPermission(role, PERMISSIONS.API_KEY_GENERATE),
    canViewApiKeys: hasPermission(role, PERMISSIONS.API_KEY_VIEW),
    canDeleteApiKey: hasPermission(role, PERMISSIONS.API_KEY_DELETE),
    role: role
  }
}

/**
 * Get a user-friendly message for a permission
 */
export function getPermissionMessage(permission: Permission): string {
  const messages: Record<Permission, string> = {
    [PERMISSIONS.PROJECT_VIEW]: 'view projects',
    [PERMISSIONS.PROJECT_CREATE]: 'create projects',
    [PERMISSIONS.PROJECT_DELETE]: 'delete projects',
    [PERMISSIONS.ENVIRONMENT_VIEW]: 'view environments',
    [PERMISSIONS.ENVIRONMENT_CREATE]: 'create environments',
    [PERMISSIONS.ENVIRONMENT_DELETE]: 'delete environments',
    [PERMISSIONS.SECRET_VIEW]: 'view secrets',
    [PERMISSIONS.SECRET_CREATE]: 'create secrets',
    [PERMISSIONS.SECRET_EDIT]: 'edit secrets',
    [PERMISSIONS.SECRET_DELETE]: 'delete secrets',
    [PERMISSIONS.SECRET_EXPORT]: 'export secrets',
    [PERMISSIONS.SECRET_IMPORT]: 'import secrets',
    [PERMISSIONS.TEAM_VIEW]: 'view team',
    [PERMISSIONS.TEAM_MANAGE]: 'manage team',
    [PERMISSIONS.MEMBER_INVITE]: 'invite team members',
    [PERMISSIONS.MEMBER_REMOVE]: 'remove team members',
    [PERMISSIONS.MEMBER_EDIT_ROLE]: 'edit member roles',
    [PERMISSIONS.API_KEY_GENERATE]: 'generate API keys',
    [PERMISSIONS.API_KEY_VIEW]: 'view API keys',
    [PERMISSIONS.API_KEY_DELETE]: 'delete API keys'
  }
  
  return messages[permission] ?? 'perform this action'
}

/**
 * Get the minimum role required for a permission
 */
export function getRequiredRole(permission: Permission): TeamRole | null {
  // Check from most restrictive to least restrictive
  if (ROLE_PERMISSIONS.member.includes(permission)) return 'member'
  if (ROLE_PERMISSIONS.manager.includes(permission)) return 'manager'
  if (ROLE_PERMISSIONS.admin.includes(permission)) return 'admin'
  return null
}

/**
 * Get a message explaining why a user can't perform an action
 */
export function getPermissionDeniedMessage(
  permission: Permission,
  currentRole?: TeamRole
): string {
  const requiredRole = getRequiredRole(permission)
  const action = getPermissionMessage(permission)
  
  if (!requiredRole) {
    return `You don't have permission to ${action}`
  }
  
  if (requiredRole === 'admin') {
    return `Only team admins can ${action}`
  }
  
  if (requiredRole === 'manager') {
    if (currentRole === 'member') {
      return `Only managers and admins can ${action}`
    }
  }
  
  return `You need ${requiredRole} permissions to ${action}`
}

// Re-export types and constants
export { PERMISSIONS } from './types'
export type { Permission, PermissionSet } from './types'