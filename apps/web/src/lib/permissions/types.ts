import { Team<PERSON><PERSON> } from '@/lib/teams/types'

export const PERMISSIONS = {
  // Project permissions
  PROJECT_VIEW: 'project:view',
  PROJECT_CREATE: 'project:create',
  PROJECT_DELETE: 'project:delete',
  
  // Environment permissions
  ENVIRONMENT_VIEW: 'environment:view',
  ENVIRONMENT_CREATE: 'environment:create',
  ENVIRONMENT_DELETE: 'environment:delete',
  
  // Secret permissions
  SECRET_VIEW: 'secret:view',
  SECRET_CREATE: 'secret:create',
  SECRET_EDIT: 'secret:edit',
  SECRET_DELETE: 'secret:delete',
  SECRET_EXPORT: 'secret:export',
  SECRET_IMPORT: 'secret:import',
  
  // Team permissions
  TEAM_VIEW: 'team:view',
  TEAM_MANAGE: 'team:manage',
  MEMBER_INVITE: 'member:invite',
  MEMBER_REMOVE: 'member:remove',
  MEMBER_EDIT_ROLE: 'member:edit_role',
  
  // API key permissions
  API_KEY_GENERATE: 'api_key:generate',
  API_KEY_VIEW: 'api_key:view',
  API_KEY_DELETE: 'api_key:delete'
} as const

export type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS]

export interface PermissionSet {
  canViewProjects: boolean
  canCreateProject: boolean
  canDeleteProject: boolean
  canViewEnvironments: boolean
  canCreateEnvironment: boolean
  canDeleteEnvironment: boolean
  canViewSecrets: boolean
  canCreateSecret: boolean
  canEditSecret: boolean
  canDeleteSecret: boolean
  canExportSecrets: boolean
  canImportSecrets: boolean
  canViewTeam: boolean
  canManageTeam: boolean
  canInviteMembers: boolean
  canRemoveMembers: boolean
  canEditMemberRoles: boolean
  canGenerateApiKey: boolean
  canViewApiKeys: boolean
  canDeleteApiKey: boolean
  role: TeamRole
}

// Role-based permission mapping
export const ROLE_PERMISSIONS: Record<TeamRole, Permission[]> = {
  admin: Object.values(PERMISSIONS), // Admins have all permissions
  manager: [
    PERMISSIONS.PROJECT_VIEW,
    PERMISSIONS.ENVIRONMENT_VIEW,
    PERMISSIONS.SECRET_VIEW,
    PERMISSIONS.SECRET_CREATE,
    PERMISSIONS.SECRET_EDIT,
    PERMISSIONS.SECRET_DELETE,
    PERMISSIONS.SECRET_EXPORT,
    PERMISSIONS.SECRET_IMPORT,
    PERMISSIONS.TEAM_VIEW,
    PERMISSIONS.API_KEY_GENERATE,
    PERMISSIONS.API_KEY_VIEW,
    PERMISSIONS.API_KEY_DELETE
  ],
  member: [
    PERMISSIONS.PROJECT_VIEW,
    PERMISSIONS.ENVIRONMENT_VIEW,
    PERMISSIONS.SECRET_VIEW,
    PERMISSIONS.SECRET_EXPORT,
    PERMISSIONS.TEAM_VIEW,
    PERMISSIONS.API_KEY_GENERATE,
    PERMISSIONS.API_KEY_VIEW,
    PERMISSIONS.API_KEY_DELETE
  ]
}