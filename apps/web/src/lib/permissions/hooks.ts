'use client'

import { useMemo } from 'react'
import { useTeamContext } from '@/contexts/TeamContext'
import { getPermissionSet, hasPermission } from './index'
import type { Permission, PermissionSet } from './types'

/**
 * Hook to get the current user's permissions in the current team context
 */
export function usePermissions(): PermissionSet & {
  checkPermission: (permission: Permission) => boolean
} {
  const { currentTeam } = useTeamContext()
  
  const permissions = useMemo(() => {
    const role = currentTeam?.role ?? null
    const permissionSet = getPermissionSet(role)
    
    return {
      ...permissionSet,
      checkPermission: (permission: Permission) => hasPermission(role, permission)
    }
  }, [currentTeam?.role])
  
  return permissions
}

/**
 * Hook to check a specific permission
 */
export function useHasPermission(permission: Permission): boolean {
  const { currentTeam } = useTeamContext()
  
  return useMemo(() => {
    return hasPermission(currentTeam?.role, permission)
  }, [currentTeam?.role, permission])
}

/**
 * Hook to check multiple permissions
 */
export function useHasAllPermissions(permissions: Permission[]): boolean {
  const { currentTeam } = useTeamContext()
  
  return useMemo(() => {
    if (!currentTeam?.role) return false
    return permissions.every(p => hasPermission(currentTeam.role, p))
  }, [currentTeam?.role, permissions])
}

/**
 * Hook to check if user has any of the specified permissions
 */
export function useHasAnyPermission(permissions: Permission[]): boolean {
  const { currentTeam } = useTeamContext()
  
  return useMemo(() => {
    if (!currentTeam?.role) return false
    return permissions.some(p => hasPermission(currentTeam.role, p))
  }, [currentTeam?.role, permissions])
}