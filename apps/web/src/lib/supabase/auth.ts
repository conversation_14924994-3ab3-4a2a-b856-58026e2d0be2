import { createClient } from './client'

interface AuthError {
  message: string
  status?: number
}

export async function signUp(email: string, password: string): Promise<{ success: boolean; error?: AuthError }> {
  const supabase = createClient()
  
  const { error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: `${window.location.origin}/auth/callback`,
    },
  })

  if (error) {
    return {
      success: false,
      error: {
        message: mapAuthError(error.message),
        status: error.status,
      },
    }
  }

  return { success: true }
}

export async function signIn(email: string, password: string): Promise<{ success: boolean; error?: AuthError }> {
  const supabase = createClient()
  
  const { error } = await supabase.auth.signInWithPassword({
    email,
    password,
  })

  if (error) {
    return {
      success: false,
      error: {
        message: mapAuthError(error.message),
        status: error.status,
      },
    }
  }

  return { success: true }
}

export async function signOut(): Promise<void> {
  const supabase = createClient()
  await supabase.auth.signOut()
}

export async function getSession() {
  const supabase = createClient()
  const { data: { session } } = await supabase.auth.getSession()
  return session
}

export async function getUser() {
  const supabase = createClient()
  const { data: { user } } = await supabase.auth.getUser()
  return user
}

// Map Supabase error messages to user-friendly messages
function mapAuthError(error: string): string {
  const errorMap: Record<string, string> = {
    'Invalid email': 'Please enter a valid email address',
    'Password should be at least 6 characters': 'Password must be at least 6 characters',
    'User already registered': 'An account with this email already exists',
    'Invalid login credentials': 'Invalid email or password',
    'Email not confirmed': 'Please verify your email before logging in',
    'Too many requests': 'Too many attempts. Please try again later',
  }

  // Check if error message contains any of our mapped errors
  for (const [key, value] of Object.entries(errorMap)) {
    if (error.toLowerCase().includes(key.toLowerCase())) {
      return value
    }
  }

  // Network error check
  if (error.toLowerCase().includes('network') || error.toLowerCase().includes('fetch')) {
    return 'Unable to connect. Please check your internet connection'
  }

  // Default error message
  return error || 'An error occurred. Please try again'
}