import { createClient } from '@/lib/supabase/client'
import type { 
  TeamInvitation, 
  CreateInvitationInput, 
  InvitationWithInviter,
  AcceptInvitationResult,
  InvitationError 
} from './types'

export async function createInvitation(input: CreateInvitationInput): Promise<TeamInvitation> {
  const supabase = createClient()
  
  const { data: userData } = await supabase.auth.getUser()
  if (!userData?.user) {
    throw new Error('User not authenticated')
  }

  // Validate email
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(input.email)) {
    const error: InvitationError = { 
      message: 'Please enter a valid email address',
      code: 'INVALID_EMAIL'
    }
    throw error
  }

  // Check if user can be invited
  const { data: canInvite, error: checkError } = await supabase
    .rpc('can_invite_to_team', {
      check_team_id: input.teamId,
      check_email: input.email.toLowerCase()
    })

  if (checkError) {
    throw new Error(`Failed to check invitation eligibility: ${checkError.message}`)
  }

  if (!canInvite) {
    // Determine specific reason
    // Check if user is already a member by checking team_members
    // We'll need to handle this differently since we can't join with auth.users
    const { data: existingMember } = await supabase
      .from('team_members_with_email')
      .select('user_id')
      .eq('team_id', input.teamId)
      .eq('user_email', input.email.toLowerCase())
      .single()

    if (existingMember) {
      const error: InvitationError = { 
        message: 'This user is already a member of the team',
        code: 'ALREADY_MEMBER'
      }
      throw error
    } else {
      const error: InvitationError = { 
        message: 'An invitation has already been sent to this email',
        code: 'INVITATION_EXISTS'
      }
      throw error
    }
  }

  // Generate token
  const { data: token, error: tokenError } = await supabase
    .rpc('generate_invitation_token')

  if (tokenError || !token) {
    throw new Error('Failed to generate invitation token')
  }

  // Create invitation
  const { data, error } = await supabase
    .from('team_invitations')
    .insert({
      team_id: input.teamId,
      email: input.email.toLowerCase(),
      role: input.role,
      token,
      invited_by: userData.user.id
    })
    .select()
    .single()

  if (error) {
    throw new Error(`Failed to create invitation: ${error.message}`)
  }

  // TODO: Send invitation email via Supabase Edge Function
  console.log('Invitation created, email sending not yet implemented', data)

  return data
}

export async function getTeamInvitations(teamId: string): Promise<InvitationWithInviter[]> {
  const supabase = createClient()

  const { data, error } = await supabase
    .from('team_invitations')
    .select('*')
    .eq('team_id', teamId)
    .order('created_at', { ascending: false })

  if (error) {
    throw new Error(`Failed to fetch invitations: ${error.message}`)
  }

  // We can't join with auth.users, so invitations won't have inviter email
  // In a real app, you'd create a function or view to safely expose this
  return (data || []).map(inv => ({
    ...inv,
    inviter: undefined
  })) as InvitationWithInviter[]
}

export async function cancelInvitation(invitationId: string): Promise<void> {
  const supabase = createClient()

  const { error } = await supabase
    .from('team_invitations')
    .delete()
    .eq('id', invitationId)
    .is('accepted_at', null)

  if (error) {
    throw new Error(`Failed to cancel invitation: ${error.message}`)
  }
}

export async function resendInvitation(invitationId: string): Promise<void> {
  const supabase = createClient()

  // Update expiry to extend by 7 days
  const { data, error } = await supabase
    .from('team_invitations')
    .update({
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
    })
    .eq('id', invitationId)
    .is('accepted_at', null)
    .select()
    .single()

  if (error) {
    if (error.message.includes('multiple (or no) rows returned')) {
      throw new Error('Invitation not found. It may have been cancelled or expired.')
    }
    throw new Error(`Failed to resend invitation: ${error.message}`)
  }

  // TODO: Resend invitation email
  console.log('Invitation extended, email resending not yet implemented', data)
}

export async function acceptInvitation(token: string): Promise<AcceptInvitationResult> {
  const supabase = createClient()
  
  const { data: userData } = await supabase.auth.getUser()
  if (!userData?.user) {
    throw new Error('User not authenticated')
  }

  const { data, error } = await supabase
    .rpc('accept_team_invitation', {
      invitation_token: token,
      accepting_user_id: userData.user.id
    })

  if (error) {
    if (error.message.includes('Invalid or expired')) {
      const invError: InvitationError = {
        message: 'This invitation is invalid or has expired',
        code: 'EXPIRED'
      }
      throw invError
    }
    throw new Error(`Failed to accept invitation: ${error.message}`)
  }

  if (!data || data.length === 0) {
    const invError: InvitationError = {
      message: 'Invalid invitation',
      code: 'INVALID_TOKEN'
    }
    throw invError
  }

  return data[0]
}

export async function getInvitationByToken(token: string): Promise<TeamInvitation | null> {
  const supabase = createClient()

  const { data, error } = await supabase
    .from('team_invitations')
    .select('*')
    .eq('token', token)
    .single()

  if (error || !data) {
    return null
  }

  return data
}

export function getInvitationStatus(invitation: TeamInvitation): import('./types').InvitationStatus {
  if (invitation.accepted_at) {
    return 'accepted'
  }
  if (new Date(invitation.expires_at) < new Date()) {
    return 'expired'
  }
  return 'pending'
}