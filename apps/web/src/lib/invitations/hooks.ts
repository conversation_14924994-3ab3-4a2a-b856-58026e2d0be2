'use client'

import { useState, useEffect, useCallback } from 'react'
import { 
  getTeamInvitations, 
  createInvitation, 
  cancelInvitation, 
  resendInvitation 
} from './api'
import type { InvitationWithInviter, CreateInvitationInput } from './types'

export { useUserInvitations } from './hooks/useUserInvitations'

export function useTeamInvitations(teamId: string) {
  const [invitations, setInvitations] = useState<InvitationWithInviter[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  const fetchInvitations = useCallback(async () => {
    if (!teamId) return
    
    try {
      setLoading(true)
      setError(null)
      const data = await getTeamInvitations(teamId)
      setInvitations(data)
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch invitations'))
    } finally {
      setLoading(false)
    }
  }, [teamId])

  useEffect(() => {
    fetchInvitations()
  }, [fetchInvitations])

  const invite = useCallback(async (input: Omit<CreateInvitationInput, 'teamId'>) => {
    try {
      setError(null)
      const invitation = await createInvitation({ ...input, teamId })
      await fetchInvitations()
      return { success: true, data: invitation }
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to create invitation')
      setError(error)
      return { success: false, error }
    }
  }, [teamId, fetchInvitations])

  const cancel = useCallback(async (invitationId: string) => {
    try {
      setError(null)
      // Optimistically remove the invitation from the list
      setInvitations(prev => prev.filter(inv => inv.id !== invitationId))
      await cancelInvitation(invitationId)
      await fetchInvitations()
      return { success: true }
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to cancel invitation')
      setError(error)
      // Refetch to restore the correct state if cancellation failed
      await fetchInvitations()
      return { success: false, error }
    }
  }, [fetchInvitations])

  const resend = useCallback(async (invitationId: string) => {
    try {
      setError(null)
      await resendInvitation(invitationId)
      await fetchInvitations()
      return { success: true }
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to resend invitation')
      setError(error)
      return { success: false, error }
    }
  }, [fetchInvitations])

  return {
    invitations,
    loading,
    error,
    invite,
    cancel,
    resend,
    refetch: fetchInvitations
  }
}