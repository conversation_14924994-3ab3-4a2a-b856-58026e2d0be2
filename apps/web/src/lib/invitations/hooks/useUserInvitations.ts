'use client'

import { useState, useEffect, useCallback } from 'react'
import { createClient } from '@/lib/supabase/client'
import { acceptInvitation } from '../api'
import type { InvitationWithTeam } from '../types'

export function useUserInvitations() {
  const [invitations, setInvitations] = useState<InvitationWithTeam[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  const fetchUserInvitations = useCallback(async () => {
    const supabase = createClient()
    
    try {
      setLoading(true)
      setError(null)
      
      // Get current user
      const { data: { user } } = await supabase.auth.getUser()
      if (!user?.email) {
        setInvitations([])
        return
      }
      
      // Fetch invitations for user's email
      const { data, error } = await supabase
        .from('team_invitations')
        .select(`
          *,
          teams (
            id,
            name
          )
        `)
        .eq('email', user.email)
        .is('accepted_at', null)
        .gt('expires_at', new Date().toISOString())
        .order('created_at', { ascending: false })
      
      if (error) throw error
      
      setInvitations(data || [])
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch invitations'))
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchUserInvitations()
  }, [fetchUserInvitations])

  const accept = useCallback(async (token: string) => {
    try {
      setError(null)
      await acceptInvitation(token)
      await fetchUserInvitations()
      return { success: true }
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to accept invitation')
      setError(error)
      return { success: false, error }
    }
  }, [fetchUserInvitations])

  return {
    invitations,
    loading,
    error,
    accept,
    refetch: fetchUserInvitations
  }
}