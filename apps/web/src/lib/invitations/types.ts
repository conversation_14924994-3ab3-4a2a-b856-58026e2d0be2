import { TeamR<PERSON> } from '@/lib/teams/types'

export interface TeamInvitation {
  id: string
  team_id: string
  email: string
  role: Team<PERSON><PERSON>
  token: string
  invited_by: string
  created_at: string
  expires_at: string
  accepted_at: string | null
  accepted_by: string | null
}

export interface CreateInvitationInput {
  teamId: string
  email: string
  role: TeamR<PERSON>
}

export interface InvitationWithInviter extends TeamInvitation {
  inviter?: {
    email: string
  }
}

export interface AcceptInvitationResult {
  team_id: string
  role: TeamR<PERSON>
}

export type InvitationStatus = 'pending' | 'accepted' | 'expired'

export interface InvitationError {
  message: string
  code?: 'ALREADY_MEMBER' | 'INVITATION_EXISTS' | 'INVALID_EMAIL' | 'EXPIRED' | 'INVALID_TOKEN' | 'CANNOT_INVITE' | 'EMAIL_MISMATCH'
}

export interface InvitationWithTeam extends TeamInvitation {
  teams?: {
    id: string
    name: string
  }
}