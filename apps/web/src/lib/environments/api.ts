import { createClient } from '@/lib/supabase/client'
import { Environment, EnvironmentError } from './types'

export async function getEnvironments(projectId: string): Promise<{ data?: Environment[]; error?: EnvironmentError }> {
  const supabase = createClient()
  
  try {
    const { data: environments, error } = await supabase
      .from('environments')
      .select('*')
      .eq('project_id', projectId)
      .order('created_at', { ascending: true })

    if (error) {
      return { error: { message: error.message } }
    }

    return { data: environments || [] }
  } catch (error) {
    console.error('Error fetching environments:', error)
    return { error: { message: 'Failed to fetch environments. Please try again.' } }
  }
}

export async function createEnvironment(projectId: string, name: string): Promise<{ data?: Environment; error?: EnvironmentError }> {
  const supabase = createClient()
  
  // Validate environment name
  const validationError = validateEnvironmentName(name)
  if (validationError) {
    return { error: { message: validationError } }
  }

  try {
    const { data: environment, error } = await supabase
      .from('environments')
      .insert({
        project_id: projectId,
        name: name.toLowerCase().trim(),
      })
      .select()
      .single()

    if (error) {
      // Handle duplicate name error
      if (error.code === '23505') {
        return { error: { message: 'An environment with this name already exists' } }
      }
      // Handle permission error
      if (error.code === '42501') {
        return { error: { message: 'You don\'t have permission to create environments' } }
      }
      return { error: { message: error.message } }
    }

    return { data: environment }
  } catch (error) {
    console.error('Error creating environment:', error)
    return { error: { message: 'Failed to create environment. Please try again.' } }
  }
}

export async function deleteEnvironment(id: string): Promise<{ success: boolean; error?: EnvironmentError }> {
  const supabase = createClient()
  
  try {
    const { error } = await supabase
      .from('environments')
      .delete()
      .eq('id', id)

    if (error) {
      if (error.code === '42501') {
        return { success: false, error: { message: 'You don\'t have permission to delete environments' } }
      }
      return { success: false, error: { message: error.message } }
    }

    return { success: true }
  } catch (error) {
    console.error('Error deleting environment:', error)
    return { success: false, error: { message: 'Failed to delete environment. Please try again.' } }
  }
}

// Validate environment name
function validateEnvironmentName(name: string): string | null {
  const trimmedName = name.trim()
  
  if (!trimmedName) {
    return 'Environment name is required'
  }

  if (trimmedName.length < 2) {
    return 'Environment name must be at least 2 characters'
  }

  if (trimmedName.length > 20) {
    return 'Environment name must be less than 20 characters'
  }

  // Only lowercase alphanumeric and hyphens
  const validNameRegex = /^[a-z0-9-]+$/
  if (!validNameRegex.test(trimmedName.toLowerCase())) {
    return 'Environment names can only contain lowercase letters, numbers, and hyphens'
  }

  return null
}

// Common environment name suggestions
export const ENVIRONMENT_SUGGESTIONS = [
  'development',
  'staging',
  'production',
  'testing',
  'preview',
  'demo',
  'qa',
]