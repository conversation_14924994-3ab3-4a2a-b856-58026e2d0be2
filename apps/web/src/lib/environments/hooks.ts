'use client'

import { useState, useEffect, useCallback } from 'react'
import { Environment, EnvironmentError } from './types'
import { getEnvironments, createEnvironment as createEnvironmentApi } from './api'

export function useEnvironments(projectId: string) {
  const [environments, setEnvironments] = useState<Environment[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<EnvironmentError | null>(null)
  const [selectedEnvironmentId, setSelectedEnvironmentId] = useState<string | null>(null)

  const fetchEnvironments = useCallback(async () => {
    if (!projectId) return
    
    setLoading(true)
    setError(null)
    
    const { data, error } = await getEnvironments(projectId)
    
    if (error) {
      setError(error)
    } else if (data) {
      setEnvironments(data)
      // Select first environment by default if none selected
      if (!selectedEnvironmentId && data.length > 0) {
        setSelectedEnvironmentId(data[0].id)
      }
    }
    
    setLoading(false)
  }, [projectId, selectedEnvironmentId])

  useEffect(() => {
    fetchEnvironments()
  }, [fetchEnvironments])

  const createEnvironment = async (name: string) => {
    setError(null)
    
    const { data, error } = await createEnvironmentApi(projectId, name)
    
    if (error) {
      setError(error)
      return { success: false, error }
    }
    
    if (data) {
      // Add the new environment to the list
      setEnvironments(prev => [...prev, data])
      // Select the new environment
      setSelectedEnvironmentId(data.id)
    }
    
    return { success: true, data }
  }

  const selectEnvironment = (environmentId: string) => {
    setSelectedEnvironmentId(environmentId)
  }

  const selectedEnvironment = environments.find(env => env.id === selectedEnvironmentId)

  return {
    environments,
    selectedEnvironment,
    loading,
    error,
    createEnvironment,
    selectEnvironment,
    refetch: fetchEnvironments,
  }
}