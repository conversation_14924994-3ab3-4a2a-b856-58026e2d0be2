import { useEffect } from 'react'

export class PerformanceMonitor {
  private static marks = new Map<string, number>()

  static mark(name: string): void {
    if (typeof window !== 'undefined' && window.performance) {
      this.marks.set(name, performance.now())
    }
  }

  static measure(name: string, startMark: string): number {
    if (typeof window !== 'undefined' && window.performance) {
      const startTime = this.marks.get(startMark)
      if (startTime) {
        const duration = performance.now() - startTime
        
        // Log in development
        if (process.env.NODE_ENV === 'development') {
          console.log(`[Performance] ${name}: ${duration.toFixed(2)}ms`)
        }
        
        // Clean up mark
        this.marks.delete(startMark)
        
        return duration
      }
    }
    return 0
  }

  static measureComponent(componentName: string, phase: 'mount' | 'update' | 'unmount'): void {
    const markName = `${componentName}-${phase}`
    
    if (phase === 'mount' || phase === 'update') {
      this.mark(markName)
      
      // Use requestIdleCallback to measure after render
      if ('requestIdleCallback' in window) {
        requestIdleCallback(() => {
          this.measure(`${componentName} ${phase}`, markName)
        })
      } else {
        setTimeout(() => {
          this.measure(`${componentName} ${phase}`, markName)
        }, 0)
      }
    }
  }
}

// React hook for performance monitoring
export function usePerformanceMonitor(componentName: string) {
  if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    useEffect(() => {
      PerformanceMonitor.measureComponent(componentName, 'mount')
      
      return () => {
        PerformanceMonitor.measureComponent(componentName, 'unmount')
      }
    }, [componentName])
  }
}

// Utility to debounce expensive operations
export function debounce<T extends (...args: unknown[]) => unknown>(
  fn: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    
    timeoutId = setTimeout(() => {
      fn(...args)
    }, delay)
  }
}

// Utility to throttle frequent operations
export function throttle<T extends (...args: unknown[]) => unknown>(
  fn: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle = false
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      fn(...args)
      inThrottle = true
      
      setTimeout(() => {
        inThrottle = false
      }, limit)
    }
  }
}

