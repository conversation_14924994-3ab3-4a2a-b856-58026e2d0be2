// Environment variable validation
const requiredEnvVars = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
] as const

// type RequiredEnvVars = typeof requiredEnvVars[number]

function validateEnvVars(): void {
  const missing: string[] = []

  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      missing.push(envVar)
    }
  }

  if (missing.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missing.join(', ')}. ` +
      `Please check your .env.local file and ensure all required variables are set.`
    )
  }
}

// Validate on module load
if (typeof window === 'undefined') {
  // Only validate on server-side
  validateEnvVars()
}

// Type-safe environment variable access
export const env = {
  NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL!,
  NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  ENCRYPTION_KEY: process.env.ENCRYPTION_KEY,
} as const