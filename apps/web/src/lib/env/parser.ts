export interface ParsedEnvVar {
  key: string
  value: string
  line: number
  warning?: string
}

export interface ParseError {
  line: number
  message: string
  content?: string
}

export interface ParseResult {
  success: boolean
  variables: ParsedEnvVar[]
  errors: ParseError[]
}

// Strip surrounding quotes from values
function stripQuotes(value: string): string {
  value = value.trim()
  
  // Check for matching quotes
  if (
    (value.startsWith('"') && value.endsWith('"')) ||
    (value.startsWith("'") && value.endsWith("'"))
  ) {
    return value.slice(1, -1)
  }
  
  return value
}

// Auto-correct common key issues
function normalizeKey(key: string): { normalized: string; warning?: string } {
  const original = key
  
  // Convert to uppercase
  key = key.toUpperCase()
  
  // Replace common separators with underscores
  key = key.replace(/[-.\s]/g, '_')
  
  // Remove invalid characters
  key = key.replace(/[^A-Z0-9_]/g, '')
  
  // Ensure it starts with a letter
  if (key && /^[0-9]/.test(key)) {
    key = 'VAR_' + key
  }
  
  const warning = original !== key 
    ? `Key "${original}" will be normalized to "${key}"`
    : undefined
    
  return { normalized: key, warning }
}

export function parseEnvContent(content: string): ParseResult {
  const lines = content.split('\n')
  const variables: ParsedEnvVar[] = []
  const errors: ParseError[] = []
  const seenKeys = new Set<string>()
  
  let inMultilineValue = false
  let multilineKey = ''
  let multilineValue: string[] = []
  let multilineStartLine = 0
  
  lines.forEach((line, index) => {
    const lineNumber = index + 1
    
    // Handle multiline continuation
    if (inMultilineValue) {
      if (line.endsWith('"') && !line.endsWith('\\"')) {
        // End of multiline value
        multilineValue.push(line.slice(0, -1))
        variables.push({
          key: multilineKey,
          value: multilineValue.join('\n'),
          line: multilineStartLine
        })
        inMultilineValue = false
        multilineKey = ''
        multilineValue = []
        return
      } else {
        // Continue multiline
        multilineValue.push(line)
        return
      }
    }
    
    // Skip empty lines and comments
    const trimmed = line.trim()
    if (!trimmed || trimmed.startsWith('#')) {
      return
    }
    
    // Parse key=value
    const equalIndex = line.indexOf('=')
    if (equalIndex === -1) {
      errors.push({
        line: lineNumber,
        message: 'Missing equals sign',
        content: line
      })
      return
    }
    
    const rawKey = line.slice(0, equalIndex).trim()
    const rawValue = line.slice(equalIndex + 1).trim()
    
    if (!rawKey) {
      errors.push({
        line: lineNumber,
        message: 'Empty key',
        content: line
      })
      return
    }
    
    // Normalize and validate key
    const { normalized: key, warning } = normalizeKey(rawKey)
    
    if (!key) {
      errors.push({
        line: lineNumber,
        message: 'Invalid key format',
        content: line
      })
      return
    }
    
    // Check for duplicates
    if (seenKeys.has(key)) {
      errors.push({
        line: lineNumber,
        message: `Duplicate key "${key}"`,
        content: line
      })
      return
    }
    seenKeys.add(key)
    
    // Handle value
    let value = rawValue
    
    // Check for multiline value (starts with quote but doesn't end with one)
    if (value.startsWith('"') && !value.endsWith('"')) {
      inMultilineValue = true
      multilineKey = key
      multilineValue = [value.slice(1)] // Remove opening quote
      multilineStartLine = lineNumber
      return
    }
    
    // Single line value
    value = stripQuotes(value)
    
    variables.push({
      key,
      value,
      line: lineNumber,
      warning
    })
  })
  
  // Check for unclosed multiline
  if (inMultilineValue) {
    errors.push({
      line: multilineStartLine,
      message: 'Unclosed multiline value',
      content: `${multilineKey}="${multilineValue[0]}...`
    })
  }
  
  return {
    success: errors.length === 0,
    variables,
    errors
  }
}

// Generate .env content from variables
export function generateEnvContent(
  variables: Array<{ key: string; value: string }>,
  environmentName?: string
): string {
  const lines: string[] = []
  
  // Add header comments
  lines.push('# Exported from EzEnv')
  if (environmentName) {
    lines.push(`# Environment: ${environmentName}`)
  }
  lines.push(`# Date: ${new Date().toISOString()}`)
  lines.push('')
  
  // Add each variable
  variables.forEach(({ key, value }) => {
    // Handle multiline values or values with special characters
    if (value.includes('\n') || value.includes('"') || value.includes(' ')) {
      // Escape quotes in the value
      const escapedValue = value.replace(/"/g, '\\"')
      lines.push(`${key}="${escapedValue}"`)
    } else {
      lines.push(`${key}=${value}`)
    }
  })
  
  return lines.join('\n')
}