import { Secret } from '@/lib/secrets/types'
import { decryptSecretValue } from '@/lib/secrets/api'
import { generateEnvContent } from './parser'

export interface ExportOptions {
  environmentName?: string
  filename?: string
}

// Export secrets to .env format
export async function exportSecrets(
  secrets: Secret[],
  options: ExportOptions = {}
): Promise<string> {
  const { environmentName } = options
  
  // Decrypt all secrets
  const decryptedVariables = await Promise.all(
    secrets.map(async (secret) => {
      try {
        const value = await decryptSecretValue(secret)
        return { key: secret.key, value }
      } catch (error) {
        console.error(`Failed to decrypt ${secret.key}:`, error)
        throw new Error(`Failed to decrypt secret: ${secret.key}`)
      }
    })
  )
  
  // Sort by key for consistent output
  decryptedVariables.sort((a, b) => a.key.localeCompare(b.key))
  
  // Generate .env content
  return generateEnvContent(decryptedVariables, environmentName)
}

// Trigger file download in browser
export function downloadFile(content: string, filename: string) {
  // Create blob with proper line endings
  const blob = new Blob([content], { 
    type: 'text/plain;charset=utf-8' 
  })
  
  // Create temporary URL
  const url = window.URL.createObjectURL(blob)
  
  // Create hidden link and trigger download
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  link.style.display = 'none'
  
  document.body.appendChild(link)
  link.click()
  
  // Cleanup
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

// Generate filename for export
export function generateExportFilename(
  projectName: string,
  environmentName: string
): string {
  // Sanitize names for filename
  const sanitize = (str: string) => 
    str.toLowerCase()
       .replace(/[^a-z0-9]+/g, '-')
       .replace(/^-+|-+$/g, '')
  
  const date = new Date().toISOString().split('T')[0]
  const project = sanitize(projectName)
  const env = sanitize(environmentName)
  
  return `${project}-${env}-${date}.env`
}