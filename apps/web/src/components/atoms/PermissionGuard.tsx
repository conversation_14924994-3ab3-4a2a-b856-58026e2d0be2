'use client'

import { ReactNode } from 'react'
import { useHasPermission, useHasAllPermissions, useHasAnyPermission } from '@/lib/permissions/hooks'
import { getPermissionDeniedMessage } from '@/lib/permissions'
import { useTeamContext } from '@/contexts/TeamContext'
import type { Permission } from '@/lib/permissions/types'

interface PermissionGuardProps {
  permission?: Permission
  permissions?: Permission[]
  requireAll?: boolean
  fallback?: ReactNode
  showMessage?: boolean
  children: ReactNode
}

export function PermissionGuard({
  permission,
  permissions,
  requireAll = true,
  fallback,
  showMessage = false,
  children
}: PermissionGuardProps) {
  const { currentTeam } = useTeamContext()
  
  // Check single permission
  const hasSinglePermission = useHasPermission(permission!)
  
  // Check multiple permissions
  const hasAllPermissions = useHasAllPermissions(permissions || [])
  const hasAnyPermission = useHasAnyPermission(permissions || [])
  
  // Determine if user has access
  let hasAccess = false
  if (permission) {
    hasAccess = hasSinglePermission
  } else if (permissions) {
    hasAccess = requireAll ? hasAllPermissions : hasAnyPermission
  } else {
    hasAccess = true // No permissions specified, allow access
  }
  
  if (!hasAccess) {
    if (fallback) {
      return <>{fallback}</>
    }
    
    if (showMessage && permission) {
      return (
        <div className="text-sm text-gray-500 italic">
          {getPermissionDeniedMessage(permission, currentTeam?.role)}
        </div>
      )
    }
    
    return null
  }
  
  return <>{children}</>
}

interface DisableGuardProps {
  permission?: Permission
  permissions?: Permission[]
  requireAll?: boolean
  tooltip?: string
  children: (props: { disabled: boolean; title?: string }) => ReactNode
}

export function DisableGuard({
  permission,
  permissions,
  requireAll = true,
  tooltip,
  children
}: DisableGuardProps) {
  const { currentTeam } = useTeamContext()
  
  // Check single permission
  const hasSinglePermission = useHasPermission(permission!)
  
  // Check multiple permissions
  const hasAllPermissions = useHasAllPermissions(permissions || [])
  const hasAnyPermission = useHasAnyPermission(permissions || [])
  
  // Determine if user has access
  let hasAccess = false
  if (permission) {
    hasAccess = hasSinglePermission
  } else if (permissions) {
    hasAccess = requireAll ? hasAllPermissions : hasAnyPermission
  } else {
    hasAccess = true // No permissions specified, allow access
  }
  
  const title = !hasAccess && permission 
    ? tooltip || getPermissionDeniedMessage(permission, currentTeam?.role)
    : undefined
  
  return <>{children({ disabled: !hasAccess, title })}</>
}