'use client'

import { Button } from '@/components/atoms/Button'
import { useUserInvitations } from '@/lib/invitations/hooks'

export function PendingInvitations() {
  const { invitations, loading, accept } = useUserInvitations()

  if (loading || invitations.length === 0) {
    return null
  }

  return (
    <div className="mb-6 bg-yellow-100 border-2 border-yellow-600 p-4 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
      <h3 className="font-bold mb-2">Pending Team Invitations</h3>
      <div className="space-y-2">
        {invitations.map((invitation) => (
          <div key={invitation.id} className="flex items-center justify-between bg-white border-2 border-black p-3">
            <div>
              <p className="font-medium">
                Join <strong>{invitation.teams?.name || 'Unknown Team'}</strong> as {invitation.role}
              </p>
              <p className="text-sm text-gray-600">
                Expires {new Date(invitation.expires_at).toLocaleDateString()}
              </p>
            </div>
            <Button
              variant="primary"
              onClick={async () => {
                const result = await accept(invitation.token)
                if (result.success) {
                  // Reload to update team context
                  window.location.reload()
                } else {
                  alert(result.error?.message || 'Failed to accept invitation')
                }
              }}
            >
              Accept
            </Button>
          </div>
        ))}
      </div>
    </div>
  )
}