'use client'

import { useState } from 'react'
import { Input } from '@/components/atoms/Input'
import { Button } from '@/components/atoms/Button'
import { Modal } from '@/components/molecules/Modal'
import { TeamRole } from '@/lib/teams/types'
import type { CreateInvitationInput } from '@/lib/invitations/types'

interface InviteMemberModalProps {
  onClose: () => void
  onInvite: (input: Omit<CreateInvitationInput, 'teamId'>) => Promise<{ success: boolean; error?: Error }>
}

export function InviteMemberModal({ onClose, onInvite }: InviteMemberModalProps) {
  const [email, setEmail] = useState('')
  const [role, setRole] = useState<TeamRole>('member')
  const [isInviting, setIsInviting] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!email.trim()) {
      setError('Email address is required')
      return
    }

    try {
      setIsInviting(true)
      setError('')
      
      const result = await onInvite({ email: email.trim(), role })
      
      if (result.success) {
        onClose()
      } else {
        setError(result.error?.message || 'Failed to send invitation')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send invitation')
    } finally {
      setIsInviting(false)
    }
  }

  return (
    <Modal
      isOpen={true}
      onClose={onClose}
      title="Invite Team Member"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label htmlFor="email" className="block text-sm font-medium mb-2">
            Email Address
          </label>
          <Input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="<EMAIL>"
            autoFocus
          />
          {error && (
            <p className="mt-2 text-sm text-red-600">{error}</p>
          )}
        </div>

        <div>
          <p className="text-sm font-medium mb-3">Role</p>
          <div className="space-y-2">
            <label className="flex items-start gap-3 cursor-pointer">
              <input
                type="radio"
                name="role"
                value="member"
                checked={role === 'member'}
                onChange={(e) => setRole(e.target.value as TeamRole)}
                className="mt-1"
              />
              <div>
                <p className="font-medium">Member</p>
                <p className="text-sm text-gray-600">Can view projects and retrieve secrets (read-only access)</p>
              </div>
            </label>
            
            <label className="flex items-start gap-3 cursor-pointer">
              <input
                type="radio"
                name="role"
                value="manager"
                checked={role === 'manager'}
                onChange={(e) => setRole(e.target.value as TeamRole)}
                className="mt-1"
              />
              <div>
                <p className="font-medium">Manager</p>
                <p className="text-sm text-gray-600">Can edit environments and secrets</p>
              </div>
            </label>
            
            <label className="flex items-start gap-3 cursor-pointer">
              <input
                type="radio"
                name="role"
                value="admin"
                checked={role === 'admin'}
                onChange={(e) => setRole(e.target.value as TeamRole)}
                className="mt-1"
              />
              <div>
                <p className="font-medium">Admin</p>
                <p className="text-sm text-gray-600">Full access including team management</p>
              </div>
            </label>
          </div>
        </div>

        <p className="text-sm text-gray-600">
          The user will receive an email invitation to join your team.
        </p>

        <div className="flex gap-2 justify-end pt-4 border-t-2 border-gray-200">
          <Button
            type="button"
            variant="secondary"
            onClick={onClose}
            disabled={isInviting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isInviting}
          >
            {isInviting ? 'Sending...' : 'Send Invitation'}
          </Button>
        </div>
      </form>
    </Modal>
  )
}