'use client'

import { useState } from 'react'
import { Button } from '@/components/atoms/Button'
import { Modal } from '@/components/molecules/Modal'

interface InvitationLinkModalProps {
  isOpen: boolean
  onClose: () => void
  invitationUrl: string
  email: string
}

export function InvitationLinkModal({ isOpen, onClose, invitationUrl, email }: InvitationLinkModalProps) {
  const [copied, setCopied] = useState(false)

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(invitationUrl)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy:', err)
    }
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Invitation Created"
    >
      <div className="space-y-4">
        <div className="bg-yellow-100 border-2 border-yellow-600 p-4">
          <p className="font-medium mb-2">Email delivery is not yet configured.</p>
          <p className="text-sm">Please share this invitation link with <strong>{email}</strong> manually:</p>
        </div>

        <div className="bg-gray-100 border-2 border-gray-300 p-4 rounded">
          <p className="text-xs font-mono break-all">{invitationUrl}</p>
        </div>

        <div className="flex gap-2">
          <Button
            variant="primary"
            onClick={handleCopy}
            className="flex-1"
          >
            {copied ? 'Copied!' : 'Copy Link'}
          </Button>
          <Button
            variant="secondary"
            onClick={onClose}
            className="flex-1"
          >
            Close
          </Button>
        </div>

        <p className="text-sm text-gray-600">
          This invitation will expire in 7 days. The recipient must use their <strong>{email}</strong> account to accept it.
        </p>
      </div>
    </Modal>
  )
}