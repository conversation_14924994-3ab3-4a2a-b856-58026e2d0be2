import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import ProfileSettings from '../ProfileSettings'
import { createClient } from '@/lib/supabase/client'
import { useAuth } from '@/contexts/AuthContext'

// Mock dependencies
jest.mock('@/lib/supabase/client')
jest.mock('@/contexts/AuthContext')

const mockSupabaseClient = {
  auth: {
    updateUser: jest.fn(),
  },
}

const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  created_at: '2024-01-01T00:00:00Z',
  user_metadata: {
    display_name: '<PERSON>',
    email_preferences: {
      project_activity: true,
      team_invitations: false,
      security_alerts: true,
    },
  },
}

const mockRefreshUser = jest.fn()

describe('ProfileSettings', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(createClient as jest.Mock).mockReturnValue(mockSupabaseClient)
    ;(useAuth as jest.Mock).mockReturnValue({ 
      user: mockUser,
      refreshUser: mockRefreshUser,
    })
  })

  it('renders profile information and email preferences', () => {
    render(<ProfileSettings />)

    // Check profile information section
    expect(screen.getByText('Profile Information')).toBeInTheDocument()
    expect(screen.getByLabelText('Email Address')).toBeInTheDocument()
    expect(screen.getByLabelText(/Display Name/)).toBeInTheDocument()
    expect(screen.getByText('Account Created')).toBeInTheDocument()

    // Check email preferences section
    expect(screen.getByText('Email Preferences')).toBeInTheDocument()
    expect(screen.getByLabelText('Project Activity')).toBeInTheDocument()
    expect(screen.getByLabelText('Team Invitations')).toBeInTheDocument()
    expect(screen.getByLabelText('Security Alerts')).toBeInTheDocument()
  })

  it('loads user data correctly', () => {
    render(<ProfileSettings />)

    // Check email field
    const emailInput = screen.getByLabelText('Email Address') as HTMLInputElement
    expect(emailInput.value).toBe('<EMAIL>')
    expect(emailInput).toBeDisabled()

    // Check display name
    const displayNameInput = screen.getByLabelText(/Display Name/) as HTMLInputElement
    expect(displayNameInput.value).toBe('John Doe')

    // Check email preferences
    const projectActivityToggle = screen.getByLabelText('Project Activity') as HTMLInputElement
    expect(projectActivityToggle.checked).toBe(true)
    
    const teamInvitationsToggle = screen.getByLabelText('Team Invitations') as HTMLInputElement
    expect(teamInvitationsToggle.checked).toBe(false)
    
    const securityAlertsToggle = screen.getByLabelText('Security Alerts') as HTMLInputElement
    expect(securityAlertsToggle.checked).toBe(true)
  })

  it('handles display name changes', () => {
    render(<ProfileSettings />)

    const displayNameInput = screen.getByLabelText(/Display Name/) as HTMLInputElement
    fireEvent.change(displayNameInput, { target: { value: 'Jane Smith' } })

    expect(displayNameInput.value).toBe('Jane Smith')
    expect(screen.getByText('10/50 characters')).toBeInTheDocument()
  })

  it('validates display name length', async () => {
    render(<ProfileSettings />)

    const displayNameInput = screen.getByLabelText(/Display Name/) as HTMLInputElement
    
    // Check that maxLength attribute is set
    expect(displayNameInput.maxLength).toBe(50)
    
    // Test that validation would prevent saving
    const longName = 'a'.repeat(51)
    fireEvent.change(displayNameInput, { target: { value: longName } })
    
    // In real DOM, maxLength would limit this, but in JSDOM it doesn't
    // So we verify the attribute is set correctly
    expect(displayNameInput.getAttribute('maxLength')).toBe('50')
  })

  it('handles email preference toggles', () => {
    render(<ProfileSettings />)

    const projectActivityToggle = screen.getByLabelText('Project Activity') as HTMLInputElement
    fireEvent.click(projectActivityToggle)

    expect(projectActivityToggle.checked).toBe(false)
  })

  it('enables save button only when changes are made', () => {
    render(<ProfileSettings />)

    const saveButton = screen.getByRole('button', { name: 'Save Changes' })
    expect(saveButton).toBeDisabled()

    // Make a change
    const displayNameInput = screen.getByLabelText(/Display Name/)
    fireEvent.change(displayNameInput, { target: { value: 'New Name' } })

    expect(saveButton).not.toBeDisabled()
  })

  it('successfully saves profile changes', async () => {
    mockSupabaseClient.auth.updateUser.mockResolvedValueOnce({
      error: null,
    })

    render(<ProfileSettings />)

    // Make changes
    const displayNameInput = screen.getByLabelText(/Display Name/)
    fireEvent.change(displayNameInput, { target: { value: 'New Name' } })

    const saveButton = screen.getByRole('button', { name: 'Save Changes' })
    fireEvent.click(saveButton)

    await waitFor(() => {
      expect(mockSupabaseClient.auth.updateUser).toHaveBeenCalledWith({
        data: {
          display_name: 'New Name',
          email_preferences: {
            project_activity: true,
            team_invitations: false,
            security_alerts: true,
          },
        },
      })
      expect(screen.getByText('Profile updated successfully')).toBeInTheDocument()
      expect(mockRefreshUser).toHaveBeenCalled()
    })
  })

  it('handles save errors', async () => {
    mockSupabaseClient.auth.updateUser.mockResolvedValueOnce({
      error: { message: 'Update failed' },
    })

    render(<ProfileSettings />)

    // Make changes
    const displayNameInput = screen.getByLabelText(/Display Name/)
    fireEvent.change(displayNameInput, { target: { value: 'New Name' } })

    const saveButton = screen.getByRole('button', { name: 'Save Changes' })
    fireEvent.click(saveButton)

    await waitFor(() => {
      expect(screen.getByText('Update failed')).toBeInTheDocument()
    })
  })

  it('shows error when trying to save without changes', async () => {
    render(<ProfileSettings />)

    const saveButton = screen.getByRole('button', { name: 'Save Changes' })
    expect(saveButton).toBeDisabled()
  })

  it('trims display name before saving', async () => {
    mockSupabaseClient.auth.updateUser.mockResolvedValueOnce({
      error: null,
    })

    render(<ProfileSettings />)

    const displayNameInput = screen.getByLabelText(/Display Name/)
    fireEvent.change(displayNameInput, { target: { value: '  Trimmed Name  ' } })

    const saveButton = screen.getByRole('button', { name: 'Save Changes' })
    fireEvent.click(saveButton)

    await waitFor(() => {
      expect(mockSupabaseClient.auth.updateUser).toHaveBeenCalledWith({
        data: expect.objectContaining({
          display_name: 'Trimmed Name',
        }),
      })
    })
  })

  it('handles missing user metadata gracefully', () => {
    ;(useAuth as jest.Mock).mockReturnValue({ 
      user: {
        id: 'test-user-id',
        email: '<EMAIL>',
        created_at: '2024-01-01T00:00:00Z',
        user_metadata: {},
      },
      refreshUser: mockRefreshUser,
    })

    render(<ProfileSettings />)

    // Should show empty display name
    const displayNameInput = screen.getByLabelText(/Display Name/) as HTMLInputElement
    expect(displayNameInput.value).toBe('')

    // Should default all preferences to true
    const projectActivityToggle = screen.getByLabelText('Project Activity') as HTMLInputElement
    expect(projectActivityToggle.checked).toBe(true)
  })

  it('formats dates correctly', () => {
    render(<ProfileSettings />)

    // Should show formatted date
    expect(screen.getByText('1/1/2024')).toBeInTheDocument()
  })

  it('handles missing created_at date', () => {
    ;(useAuth as jest.Mock).mockReturnValue({ 
      user: {
        id: 'test-user-id',
        email: '<EMAIL>',
        created_at: undefined,
        user_metadata: {},
      },
      refreshUser: mockRefreshUser,
    })

    render(<ProfileSettings />)

    expect(screen.getByText('N/A')).toBeInTheDocument()
  })
})