import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import SecuritySettings from '../SecuritySettings'
import { createClient } from '@/lib/supabase/client'
import { useAuth } from '@/contexts/AuthContext'

// Mock dependencies
jest.mock('@/lib/supabase/client')
jest.mock('@/contexts/AuthContext')

const mockSupabaseClient = {
  auth: {
    signInWithPassword: jest.fn(),
    updateUser: jest.fn(),
  },
}

const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  created_at: '2024-01-01T00:00:00Z',
  last_sign_in_at: '2024-01-02T00:00:00Z',
  app_metadata: { provider: 'email' },
}

describe('SecuritySettings', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(createClient as jest.Mock).mockReturnValue(mockSupabaseClient)
    ;(useAuth as jest.Mock).mockReturnValue({ user: mockUser })
  })

  it('renders password change form and session information', () => {
    render(<SecuritySettings />)

    // Check password change section
    expect(screen.getByText('Change Password')).toBeInTheDocument()
    expect(screen.getByLabelText('Current Password')).toBeInTheDocument()
    expect(screen.getByLabelText('New Password')).toBeInTheDocument()
    expect(screen.getByLabelText('Confirm New Password')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Update Password' })).toBeInTheDocument()

    // Check session information section
    expect(screen.getByText('Session Information')).toBeInTheDocument()
    expect(screen.getByText('Current Session')).toBeInTheDocument()
    expect(screen.getByText('Account Created')).toBeInTheDocument()
    expect(screen.getByText('Last Sign In')).toBeInTheDocument()
  })

  it('validates required fields', async () => {
    render(<SecuritySettings />)

    const submitButton = screen.getByRole('button', { name: 'Update Password' })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('All fields are required')).toBeInTheDocument()
    })
  })

  it('validates password length', async () => {
    render(<SecuritySettings />)

    fireEvent.change(screen.getByLabelText('Current Password'), { target: { value: 'current' } })
    fireEvent.change(screen.getByLabelText('New Password'), { target: { value: '12345' } })
    fireEvent.change(screen.getByLabelText('Confirm New Password'), { target: { value: '12345' } })

    fireEvent.click(screen.getByRole('button', { name: 'Update Password' }))

    await waitFor(() => {
      expect(screen.getByText('New password must be at least 6 characters long')).toBeInTheDocument()
    })
  })

  it('validates password confirmation match', async () => {
    render(<SecuritySettings />)

    fireEvent.change(screen.getByLabelText('Current Password'), { target: { value: 'current' } })
    fireEvent.change(screen.getByLabelText('New Password'), { target: { value: '123456' } })
    fireEvent.change(screen.getByLabelText('Confirm New Password'), { target: { value: '123457' } })

    fireEvent.click(screen.getByRole('button', { name: 'Update Password' }))

    await waitFor(() => {
      expect(screen.getByText('New passwords do not match')).toBeInTheDocument()
    })
  })

  it('handles incorrect current password', async () => {
    mockSupabaseClient.auth.signInWithPassword.mockResolvedValueOnce({
      error: { message: 'Invalid login credentials' },
    })

    render(<SecuritySettings />)

    fireEvent.change(screen.getByLabelText('Current Password'), { target: { value: 'wrong' } })
    fireEvent.change(screen.getByLabelText('New Password'), { target: { value: '123456' } })
    fireEvent.change(screen.getByLabelText('Confirm New Password'), { target: { value: '123456' } })

    fireEvent.click(screen.getByRole('button', { name: 'Update Password' }))

    await waitFor(() => {
      expect(screen.getByText('Current password is incorrect')).toBeInTheDocument()
    })
  })

  it('successfully updates password', async () => {
    mockSupabaseClient.auth.signInWithPassword.mockResolvedValueOnce({
      error: null,
    })
    mockSupabaseClient.auth.updateUser.mockResolvedValueOnce({
      error: null,
    })

    render(<SecuritySettings />)

    const currentPasswordInput = screen.getByLabelText('Current Password')
    const newPasswordInput = screen.getByLabelText('New Password')
    const confirmPasswordInput = screen.getByLabelText('Confirm New Password')

    fireEvent.change(currentPasswordInput, { target: { value: 'current123' } })
    fireEvent.change(newPasswordInput, { target: { value: 'newpass123' } })
    fireEvent.change(confirmPasswordInput, { target: { value: 'newpass123' } })

    fireEvent.click(screen.getByRole('button', { name: 'Update Password' }))

    await waitFor(() => {
      expect(screen.getByText('Password updated successfully')).toBeInTheDocument()
    })

    // Check that form fields are cleared
    expect(currentPasswordInput).toHaveValue('')
    expect(newPasswordInput).toHaveValue('')
    expect(confirmPasswordInput).toHaveValue('')
  })

  it('handles update password errors', async () => {
    mockSupabaseClient.auth.signInWithPassword.mockResolvedValueOnce({
      error: null,
    })
    mockSupabaseClient.auth.updateUser.mockResolvedValueOnce({
      error: { message: 'Network error' },
    })

    render(<SecuritySettings />)

    fireEvent.change(screen.getByLabelText('Current Password'), { target: { value: 'current' } })
    fireEvent.change(screen.getByLabelText('New Password'), { target: { value: '123456' } })
    fireEvent.change(screen.getByLabelText('Confirm New Password'), { target: { value: '123456' } })

    fireEvent.click(screen.getByRole('button', { name: 'Update Password' }))

    await waitFor(() => {
      expect(screen.getByText('Network error')).toBeInTheDocument()
    })
  })

  it('displays session information correctly', () => {
    render(<SecuritySettings />)

    expect(screen.getByText('Active')).toBeInTheDocument()
    expect(screen.getByText('email')).toBeInTheDocument()
  })

  it('handles missing user data gracefully', () => {
    ;(useAuth as jest.Mock).mockReturnValue({ 
      user: { 
        id: 'test-user-id',
        email: '<EMAIL>',
        created_at: undefined,
        last_sign_in_at: undefined,
      } 
    })

    render(<SecuritySettings />)

    // Should show N/A for missing dates
    const naElements = screen.getAllByText('N/A')
    expect(naElements.length).toBeGreaterThan(0)
  })
})