import { validatePasswordChange, calculatePasswordStrength } from '../SecuritySettings.utils'
import { PASSWORD_STRENGTH } from '../SecuritySettings.constants'

describe('SecuritySettings.utils', () => {
  describe('validatePasswordChange', () => {
    it('should fail when fields are empty', () => {
      const result = validatePasswordChange('', '', '')
      expect(result.isValid).toBe(false)
      expect(result.message).toBe('All fields are required')
    })

    it('should fail when new password is too short', () => {
      const result = validatePasswordChange('current', '12345', '12345')
      expect(result.isValid).toBe(false)
      expect(result.message).toContain('at least 6 characters')
    })

    it('should fail when passwords do not match', () => {
      const result = validatePasswordChange('current', '123456', '123457')
      expect(result.isValid).toBe(false)
      expect(result.message).toBe('New passwords do not match')
    })

    it('should fail when new password is same as current', () => {
      const result = validatePasswordChange('123456', '123456', '123456')
      expect(result.isValid).toBe(false)
      expect(result.message).toBe('New password must be different from current password')
    })

    it('should pass with valid inputs', () => {
      const result = validatePasswordChange('current', 'newpass123', 'newpass123')
      expect(result.isValid).toBe(true)
      expect(result.message).toBeUndefined()
    })
  })

  describe('calculatePasswordStrength', () => {
    it('should return WEAK for short simple passwords', () => {
      const strength = calculatePasswordStrength('abc')
      expect(strength).toBe(PASSWORD_STRENGTH.WEAK)
    })

    it('should return FAIR for medium passwords', () => {
      const strength = calculatePasswordStrength('abc123')
      expect(strength).toBe(PASSWORD_STRENGTH.FAIR)
    })

    it('should return GOOD for stronger passwords', () => {
      const strength = calculatePasswordStrength('Abc123')
      expect(strength).toBe(PASSWORD_STRENGTH.GOOD)
    })

    it('should return STRONG for complex passwords', () => {
      const strength = calculatePasswordStrength('MyP@ssw0rd123!')
      expect(strength).toBe(PASSWORD_STRENGTH.STRONG)
    })
  })
})