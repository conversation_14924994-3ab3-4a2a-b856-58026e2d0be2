'use client'

import { But<PERSON> } from '@/components/atoms/Button'
import { 
  EMAIL_PREFERENCE_LABELS, 
  DISPLAY_NAME_MAX_LENGTH, 
  MESSAGES 
} from './ProfileSettings.constants'
import { useProfileSettings, type EmailPreferences } from './useProfileSettings'

export default function ProfileSettings() {
  const {
    user,
    displayName,
    setDisplayName,
    emailPreferences,
    togglePreference,
    loading,
    hasChanges,
    message,
    saveProfile,
  } = useProfileSettings()

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate display name
    if (displayName.length > DISPLAY_NAME_MAX_LENGTH) {
      return
    }

    await saveProfile()
  }

  // Format date for display
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString()
  }

  return (
    <div className="space-y-8">
      {/* Profile Information Section */}
      <section className="border-2 border-black rounded-lg p-6 shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
        <h3 className="text-xl font-bold mb-6">Profile Information</h3>
        
        <form onSubmit={handleSave} className="space-y-4 max-w-md">
          <div>
            <label htmlFor="email" className="block text-sm font-medium mb-2">
              Email Address
            </label>
            <input
              id="email"
              type="email"
              value={user?.email || ''}
              className="w-full px-4 py-2 border-2 border-gray-300 rounded-lg bg-gray-50 text-gray-600 cursor-not-allowed"
              disabled
              readOnly
            />
            <p className="text-sm text-gray-500 mt-1">
              {MESSAGES.EMAIL_CANNOT_CHANGE}
            </p>
          </div>

          <div>
            <label htmlFor="displayName" className="block text-sm font-medium mb-2">
              Display Name <span className="text-gray-500">(optional)</span>
            </label>
            <input
              id="displayName"
              type="text"
              value={displayName}
              onChange={(e) => setDisplayName(e.target.value)}
              maxLength={DISPLAY_NAME_MAX_LENGTH}
              placeholder="Enter your display name"
              className="w-full px-4 py-2 border-2 border-black rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
              disabled={loading}
            />
            <p className="text-sm text-gray-600 mt-1">
              {displayName.length}/{DISPLAY_NAME_MAX_LENGTH} characters
            </p>
          </div>

          <div>
            <p className="text-sm font-medium text-gray-600">Account Created</p>
            <p className="font-mono">{formatDate(user?.created_at)}</p>
          </div>
        </form>
      </section>

      {/* Email Preferences Section */}
      <section className="border-2 border-black rounded-lg p-6 shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
        <h3 className="text-xl font-bold mb-6">Email Preferences</h3>
        
        <div className="space-y-4">
          {(Object.keys(EMAIL_PREFERENCE_LABELS) as Array<keyof EmailPreferences>).map((prefKey) => (
            <div key={prefKey} className="flex items-start justify-between p-4 border-2 border-gray-200 rounded-lg">
              <div className="flex-1">
                <label htmlFor={prefKey} className="font-medium cursor-pointer">
                  {EMAIL_PREFERENCE_LABELS[prefKey].title}
                </label>
                <p className="text-sm text-gray-600 mt-1">
                  {EMAIL_PREFERENCE_LABELS[prefKey].description}
                </p>
              </div>
              <input
                id={prefKey}
                type="checkbox"
                checked={emailPreferences[prefKey]}
                onChange={() => togglePreference(prefKey)}
                className="ml-4 mt-1 w-5 h-5 accent-black cursor-pointer"
                disabled={loading}
              />
            </div>
          ))}
        </div>
      </section>

      {/* Save Section */}
      <div className="flex items-center justify-between">
        <div className="flex-1">
          {message && (
            <div
              className={`inline-block px-4 py-2 rounded-lg border-2 ${
                message.type === 'error'
                  ? 'border-red-600 bg-red-50 text-red-600'
                  : 'border-green-600 bg-green-50 text-green-600'
              }`}
              role="alert"
            >
              {message.text}
            </div>
          )}
        </div>
        <Button
          onClick={handleSave}
          loading={loading}
          disabled={!hasChanges}
          className="bg-black text-white hover:bg-gray-800 disabled:opacity-50"
        >
          Save Changes
        </Button>
      </div>
    </div>
  )
}