'use client'

import { useState } from 'react'
import { Modal } from './Modal'
import { Input } from '@/components/atoms/Input'
import { Button } from '@/components/atoms/Button'
import { ENVIRONMENT_SUGGESTIONS } from '@/lib/environments/api'

interface CreateEnvironmentModalProps {
  isOpen: boolean
  onClose: () => void
  onCreateEnvironment: (name: string) => Promise<{ success: boolean; error?: { message: string } }>
  projectId: string
}

export function CreateEnvironmentModal({ isOpen, onClose, onCreateEnvironment }: CreateEnvironmentModalProps) {
  const [environmentName, setEnvironmentName] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!environmentName.trim()) {
      setError('Environment name is required')
      return
    }

    setLoading(true)
    setError(null)

    const result = await onCreateEnvironment(environmentName.trim())

    if (result.success) {
      setEnvironmentName('')
      onClose()
    } else if (result.error) {
      setError(result.error.message)
    }

    setLoading(false)
  }

  const handleClose = () => {
    if (!loading) {
      setEnvironmentName('')
      setError(null)
      onClose()
    }
  }

  const handleSuggestionClick = (suggestion: string) => {
    setEnvironmentName(suggestion)
    setError(null)
  }

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Create New Environment">
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <Input
            id="environment-name"
            name="environment-name"
            type="text"
            label="Environment Name"
            placeholder="e.g., development, staging, production"
            value={environmentName}
            onChange={(e) => {
              setEnvironmentName(e.target.value.toLowerCase())
              setError(null)
            }}
            error={error || undefined}
            required
            autoFocus
            disabled={loading}
          />
          <p className="mt-2 text-sm text-gray-600">
            Use lowercase letters, numbers, and hyphens only (2-20 characters)
          </p>
        </div>

        {/* Suggestions */}
        <div>
          <p className="text-sm font-medium mb-2">Common environments:</p>
          <div className="flex flex-wrap gap-2">
            {ENVIRONMENT_SUGGESTIONS.map((suggestion) => (
              <button
                key={suggestion}
                type="button"
                onClick={() => handleSuggestionClick(suggestion)}
                className="px-3 py-1 text-sm border-2 border-black hover:bg-gray-100 transition-colors"
                disabled={loading}
              >
                {suggestion}
              </button>
            ))}
          </div>
        </div>

        <div className="flex gap-4 justify-end">
          <Button
            type="button"
            variant="secondary"
            onClick={handleClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            loading={loading}
          >
            Create Environment
          </Button>
        </div>
      </form>
    </Modal>
  )
}