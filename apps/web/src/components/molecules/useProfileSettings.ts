import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useAuth } from '@/contexts/AuthContext'
import { DEFAULT_EMAIL_PREFERENCES, MESSAGES } from './ProfileSettings.constants'

export interface EmailPreferences {
  project_activity: boolean
  team_invitations: boolean
  security_alerts: boolean
}

export function useProfileSettings() {
  const { user, refreshUser } = useAuth()
  const [displayName, setDisplayName] = useState('')
  const [emailPreferences, setEmailPreferences] = useState<EmailPreferences>(DEFAULT_EMAIL_PREFERENCES)
  const [loading, setLoading] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)
  
  const supabase = createClient()

  // Load user metadata on mount
  useEffect(() => {
    if (user?.user_metadata) {
      setDisplayName(user.user_metadata.display_name || '')
      if (user.user_metadata.email_preferences) {
        setEmailPreferences({
          project_activity: user.user_metadata.email_preferences.project_activity ?? true,
          team_invitations: user.user_metadata.email_preferences.team_invitations ?? true,
          security_alerts: user.user_metadata.email_preferences.security_alerts ?? true,
        })
      }
    }
  }, [user])

  // Track changes
  useEffect(() => {
    if (!user) return
    
    const currentDisplayName = user.user_metadata?.display_name || ''
    const currentPrefs = user.user_metadata?.email_preferences || DEFAULT_EMAIL_PREFERENCES

    const nameChanged = displayName !== currentDisplayName
    // Deep comparison for email preferences
    const prefsChanged = Object.keys(emailPreferences).some(
      key => emailPreferences[key as keyof EmailPreferences] !== currentPrefs[key as keyof EmailPreferences]
    )
    
    setHasChanges(nameChanged || prefsChanged)
  }, [displayName, emailPreferences, user])

  const saveProfile = async () => {
    if (!hasChanges) {
      setMessage({ type: 'error', text: MESSAGES.NO_CHANGES })
      return false
    }

    setLoading(true)
    setMessage(null)

    try {
      const { error } = await supabase.auth.updateUser({
        data: {
          display_name: displayName.trim(),
          email_preferences: emailPreferences,
        },
      })

      if (error) {
        setMessage({ type: 'error', text: error.message })
        return false
      } else {
        setMessage({ type: 'success', text: MESSAGES.UPDATE_SUCCESS })
        // Refresh user context to propagate changes
        if (refreshUser) {
          await refreshUser()
        }
        setHasChanges(false)
        return true
      }
    } catch {
      setMessage({ type: 'error', text: MESSAGES.UNEXPECTED_ERROR })
      return false
    } finally {
      setLoading(false)
    }
  }

  const togglePreference = (key: keyof EmailPreferences) => {
    setEmailPreferences(prev => ({
      ...prev,
      [key]: !prev[key],
    }))
  }

  return {
    user,
    displayName,
    setDisplayName,
    emailPreferences,
    togglePreference,
    loading,
    hasChanges,
    message,
    saveProfile,
  }
}