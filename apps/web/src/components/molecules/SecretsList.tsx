'use client'

import { useState } from 'react'
import { Secret } from '@/lib/secrets/types'
import { Button } from '@/components/atoms/Button'
import { DisableGuard } from '@/components/atoms/PermissionGuard'
import { PERMISSIONS } from '@/lib/permissions/types'

interface SecretsListProps {
  secrets: Secret[]
  loading: boolean
  onUpdateSecret: (secretId: string, value: string) => Promise<{ success: boolean; error?: { message: string } }>
  onDeleteSecret: (secretId: string) => Promise<{ success: boolean; error?: { message: string } }>
  onDecryptSecret: (secret: Secret) => Promise<string>
  getDecryptedValue: (secretId: string) => string | undefined
  clearDecryptedValue: (secretId: string) => void
}

export function SecretsList({ 
  secrets, 
  loading, 
  onUpdateSecret, 
  onDeleteSecret,
  onDecryptSecret,
  getDecryptedValue,
  clearDecryptedValue
}: SecretsListProps) {
  const [visibleSecrets, setVisibleSecrets] = useState<Set<string>>(new Set())
  const [editingSecret, setEditingSecret] = useState<string | null>(null)
  const [editValue, setEditValue] = useState('')
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null)
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  const toggleSecretVisibility = async (secret: Secret) => {
    const secretId = secret.id
    
    if (visibleSecrets.has(secretId)) {
      // Hide the secret
      setVisibleSecrets(prev => {
        const newSet = new Set(prev)
        newSet.delete(secretId)
        return newSet
      })
      // Clear decrypted value from memory
      clearDecryptedValue(secretId)
    } else {
      // Show the secret - decrypt if needed
      setActionLoading(secretId)
      try {
        const decryptedValue = getDecryptedValue(secretId)
        if (!decryptedValue) {
          await onDecryptSecret(secret)
        }
        setVisibleSecrets(prev => new Set(prev).add(secretId))
      } catch (error) {
        console.error('Failed to decrypt secret:', error)
      }
      setActionLoading(null)
    }
  }

  const copyToClipboard = async (secret: Secret) => {
    setActionLoading(secret.id)
    try {
      let value = getDecryptedValue(secret.id)
      if (!value) {
        value = await onDecryptSecret(secret)
      }
      await navigator.clipboard.writeText(value)
      // Show brief success indicator
      setActionLoading(null)
    } catch (error) {
      console.error('Failed to copy secret:', error)
      setActionLoading(null)
    }
  }

  const startEdit = async (secret: Secret) => {
    setActionLoading(secret.id)
    try {
      let value = getDecryptedValue(secret.id)
      if (!value) {
        value = await onDecryptSecret(secret)
      }
      setEditValue(value)
      setEditingSecret(secret.id)
    } catch (error) {
      console.error('Failed to decrypt secret for editing:', error)
    }
    setActionLoading(null)
  }

  const saveEdit = async () => {
    if (!editingSecret) return
    
    setActionLoading(editingSecret)
    const result = await onUpdateSecret(editingSecret, editValue)
    
    if (result.success) {
      setEditingSecret(null)
      setEditValue('')
      // Hide the secret after editing
      setVisibleSecrets(prev => {
        const newSet = new Set(prev)
        newSet.delete(editingSecret)
        return newSet
      })
    }
    setActionLoading(null)
  }

  const cancelEdit = () => {
    setEditingSecret(null)
    setEditValue('')
  }

  const confirmDelete = async (secretId: string) => {
    setActionLoading(secretId)
    const result = await onDeleteSecret(secretId)
    
    if (result.success) {
      setDeleteConfirm(null)
    }
    setActionLoading(null)
  }

  const getDisplayValue = (secret: Secret): string => {
    const decryptedValue = getDecryptedValue(secret.id)
    
    if (editingSecret === secret.id) {
      return editValue
    }
    
    if (visibleSecrets.has(secret.id) && decryptedValue) {
      return decryptedValue
    }
    
    return '••••••••'
  }

  if (loading) {
    return (
      <div className="space-y-2">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-white border-2 border-gray-300 p-4 animate-pulse">
            <div className="flex items-center justify-between">
              <div className="h-4 bg-gray-300 rounded w-1/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/3"></div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (secrets.length === 0) {
    return (
      <div className="empty-state-card">
        <div className="text-center">
          <div className="mb-4">
            <svg className="w-16 h-16 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
            </svg>
          </div>
          <h3 className="text-lg font-bold mb-2">No secrets yet</h3>
          <p className="text-gray-600">
            Add your first secret to get started
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-2">
      {secrets.map((secret) => (
        <div key={secret.id} className="bg-white border-2 border-black p-4">
          {deleteConfirm === secret.id ? (
            // Delete confirmation
            <div className="flex items-center justify-between">
              <span className="text-red-600 font-medium">
                Are you sure you want to delete {secret.key}?
              </span>
              <div className="flex gap-2">
                <Button
                  variant="secondary"
                  onClick={() => setDeleteConfirm(null)}
                  disabled={actionLoading === secret.id}
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={() => confirmDelete(secret.id)}
                  loading={actionLoading === secret.id}
                  className="bg-red-600 border-red-600"
                >
                  Delete
                </Button>
              </div>
            </div>
          ) : (
            // Normal view
            <div className="flex items-start gap-4">
              <div className="flex-1">
                <div className="font-mono font-bold text-sm mb-1">{secret.key}</div>
                {editingSecret === secret.id ? (
                  <textarea
                    value={editValue}
                    onChange={(e) => setEditValue(e.target.value)}
                    className="w-full font-mono text-sm p-2 border-2 border-black min-h-[60px] resize-y"
                    autoFocus
                  />
                ) : (
                  <div className="font-mono text-sm text-gray-600 break-all whitespace-pre-wrap">
                    {getDisplayValue(secret)}
                  </div>
                )}
              </div>
              
              <div className="flex items-center gap-1">
                {editingSecret === secret.id ? (
                  <>
                    <button
                      onClick={saveEdit}
                      className="p-2 hover:bg-gray-100 transition-colors"
                      title="Save"
                      disabled={actionLoading === secret.id}
                    >
                      <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </button>
                    <button
                      onClick={cancelEdit}
                      className="p-2 hover:bg-gray-100 transition-colors"
                      title="Cancel"
                      disabled={actionLoading === secret.id}
                    >
                      <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </>
                ) : (
                  <>
                    <button
                      onClick={() => toggleSecretVisibility(secret)}
                      className="p-2 hover:bg-gray-100 transition-colors"
                      title={visibleSecrets.has(secret.id) ? "Hide value" : "Show value"}
                      disabled={actionLoading === secret.id}
                    >
                      {actionLoading === secret.id ? (
                        <div className="w-5 h-5 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                      ) : (
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          {visibleSecrets.has(secret.id) ? (
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                          ) : (
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          )}
                        </svg>
                      )}
                    </button>
                    <button
                      onClick={() => copyToClipboard(secret)}
                      className="p-2 hover:bg-gray-100 transition-colors"
                      title="Copy value"
                      disabled={actionLoading === secret.id}
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                    </button>
                    <DisableGuard permission={PERMISSIONS.SECRET_EDIT}>
                      {({ disabled }) => 
                        disabled ? null : (
                          <button
                            onClick={() => startEdit(secret)}
                            className="p-2 hover:bg-gray-100 transition-colors"
                            title="Edit value"
                            disabled={actionLoading === secret.id}
                          >
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                          </button>
                        )
                      }
                    </DisableGuard>
                    <DisableGuard permission={PERMISSIONS.SECRET_DELETE}>
                      {({ disabled }) => 
                        disabled ? null : (
                          <button
                            onClick={() => setDeleteConfirm(secret.id)}
                            className="p-2 hover:bg-gray-100 transition-colors"
                            title="Delete secret"
                          >
                            <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        )
                      }
                    </DisableGuard>
                  </>
                )}
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  )
}