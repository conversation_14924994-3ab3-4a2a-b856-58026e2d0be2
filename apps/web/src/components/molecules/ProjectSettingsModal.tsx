'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/atoms/Button'
import { Project } from '@/lib/projects/types'

interface ProjectSettingsModalProps {
  isOpen: boolean
  onClose: () => void
  project: Project
  onUpdateProject?: (updates: Partial<Project>) => Promise<void>
}

export function ProjectSettingsModal({ 
  isOpen, 
  onClose, 
  project,
  onUpdateProject 
}: ProjectSettingsModalProps) {
  const [name, setName] = useState(project.name)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  if (!isOpen) return null

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!name.trim()) {
      setError('Project name is required')
      return
    }

    if (name === project.name) {
      onClose()
      return
    }

    setLoading(true)
    setError(null)

    try {
      // TODO: Implement update project API
      if (onUpdateProject) {
        await onUpdateProject({ name: name.trim() })
      }
      onClose()
    } catch {
      setError('Failed to update project settings')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white border-2 border-black p-6 max-w-md w-full shadow-[8px_8px_0px_0px_rgba(0,0,0,1)]">
        <h2 className="text-2xl font-bold mb-6">Project Settings</h2>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="project-name" className="block text-sm font-medium mb-2">
              Project Name
            </label>
            <input
              id="project-name"
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-3 py-2 border-2 border-black focus:outline-none focus:shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]"
              disabled={loading}
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              Team
            </label>
            <p className="px-3 py-2 bg-gray-100 border-2 border-gray-300">
              {project.team?.name || 'Unknown Team'}
            </p>
            <p className="text-sm text-gray-600 mt-1">
              Projects cannot be moved between teams
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              Created
            </label>
            <p className="text-gray-700">
              {new Date(project.created_at).toLocaleDateString()} at {new Date(project.created_at).toLocaleTimeString()}
            </p>
          </div>

          {error && (
            <div className="bg-red-100 border-2 border-red-500 p-3 text-red-700 text-sm">
              {error}
            </div>
          )}

          <div className="flex gap-4 pt-4">
            <Button
              type="submit"
              disabled={loading || !name.trim()}
            >
              {loading ? 'Saving...' : 'Save Changes'}
            </Button>
            <Button
              type="button"
              variant="secondary"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </Button>
          </div>
        </form>

        <div className="mt-6 pt-6 border-t-2 border-gray-200">
          <p className="text-sm text-gray-600">
            Note: Additional settings like API access, webhooks, and integrations are coming soon.
          </p>
        </div>
      </div>
    </div>
  )
}