import { calculatePasswordStrength } from './SecuritySettings.utils'

interface PasswordStrengthIndicatorProps {
  password: string
}

export function PasswordStrengthIndicator({ password }: PasswordStrengthIndicatorProps) {
  if (!password) return null

  const strength = calculatePasswordStrength(password)
  const strengthPercentage = ((strength.minScore + 1) / 5) * 100

  return (
    <div className="mt-2">
      <div className="flex items-center justify-between mb-1">
        <span className="text-xs text-gray-600">Password strength:</span>
        <span className={`text-xs font-medium ${strength.color}`}>
          {strength.label}
        </span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-1.5">
        <div
          className={`h-1.5 rounded-full transition-all duration-300 ${
            strength.minScore === 0 ? 'bg-red-600' :
            strength.minScore === 2 ? 'bg-yellow-600' :
            strength.minScore === 3 ? 'bg-blue-600' : 'bg-green-600'
          }`}
          style={{ width: `${strengthPercentage}%` }}
        />
      </div>
    </div>
  )
}