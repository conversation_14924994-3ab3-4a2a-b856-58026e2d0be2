'use client'

import Link from 'next/link'
import { Project } from '@/lib/projects/types'

interface ProjectListProps {
  projects: Project[]
  loading: boolean
}

export function ProjectList({ projects, loading }: ProjectListProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-white border-2 border-gray-300 p-6 animate-pulse">
            <div className="h-6 bg-gray-300 rounded mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        ))}
      </div>
    )
  }

  if (projects.length === 0) {
    return (
      <div className="empty-state-card col-span-full">
        <div className="text-center max-w-md mx-auto">
          <div className="mb-4">
            <svg className="w-24 h-24 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
            </svg>
          </div>
          <h3 className="text-xl font-bold mb-2">No projects yet</h3>
          <p className="text-gray-600 mb-6">
            Create your first project to get started organizing your environment variables.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {projects.map((project) => (
        <Link
          key={project.id}
          href={`/projects/${project.id}`}
          className="block bg-white border-2 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] p-6 hover:shadow-none hover:translate-x-1 hover:translate-y-1 transition-all duration-200"
        >
          <h3 className="text-lg font-bold mb-2 truncate">{project.name}</h3>
          <div className="space-y-1 text-sm text-gray-600">
            {project.team && (
              <p className="truncate">
                <span className="font-medium">Team:</span> {project.team.name}
              </p>
            )}
            <p>
              <span className="font-medium">Created:</span> {new Date(project.created_at).toLocaleDateString()}
            </p>
          </div>
        </Link>
      ))}
    </div>
  )
}