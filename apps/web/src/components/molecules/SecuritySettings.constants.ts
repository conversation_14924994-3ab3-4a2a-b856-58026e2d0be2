export const PASSWORD_MIN_LENGTH = 6

export const MESSAGES = {
  VALIDATION: {
    REQUIRED_FIELDS: 'All fields are required',
    PASSWORD_TOO_SHORT: `New password must be at least ${PASSWORD_MIN_LENGTH} characters long`,
    PASSWORDS_DONT_MATCH: 'New passwords do not match',
    INCORRECT_CURRENT_PASSWORD: 'Current password is incorrect',
  },
  SUCCESS: {
    PASSWORD_UPDATED: 'Password updated successfully',
  },
  ERROR: {
    UNEXPECTED: 'An unexpected error occurred. Please try again.',
  },
} as const

export const SESSION_LABELS = {
  CURRENT_SESSION: 'Current Session',
  ACCOUNT_CREATED: 'Account Created',
  LAST_SIGN_IN: 'Last Sign In',
  AUTH_METHOD: 'Authentication Method',
} as const

// Password strength indicators
export const PASSWORD_STRENGTH = {
  WEAK: { label: 'Weak', color: 'text-red-600', minScore: 0 },
  FAIR: { label: 'Fair', color: 'text-yellow-600', minScore: 2 },
  GOOD: { label: 'Good', color: 'text-blue-600', minScore: 3 },
  STRONG: { label: 'Strong', color: 'text-green-600', minScore: 4 },
} as const