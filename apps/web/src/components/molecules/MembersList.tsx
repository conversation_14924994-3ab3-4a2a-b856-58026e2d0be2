'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { TeamR<PERSON> } from '@/lib/teams/types'
import { Button } from '@/components/atoms/Button'
import { useHasPermission } from '@/lib/permissions/hooks'
import { PERMISSIONS } from '@/lib/permissions/types'

interface TeamMember {
  team_id: string
  user_id: string
  role: TeamRole
  created_at: string
  user_email: string
}

interface MembersListProps {
  teamId: string
}

export function MembersList({ teamId }: MembersListProps) {
  const canEditRoles = useHasPermission(PERMISSIONS.MEMBER_EDIT_ROLE)
  const canRemoveMembers = useHasPermission(PERMISSIONS.MEMBER_REMOVE)
  const [members, setMembers] = useState<TeamMember[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentUserId, setCurrentUserId] = useState<string | null>(null)
  const [changingRole, setChangingRole] = useState<string | null>(null)
  const [removingMember, setRemovingMember] = useState<string | null>(null)

  useEffect(() => {
    fetchMembers()
    getCurrentUser()
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [teamId])

  async function getCurrentUser() {
    const supabase = createClient()
    const { data } = await supabase.auth.getUser()
    if (data?.user) {
      setCurrentUserId(data.user.id)
    }
  }

  async function fetchMembers() {
    const supabase = createClient()
    
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('team_members_with_email')
        .select('*')
        .eq('team_id', teamId)
        .order('created_at', { ascending: true })

      if (error) throw error

      setMembers(data || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch members')
    } finally {
      setLoading(false)
    }
  }

  async function handleRoleChange(userId: string, newRole: TeamRole) {
    const supabase = createClient()
    
    if (userId === currentUserId) {
      alert('You cannot change your own role')
      return
    }

    // Check if this would remove the last admin
    if (newRole !== 'admin') {
      const adminCount = members.filter(m => m.role === 'admin').length
      const isCurrentlyAdmin = members.find(m => m.user_id === userId)?.role === 'admin'
      
      if (adminCount === 1 && isCurrentlyAdmin) {
        alert('Teams must have at least one admin')
        return
      }
    }

    try {
      setChangingRole(userId)
      const { error } = await supabase
        .from('team_members')
        .update({ role: newRole })
        .eq('team_id', teamId)
        .eq('user_id', userId)

      if (error) throw error

      await fetchMembers()
    } catch (err) {
      alert(err instanceof Error ? err.message : 'Failed to update role')
    } finally {
      setChangingRole(null)
    }
  }

  async function handleRemoveMember(userId: string) {
    const supabase = createClient()
    
    if (userId === currentUserId) {
      alert('You cannot remove yourself from the team')
      return
    }

    const member = members.find(m => m.user_id === userId)
    if (!member) return

    // Check if this would remove the last admin
    if (member.role === 'admin') {
      const adminCount = members.filter(m => m.role === 'admin').length
      if (adminCount === 1) {
        alert('Teams must have at least one admin')
        return
      }
    }

    if (!confirm(`Remove ${member.user_email} from the team?`)) {
      return
    }

    try {
      setRemovingMember(userId)
      const { error } = await supabase
        .from('team_members')
        .delete()
        .eq('team_id', teamId)
        .eq('user_id', userId)

      if (error) throw error

      await fetchMembers()
    } catch (err) {
      alert(err instanceof Error ? err.message : 'Failed to remove member')
    } finally {
      setRemovingMember(null)
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-white border-2 border-gray-300 p-4 animate-pulse">
            <div className="h-5 bg-gray-300 rounded w-1/3 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          </div>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-100 border-2 border-red-500 p-4">
        <p className="text-red-700">{error}</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {members.map((member) => {
        const isCurrentUser = member.user_id === currentUserId
        const isChangingRole = changingRole === member.user_id
        const isRemoving = removingMember === member.user_id
        
        return (
          <div
            key={member.user_id}
            className="bg-white border-2 border-black p-4 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">
                  {member.user_email}
                  {isCurrentUser && ' (You)'}
                </p>
                <p className="text-sm text-gray-600">
                  Joined {new Date(member.created_at).toLocaleDateString()}
                </p>
              </div>
              
              <div className="flex items-center gap-4">
                {/* Role display/selector */}
                {canEditRoles && !isCurrentUser ? (
                  <select
                    value={member.role}
                    onChange={(e) => handleRoleChange(member.user_id, e.target.value as TeamRole)}
                    disabled={isChangingRole}
                    className="px-3 py-1 border-2 border-black bg-white font-medium focus:outline-none focus:shadow-[2px_2px_0px_0px_rgba(0,0,0,1)] disabled:opacity-50"
                  >
                    <option value="admin">Admin</option>
                    <option value="manager">Manager</option>
                    <option value="member">Member</option>
                  </select>
                ) : (
                  <span className={`
                    px-3 py-1 text-sm font-medium
                    ${member.role === 'admin' ? 'bg-blue-100 text-blue-700' : 
                      member.role === 'manager' ? 'bg-green-100 text-green-700' : 
                      'bg-gray-100 text-gray-700'}
                  `}>
                    {member.role === 'admin' ? 'Admin' : 
                     member.role === 'manager' ? 'Manager' : 'Member'}
                  </span>
                )}

                {/* Remove button */}
                {canRemoveMembers && !isCurrentUser && (
                  <Button
                    variant="secondary"
                    onClick={() => handleRemoveMember(member.user_id)}
                    disabled={isRemoving}
                  >
                    {isRemoving ? 'Removing...' : 'Remove'}
                  </Button>
                )}
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
}