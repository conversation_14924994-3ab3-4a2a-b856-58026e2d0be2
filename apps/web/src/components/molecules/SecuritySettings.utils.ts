import { PASSWORD_MIN_LENGTH, PASSWORD_STRENGTH } from './SecuritySettings.constants'

export interface PasswordValidationResult {
  isValid: boolean
  message?: string
}

export function validatePasswordChange(
  currentPassword: string,
  newPassword: string,
  confirmPassword: string
): PasswordValidationResult {
  if (!currentPassword || !newPassword || !confirmPassword) {
    return { isValid: false, message: 'All fields are required' }
  }

  if (newPassword.length < PASSWORD_MIN_LENGTH) {
    return { 
      isValid: false, 
      message: `New password must be at least ${PASSWORD_MIN_LENGTH} characters long` 
    }
  }

  if (newPassword !== confirmPassword) {
    return { isValid: false, message: 'New passwords do not match' }
  }

  // Check if new password is same as current
  if (newPassword === currentPassword) {
    return { isValid: false, message: 'New password must be different from current password' }
  }

  return { isValid: true }
}

export function calculatePasswordStrength(password: string): typeof PASSWORD_STRENGTH[keyof typeof PASSWORD_STRENGTH] {
  let score = 0

  // Length check
  if (password.length >= 8) score++
  if (password.length >= 12) score++

  // Character variety checks
  if (/[a-z]/.test(password)) score++
  if (/[A-Z]/.test(password)) score++
  if (/[0-9]/.test(password)) score++
  if (/[^a-zA-Z0-9]/.test(password)) score++

  // Return appropriate strength level
  if (score >= PASSWORD_STRENGTH.STRONG.minScore) return PASSWORD_STRENGTH.STRONG
  if (score >= PASSWORD_STRENGTH.GOOD.minScore) return PASSWORD_STRENGTH.GOOD
  if (score >= PASSWORD_STRENGTH.FAIR.minScore) return PASSWORD_STRENGTH.FAIR
  return PASSWORD_STRENGTH.WEAK
}

// Securely clear sensitive data from memory
export function clearSensitiveData(
  setCurrentPassword: (value: string) => void,
  setNewPassword: (value: string) => void,
  setConfirmPassword: (value: string) => void
): void {
  setCurrentPassword('')
  setNewPassword('')
  setConfirmPassword('')
}