import { useState, useCallback } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useAuth } from '@/contexts/AuthContext'
import { MESSAGES } from './SecuritySettings.constants'
import { validatePasswordChange, clearSensitiveData } from './SecuritySettings.utils'

export function useSecuritySettings() {
  const { user } = useAuth()
  const [currentPassword, setCurrentPassword] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)
  
  const supabase = createClient()

  const updatePassword = useCallback(async () => {
    setMessage(null)

    // Validate inputs
    const validation = validatePasswordChange(currentPassword, newPassword, confirmPassword)
    if (!validation.isValid) {
      setMessage({ type: 'error', text: validation.message! })
      return false
    }

    setLoading(true)

    try {
      // First, verify current password by attempting to sign in
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: user?.email || '',
        password: currentPassword,
      })

      if (signInError) {
        setMessage({ type: 'error', text: MESSAGES.VALIDATION.INCORRECT_CURRENT_PASSWORD })
        return false
      }

      // Update password
      const { error: updateError } = await supabase.auth.updateUser({
        password: newPassword,
      })

      if (updateError) {
        setMessage({ type: 'error', text: updateError.message })
        return false
      }

      setMessage({ type: 'success', text: MESSAGES.SUCCESS.PASSWORD_UPDATED })
      
      // Clear sensitive data from memory
      clearSensitiveData(setCurrentPassword, setNewPassword, setConfirmPassword)
      
      return true
    } catch {
      setMessage({ type: 'error', text: MESSAGES.ERROR.UNEXPECTED })
      return false
    } finally {
      setLoading(false)
    }
  }, [currentPassword, newPassword, confirmPassword, user?.email, supabase.auth])

  const clearMessage = useCallback(() => {
    setMessage(null)
  }, [])

  return {
    user,
    currentPassword,
    setCurrentPassword,
    newPassword,
    setNewPassword,
    confirmPassword,
    setConfirmPassword,
    loading,
    message,
    updatePassword,
    clearMessage,
  }
}