'use client'

import { memo } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/atoms/Button'
import type { TeamWithDetails } from '@/lib/teams/types'
import { getRoleLabel, getRoleColors, formatMemberCount, formatProjectCount, canManageTeam } from '@/lib/teams/utils'

interface TeamCardProps {
  team: TeamWithDetails
  isCurrentTeam: boolean
}

export const TeamCard = memo(function TeamCard({ team, isCurrentTeam }: TeamCardProps) {
  const router = useRouter()

  return (
    <div
      className={`
        bg-white border-2 border-black p-6 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]
        ${isCurrentTeam ? 'ring-2 ring-blue-500' : ''}
      `}
    >
      <h3 className="text-lg font-bold mb-2">{team.name}</h3>
      <div className="text-sm text-gray-600 mb-4">
        <p>{formatMemberCount(team.member_count)} • {formatProjectCount(team.project_count)}</p>
        <p className="mt-1">Created {new Date(team.created_at).toLocaleDateString()}</p>
      </div>
      <div className="flex items-center justify-between">
        <span className={`
          px-3 py-1 text-sm font-medium rounded
          ${getRoleColors(team.role).bg} ${getRoleColors(team.role).text}
        `}>
          {getRoleLabel(team.role)}
        </span>
        {canManageTeam(team.role) && (
          <Button
            variant="secondary"
            onClick={() => {
              router.push(`/teams/${team.id}/members`)
            }}
          >
            View Team
          </Button>
        )}
      </div>
    </div>
  )
})