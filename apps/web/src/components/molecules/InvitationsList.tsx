'use client'

import { But<PERSON> } from '@/components/atoms/Button'
import { InvitationWithInviter } from '@/lib/invitations/types'

interface InvitationsListProps {
  invitations: InvitationWithInviter[]
  loading: boolean
  onCancel: (invitationId: string) => Promise<{ success: boolean; error?: Error }>
  onResend: (invitationId: string) => Promise<{ success: boolean; error?: Error }>
}

export function InvitationsList({ invitations, loading, onCancel, onResend }: InvitationsListProps) {
  
  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(2)].map((_, i) => (
          <div key={i} className="bg-white border-2 border-gray-300 p-4 animate-pulse">
            <div className="h-5 bg-gray-300 rounded w-1/3 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          </div>
        ))}
      </div>
    )
  }

  if (invitations.length === 0) {
    return null
  }

  function formatTimeAgo(date: string): string {
    const now = new Date()
    const then = new Date(date)
    const diffMs = now.getTime() - then.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    
    if (diffDays === 0) return 'today'
    if (diffDays === 1) return 'yesterday'
    return `${diffDays} days ago`
  }

  return (
    <div className="space-y-4">
      {invitations.map((invitation) => (
        <div
          key={invitation.id}
          className="bg-white border-2 border-black p-4 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] opacity-75"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">{invitation.email}</p>
              <p className="text-sm text-gray-600">
                {invitation.role === 'admin' ? 'Admin' : 
                 invitation.role === 'manager' ? 'Manager' : 'Member'} • 
                Invited {formatTimeAgo(invitation.created_at)} by {invitation.inviter?.email || 'Unknown'}
              </p>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="secondary"
                onClick={async () => {
                  const result = await onResend(invitation.id)
                  if (result.success) {
                    alert('Invitation resent successfully')
                  } else {
                    alert(result.error?.message || 'Failed to resend invitation')
                  }
                }}
              >
                Resend
              </Button>
              <Button
                variant="secondary"
                onClick={async () => {
                  if (confirm('Cancel this invitation?')) {
                    const result = await onCancel(invitation.id)
                    if (!result.success) {
                      alert(result.error?.message || 'Failed to cancel invitation')
                    }
                  }
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}