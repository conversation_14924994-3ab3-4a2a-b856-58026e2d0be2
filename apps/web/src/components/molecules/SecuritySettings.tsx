'use client'

import { But<PERSON> } from '@/components/atoms/Button'
import { useSecuritySettings } from './useSecuritySettings'
import { PasswordStrengthIndicator } from './PasswordStrengthIndicator'
import { SESSION_LABELS, PASSWORD_MIN_LENGTH } from './SecuritySettings.constants'

export default function SecuritySettings() {
  const {
    user,
    currentPassword,
    setCurrentPassword,
    newPassword,
    setNewPassword,
    confirmPassword,
    setConfirmPassword,
    loading,
    message,
    updatePassword,
  } = useSecuritySettings()

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault()
    await updatePassword()
  }

  // Format date for display
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleString()
  }

  return (
    <div className="space-y-8">
      {/* Password Change Section */}
      <section className="border-2 border-black rounded-lg p-6 shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
        <h3 className="text-xl font-bold mb-6">Change Password</h3>
        
        <form onSubmit={handlePasswordChange} className="space-y-4 max-w-md">
          <div>
            <label htmlFor="currentPassword" className="block text-sm font-medium mb-2">
              Current Password
            </label>
            <input
              id="currentPassword"
              type="password"
              value={currentPassword}
              onChange={(e) => setCurrentPassword(e.target.value)}
              className="w-full px-4 py-2 border-2 border-black rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
              disabled={loading}
              autoComplete="current-password"
              aria-required="true"
            />
          </div>

          <div>
            <label htmlFor="newPassword" className="block text-sm font-medium mb-2">
              New Password
            </label>
            <input
              id="newPassword"
              type="password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              className="w-full px-4 py-2 border-2 border-black rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
              disabled={loading}
              autoComplete="new-password"
              aria-required="true"
              aria-describedby="password-requirements"
            />
            <p id="password-requirements" className="text-sm text-gray-600 mt-1">
              Minimum {PASSWORD_MIN_LENGTH} characters
            </p>
            <PasswordStrengthIndicator password={newPassword} />
          </div>

          <div>
            <label htmlFor="confirmPassword" className="block text-sm font-medium mb-2">
              Confirm New Password
            </label>
            <input
              id="confirmPassword"
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              className="w-full px-4 py-2 border-2 border-black rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
              disabled={loading}
              autoComplete="new-password"
              aria-required="true"
            />
          </div>

          {message && (
            <div
              className={`p-3 rounded-lg border-2 ${
                message.type === 'error'
                  ? 'border-red-600 bg-red-50 text-red-600'
                  : 'border-green-600 bg-green-50 text-green-600'
              }`}
              role="alert"
            >
              {message.text}
            </div>
          )}

          <Button
            type="submit"
            loading={loading}
            className="bg-black text-white hover:bg-gray-800"
          >
            Update Password
          </Button>
        </form>
      </section>

      {/* Session Information Section */}
      <section className="border-2 border-black rounded-lg p-6 shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
        <h3 className="text-xl font-bold mb-6">Session Information</h3>
        
        <div className="space-y-4">
          <div>
            <p className="text-sm font-medium text-gray-600">{SESSION_LABELS.CURRENT_SESSION}</p>
            <p className="font-mono">Active</p>
          </div>

          <div>
            <p className="text-sm font-medium text-gray-600">{SESSION_LABELS.ACCOUNT_CREATED}</p>
            <p className="font-mono">{formatDate(user?.created_at)}</p>
          </div>

          <div>
            <p className="text-sm font-medium text-gray-600">{SESSION_LABELS.LAST_SIGN_IN}</p>
            <p className="font-mono">{formatDate(user?.last_sign_in_at)}</p>
          </div>

          {user?.app_metadata?.provider && (
            <div>
              <p className="text-sm font-medium text-gray-600">{SESSION_LABELS.AUTH_METHOD}</p>
              <p className="font-mono">{user.app_metadata.provider}</p>
            </div>
          )}
        </div>
      </section>
    </div>
  )
}