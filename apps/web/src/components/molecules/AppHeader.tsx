'use client'

import { useState } from 'react'
import Link from 'next/link'
import { User } from '@supabase/supabase-js'
import { useAuth } from '@/contexts/AuthContext'
import { useTeamContext } from '@/contexts/TeamContext'

interface AppHeaderProps {
  user: User
}

function TeamSwitcher() {
  const { teams, currentTeam, loading } = useTeamContext()
  const [isOpen, setIsOpen] = useState(false)
  
  if (loading || !currentTeam) {
    return null
  }
  
  if (teams.length <= 1) {
    return (
      <div className="px-3 py-1 text-sm font-medium bg-gray-100 border-2 border-gray-300">
        {currentTeam.name}
      </div>
    )
  }
  
  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-1 text-sm font-medium bg-gray-100 border-2 border-black hover:bg-gray-200 transition-colors"
      >
        <span>{currentTeam.name}</span>
        <svg className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>
      
      {isOpen && (
        <>
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          <div className="absolute left-0 mt-2 w-64 bg-white border-2 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] z-20">
            <div className="p-2">
              {teams.map((team) => (
                <button
                  key={team.id}
                  onClick={() => {
                    setIsOpen(false)
                    if (team.id !== currentTeam.id) {
                      window.location.href = `/teams?switch=${team.id}`
                    }
                  }}
                  className={`
                    block w-full text-left px-3 py-2 text-sm font-medium transition-colors
                    ${team.id === currentTeam.id ? 'bg-black text-white' : 'hover:bg-gray-100'}
                  `}
                >
                  <div className="flex items-center justify-between">
                    <span>{team.name}</span>
                    <span className="text-xs opacity-75">
                      {team.role === 'admin' ? 'Admin' : team.role === 'manager' ? 'Manager' : 'Member'}
                    </span>
                  </div>
                </button>
              ))}
            </div>
            <div className="border-t-2 border-gray-200 p-2">
              <Link
                href="/teams"
                className="block w-full text-left px-3 py-2 text-sm font-medium hover:bg-gray-100 transition-colors"
                onClick={() => setIsOpen(false)}
              >
                Manage Teams
              </Link>
            </div>
          </div>
        </>
      )}
    </div>
  )
}

export function AppHeader({ user }: AppHeaderProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const { signOut } = useAuth()
  
  const displayName = user.user_metadata?.display_name || user.email

  return (
    <header className="bg-white border-b-2 border-black h-16">
      <div className="h-full px-8 flex items-center justify-between">
        {/* Logo and Team Switcher */}
        <div className="flex items-center gap-6">
          <Link href="/dashboard" className="flex items-center">
            <h1 className="text-2xl font-bold">EzEnv</h1>
          </Link>
          
          <TeamSwitcher />
        </div>

        {/* User Menu */}
        <div className="relative">
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="flex items-center gap-2 px-4 py-2 border-2 border-black hover:bg-gray-50 transition-colors"
            aria-expanded={isMenuOpen}
            aria-haspopup="true"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <span className="text-sm font-medium truncate max-w-[200px]">
              {displayName}
            </span>
            <svg className={`w-4 h-4 transition-transform ${isMenuOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          {/* Dropdown Menu */}
          {isMenuOpen && (
            <>
              {/* Backdrop */}
              <div 
                className="fixed inset-0 z-10" 
                onClick={() => setIsMenuOpen(false)}
              />
              
              {/* Menu */}
              <div className="absolute right-0 mt-2 w-56 bg-white border-2 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] z-20">
                <div className="p-4 border-b-2 border-black">
                  <p className="text-sm font-bold">Signed in as</p>
                  <p className="text-sm text-gray-600 truncate">{user.email}</p>
                  {user.user_metadata?.display_name && (
                    <p className="text-xs text-gray-500 mt-1">{user.user_metadata.display_name}</p>
                  )}
                </div>
                
                <nav className="p-2">
                  <Link 
                    href="/settings" 
                    className="block w-full text-left px-3 py-2 text-sm font-medium hover:bg-gray-50 transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Account Settings
                  </Link>
                  <hr className="my-2 border-black" />
                  <button 
                    className="block w-full text-left px-3 py-2 text-sm font-medium hover:bg-gray-50 transition-colors text-red-600"
                    onClick={async () => {
                      setIsMenuOpen(false)
                      await signOut()
                    }}
                  >
                    Sign Out
                  </button>
                </nav>
              </div>
            </>
          )}
        </div>
      </div>
    </header>
  )
}