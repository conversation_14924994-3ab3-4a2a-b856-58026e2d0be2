'use client'

import { useState } from 'react'
import { Input } from '@/components/atoms/Input'
import { Button } from '@/components/atoms/Button'
import { Modal } from '@/components/molecules/Modal'
import { useTeamContext } from '@/contexts/TeamContext'
import { validateTeamName } from '@/lib/teams/utils'
import { TEAM_MESSAGES, TEAM_LIMITS } from '@/lib/teams/constants'
import { useToast } from '@/contexts/ToastContext'
import { isTeamError } from '@/lib/teams/errors'

interface CreateTeamModalProps {
  onClose: () => void
}

export function CreateTeamModal({ onClose }: CreateTeamModalProps) {
  const { createTeam } = useTeamContext()
  const [teamName, setTeamName] = useState('')
  const [isCreating, setIsCreating] = useState(false)
  const [error, setError] = useState('')
  const { showSuccess, showError } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    const validation = validateTeamName(teamName)
    if (!validation.isValid) {
      setError(validation.error || TEAM_MESSAGES.NAME_REQUIRED)
      return
    }

    try {
      setIsCreating(true)
      setError('')
      await createTeam({ name: teamName.trim() })
      showSuccess(TEAM_MESSAGES.CREATE_SUCCESS)
      onClose()
    } catch (err) {
      if (isTeamError(err)) {
        if (err.code === 'DUPLICATE_NAME') {
          setError(TEAM_MESSAGES.DUPLICATE_NAME)
        } else {
          setError(err.message)
          showError(err.message)
        }
      } else if (err instanceof Error) {
        // Check for duplicate name error
        if (err.message.includes('duplicate') || err.message.includes('already exists')) {
          setError(TEAM_MESSAGES.DUPLICATE_NAME)
        } else {
          setError(err.message)
          showError(err.message)
        }
      } else {
        setError(TEAM_MESSAGES.CREATE_ERROR)
        showError(TEAM_MESSAGES.CREATE_ERROR)
      }
    } finally {
      setIsCreating(false)
    }
  }

  return (
    <Modal
      isOpen={true}
      onClose={onClose}
      title="Create New Team"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="teamName" className="block text-sm font-medium mb-2">
            Team Name
          </label>
          <Input
            id="teamName"
            type="text"
            value={teamName}
            onChange={(e) => setTeamName(e.target.value)}
            placeholder="Enter team name"
            maxLength={TEAM_LIMITS.MAX_NAME_LENGTH}
            autoFocus
            aria-describedby={error ? 'team-error' : undefined}
            aria-invalid={!!error}
          />
          {error && (
            <p id="team-error" className="mt-2 text-sm text-red-600" role="alert">
              {error}
            </p>
          )}
        </div>

        <p className="text-sm text-gray-600">
          You&apos;ll be able to invite members after creating the team.
        </p>

        <div className="flex gap-2 justify-end pt-4 border-t-2 border-gray-200">
          <Button
            type="button"
            variant="secondary"
            onClick={onClose}
            disabled={isCreating}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isCreating}
          >
            {isCreating ? 'Creating...' : 'Create Team'}
          </Button>
        </div>
      </form>
    </Modal>
  )
}