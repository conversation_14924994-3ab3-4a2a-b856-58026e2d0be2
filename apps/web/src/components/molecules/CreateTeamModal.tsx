'use client'

import { useState } from 'react'
import { Input } from '@/components/atoms/Input'
import { Button } from '@/components/atoms/Button'
import { Modal } from '@/components/molecules/Modal'
import { useTeamContext } from '@/contexts/TeamContext'

interface CreateTeamModalProps {
  onClose: () => void
}

export function CreateTeamModal({ onClose }: CreateTeamModalProps) {
  const { createTeam } = useTeamContext()
  const [teamName, setTeamName] = useState('')
  const [isCreating, setIsCreating] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!teamName.trim()) {
      setError('Team name is required')
      return
    }

    try {
      setIsCreating(true)
      setError('')
      await createTeam({ name: teamName.trim() })
      onClose()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create team')
    } finally {
      setIsCreating(false)
    }
  }

  return (
    <Modal
      isOpen={true}
      onClose={onClose}
      title="Create New Team"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="teamName" className="block text-sm font-medium mb-2">
            Team Name
          </label>
          <Input
            id="teamName"
            type="text"
            value={teamName}
            onChange={(e) => setTeamName(e.target.value)}
            placeholder="Enter team name"
            autoFocus
          />
          {error && (
            <p className="mt-2 text-sm text-red-600">{error}</p>
          )}
        </div>

        <p className="text-sm text-gray-600">
          You&apos;ll be able to invite members after creating the team.
        </p>

        <div className="flex gap-2 justify-end pt-4 border-t-2 border-gray-200">
          <Button
            type="button"
            variant="secondary"
            onClick={onClose}
            disabled={isCreating}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isCreating}
          >
            {isCreating ? 'Creating...' : 'Create Team'}
          </Button>
        </div>
      </form>
    </Modal>
  )
}