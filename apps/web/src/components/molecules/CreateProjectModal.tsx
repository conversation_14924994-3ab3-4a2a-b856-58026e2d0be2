'use client'

import { useState } from 'react'
import { Modal } from './Modal'
import { Input } from '@/components/atoms/Input'
import { Button } from '@/components/atoms/Button'
import { useTeamContext } from '@/contexts/TeamContext'

interface CreateProjectModalProps {
  isOpen: boolean
  onClose: () => void
  onCreateProject: (name: string, teamId?: string) => Promise<{ success: boolean; error?: { message: string } }>
}

export function CreateProjectModal({ isOpen, onClose, onCreateProject }: CreateProjectModalProps) {
  const [projectName, setProjectName] = useState('')
  const [selectedTeamId, setSelectedTeamId] = useState<string>('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { teams, currentTeam } = useTeamContext()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!projectName.trim()) {
      setError('Project name is required')
      return
    }

    if (projectName.length < 3) {
      setError('Project name must be at least 3 characters')
      return
    }

    if (projectName.length > 50) {
      setError('Project name must be less than 50 characters')
      return
    }

    // Validate allowed characters
    const validNameRegex = /^[a-zA-Z0-9\s\-_]+$/
    if (!validNameRegex.test(projectName)) {
      setError('Project name can only contain letters, numbers, spaces, hyphens, and underscores')
      return
    }

    setLoading(true)
    setError(null)

    const teamId = teams.length > 1 ? selectedTeamId : currentTeam?.id
    const result = await onCreateProject(projectName.trim(), teamId)

    if (result.success) {
      setProjectName('')
      onClose()
    } else if (result.error) {
      setError(result.error.message)
    }

    setLoading(false)
  }

  const handleClose = () => {
    if (!loading) {
      setProjectName('')
      setSelectedTeamId('')
      setError(null)
      onClose()
    }
  }

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Create New Project">
      <form onSubmit={handleSubmit} className="space-y-6">
        <Input
          id="project-name"
          name="project-name"
          type="text"
          label="Project Name"
          placeholder="My Awesome Project"
          value={projectName}
          onChange={(e) => {
            setProjectName(e.target.value)
            setError(null)
          }}
          error={error || undefined}
          required
          autoFocus
          disabled={loading}
        />
        
        {teams.length > 1 && (
          <div>
            <label htmlFor="team-select" className="block text-sm font-medium mb-2">
              Team
            </label>
            <select
              id="team-select"
              value={selectedTeamId || currentTeam?.id || ''}
              onChange={(e) => setSelectedTeamId(e.target.value)}
              className="w-full px-4 py-2 border-2 border-black bg-white font-medium focus:outline-none focus:shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]"
              disabled={loading}
            >
              {teams.filter(team => team.role === 'admin').map((team) => (
                <option key={team.id} value={team.id}>
                  {team.name}
                </option>
              ))}
            </select>
            <p className="mt-1 text-sm text-gray-600">
              Only showing teams where you are an admin
            </p>
          </div>
        )}

        <div className="flex gap-4 justify-end">
          <Button
            type="button"
            variant="secondary"
            onClick={handleClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            loading={loading}
          >
            Create Project
          </Button>
        </div>
      </form>
    </Modal>
  )
}