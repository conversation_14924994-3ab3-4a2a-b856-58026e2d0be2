'use client'

import { useState } from 'react'
import { Modal } from './Modal'
import { Input } from '@/components/atoms/Input'
import { Button } from '@/components/atoms/Button'

interface AddSecretModalProps {
  isOpen: boolean
  onClose: () => void
  onCreateSecret: (key: string, value: string) => Promise<{ success: boolean; error?: { message: string } }>
}

export function AddSecretModal({ isOpen, onClose, onCreateSecret }: AddSecretModalProps) {
  const [secretKey, setSecretKey] = useState('')
  const [secretValue, setSecretValue] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!secretKey.trim()) {
      setError('Key is required')
      return
    }

    if (!secretValue) {
      setError('Value is required')
      return
    }

    setLoading(true)
    setError(null)

    const result = await onCreateSecret(secretKey.trim().toUpperCase(), secretValue)

    if (result.success) {
      setSecretKey('')
      setSecretValue('')
      onClose()
    } else if (result.error) {
      setError(result.error.message)
    }

    setLoading(false)
  }

  const handleClose = () => {
    if (!loading) {
      setSecretKey('')
      setSecretValue('')
      setError(null)
      onClose()
    }
  }

  const handleKeyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Auto-uppercase and replace spaces with underscores
    const value = e.target.value.toUpperCase().replace(/\s/g, '_')
    setSecretKey(value)
    setError(null)
  }

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Add Secret">
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <Input
            id="secret-key"
            name="secret-key"
            type="text"
            label="Key"
            placeholder="DATABASE_URL"
            value={secretKey}
            onChange={handleKeyChange}
            error={error && !secretValue ? error : undefined}
            required
            autoFocus
            disabled={loading}
          />
          <p className="mt-2 text-sm text-gray-600">
            Use uppercase letters, numbers, and underscores only
          </p>
        </div>

        <div>
          <label htmlFor="secret-value" className="block text-sm font-bold mb-2">
            Value
          </label>
          <textarea
            id="secret-value"
            name="secret-value"
            rows={4}
            className="input-brutal min-h-[100px] resize-y"
            placeholder="**********************************/dbname"
            value={secretValue}
            onChange={(e) => {
              setSecretValue(e.target.value)
              setError(null)
            }}
            required
            disabled={loading}
          />
          <p className="mt-2 text-sm text-gray-600">
            The value will be encrypted before storage
          </p>
        </div>

        {error && (
          <div className="p-3 bg-red-50 border-2 border-red-500 text-red-700 text-sm">
            {error}
          </div>
        )}

        <div className="flex gap-4 justify-end">
          <Button
            type="button"
            variant="secondary"
            onClick={handleClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            loading={loading}
          >
            Add Secret
          </Button>
        </div>
      </form>
    </Modal>
  )
}