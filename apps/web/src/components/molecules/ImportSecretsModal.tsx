'use client'

import { useState, useRef } from 'react'
import { Modal } from './Modal'
import { Button } from '@/components/atoms/Button'
import { parseEnvContent, ParsedEnvVar } from '@/lib/env/parser'

interface ImportSecretsModalProps {
  isOpen: boolean
  onClose: () => void
  onImport: (
    variables: ParsedEnvVar[], 
    duplicateStrategy: 'replace' | 'skip'
  ) => Promise<{ success: boolean; imported: number; skipped: number; errors: string[] }>
  existingKeys: string[]
}

type ImportMethod = 'paste' | 'upload'
type DuplicateStrategy = 'replace' | 'skip'

export function ImportSecretsModal({ 
  isOpen, 
  onClose, 
  onImport,
  existingKeys 
}: ImportSecretsModalProps) {
  const [method, setMethod] = useState<ImportMethod>('paste')
  const [content, setContent] = useState('')
  const [parseResult, setParseResult] = useState<ReturnType<typeof parseEnvContent> | null>(null)
  const [duplicateStrategy, setDuplicateStrategy] = useState<DuplicateStrategy>('skip')
  const [loading, setLoading] = useState(false)
  const [importResult, setImportResult] = useState<{ success: boolean; message: string } | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleContentChange = (newContent: string) => {
    setContent(newContent)
    setImportResult(null)
    
    if (newContent.trim()) {
      const result = parseEnvContent(newContent)
      setParseResult(result)
    } else {
      setParseResult(null)
    }
  }

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validate file size (1MB limit)
    if (file.size > 1024 * 1024) {
      setImportResult({ 
        success: false, 
        message: 'File must be less than 1MB' 
      })
      return
    }

    // Read file content
    try {
      const text = await file.text()
      handleContentChange(text)
      setMethod('upload')
    } catch {
      setImportResult({ 
        success: false, 
        message: 'Failed to read file. Make sure it\'s a text file.' 
      })
    }

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleImport = async () => {
    if (!parseResult || parseResult.errors.length > 0) return

    setLoading(true)
    setImportResult(null)

    try {
      const result = await onImport(parseResult.variables, duplicateStrategy)
      
      if (result.success) {
        const message = `Successfully imported ${result.imported} secrets${
          result.skipped > 0 ? ` (${result.skipped} skipped)` : ''
        }`
        setImportResult({ success: true, message })
        
        // Clear form after successful import
        setTimeout(() => {
          handleClose()
        }, 2000)
      } else {
        setImportResult({ 
          success: false, 
          message: result.errors.join(', ') || 'Import failed' 
        })
      }
    } catch {
      setImportResult({ 
        success: false, 
        message: 'An unexpected error occurred during import' 
      })
    }

    setLoading(false)
  }

  const handleClose = () => {
    if (!loading) {
      setContent('')
      setParseResult(null)
      setImportResult(null)
      setMethod('paste')
      onClose()
    }
  }

  const getDuplicateCount = () => {
    if (!parseResult) return 0
    const existingSet = new Set(existingKeys)
    return parseResult.variables.filter(v => existingSet.has(v.key)).length
  }

  const duplicateCount = getDuplicateCount()

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={handleClose} 
      title="Import Secrets"
      size="lg"
    >
      <div className="space-y-6">
        {/* Import method selector */}
        <div className="flex gap-2">
          <Button
            variant={method === 'paste' ? 'primary' : 'secondary'}
            size="sm"
            onClick={() => setMethod('paste')}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            Paste
          </Button>
          <Button
            variant={method === 'upload' ? 'primary' : 'secondary'}
            size="sm"
            onClick={() => setMethod('upload')}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
            Upload File
          </Button>
        </div>

        {/* Content input area */}
        {method === 'paste' ? (
          <div>
            <label className="block text-sm font-bold mb-2">
              Paste your .env content
            </label>
            <textarea
              className="input-brutal min-h-[200px] font-mono text-sm"
              placeholder="DATABASE_URL=postgresql://..."
              value={content}
              onChange={(e) => handleContentChange(e.target.value)}
              disabled={loading}
            />
          </div>
        ) : (
          <div>
            <input
              ref={fileInputRef}
              type="file"
              accept=".env,.txt,text/plain"
              onChange={handleFileUpload}
              className="hidden"
              id="env-file-upload"
            />
            <label
              htmlFor="env-file-upload"
              className="block border-2 border-dashed border-gray-400 rounded p-8 text-center cursor-pointer hover:border-black transition-colors"
            >
              <svg className="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
              <p className="text-gray-600">
                Drop your .env file here or click to browse
              </p>
              <p className="text-sm text-gray-500 mt-2">
                Maximum file size: 1MB
              </p>
            </label>
            {content && (
              <div className="mt-4">
                <p className="text-sm text-gray-600 mb-2">File loaded successfully</p>
                <details className="text-sm">
                  <summary className="cursor-pointer text-blue-600 hover:underline">
                    View content
                  </summary>
                  <pre className="mt-2 p-2 bg-gray-50 border-2 border-gray-200 rounded overflow-auto max-h-[200px]">
                    {content}
                  </pre>
                </details>
              </div>
            )}
          </div>
        )}

        {/* Parse results preview */}
        {parseResult && (
          <div>
            <h3 className="font-bold mb-2">Preview</h3>
            
            {/* Errors */}
            {parseResult.errors.length > 0 && (
              <div className="mb-4 p-3 bg-red-50 border-2 border-red-500">
                <p className="font-bold text-red-700 mb-2">
                  {parseResult.errors.length} error{parseResult.errors.length > 1 ? 's' : ''} found:
                </p>
                <ul className="text-sm text-red-600 space-y-1">
                  {parseResult.errors.map((error, i) => (
                    <li key={i}>
                      Line {error.line}: {error.message}
                      {error.content && (
                        <code className="block mt-1 p-1 bg-red-100 rounded text-xs">
                          {error.content}
                        </code>
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Valid variables */}
            {parseResult.variables.length > 0 && (
              <div className="space-y-2">
                <p className="text-sm text-gray-600">
                  {parseResult.variables.length} variable{parseResult.variables.length > 1 ? 's' : ''} found
                  {duplicateCount > 0 && ` (${duplicateCount} duplicate${duplicateCount > 1 ? 's' : ''})`}
                </p>
                <div className="max-h-[200px] overflow-y-auto border-2 border-gray-200 rounded">
                  {parseResult.variables.map((variable, i) => {
                    const isDuplicate = existingKeys.includes(variable.key)
                    return (
                      <div 
                        key={i} 
                        className={`
                          px-3 py-2 border-b border-gray-200 last:border-b-0
                          ${isDuplicate ? 'bg-yellow-50' : ''}
                        `}
                      >
                        <div className="flex items-center gap-2">
                          {isDuplicate ? (
                            <svg className="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                          ) : (
                            <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          )}
                          <code className="font-mono text-sm font-bold">{variable.key}</code>
                          {isDuplicate && (
                            <span className="text-xs text-yellow-600">(exists)</span>
                          )}
                        </div>
                        {variable.warning && (
                          <p className="text-xs text-gray-500 mt-1 ml-6">{variable.warning}</p>
                        )}
                      </div>
                    )
                  })}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Duplicate handling options */}
        {duplicateCount > 0 && parseResult && parseResult.errors.length === 0 && (
          <div>
            <h3 className="font-bold mb-2">Duplicate Handling</h3>
            <div className="space-y-2">
              <label className="flex items-center gap-2">
                <input
                  type="radio"
                  name="duplicate-strategy"
                  value="skip"
                  checked={duplicateStrategy === 'skip'}
                  onChange={() => setDuplicateStrategy('skip')}
                  className="radio-brutal"
                />
                <span>Skip existing (keep current values)</span>
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="radio"
                  name="duplicate-strategy"
                  value="replace"
                  checked={duplicateStrategy === 'replace'}
                  onChange={() => setDuplicateStrategy('replace')}
                  className="radio-brutal"
                />
                <span>Replace existing (overwrite with new values)</span>
              </label>
            </div>
          </div>
        )}

        {/* Import result message */}
        {importResult && (
          <div className={`
            p-3 border-2 
            ${importResult.success 
              ? 'bg-green-50 border-green-500 text-green-700' 
              : 'bg-red-50 border-red-500 text-red-700'
            }
          `}>
            {importResult.message}
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-4 justify-end">
          <Button
            variant="secondary"
            onClick={handleClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleImport}
            loading={loading}
            disabled={
              !parseResult || 
              parseResult.errors.length > 0 || 
              parseResult.variables.length === 0
            }
          >
            Import {parseResult?.variables.length || 0} Secrets
          </Button>
        </div>
      </div>
    </Modal>
  )
}