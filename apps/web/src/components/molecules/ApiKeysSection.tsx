'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/atoms/Button'
import { Modal } from '@/components/molecules/Modal'
import { generateApiKey, getApiKeys, revokeApiKey } from '@/lib/api-keys/api'
import type { <PERSON>pi<PERSON><PERSON> } from '@/lib/api-keys/types'

export default function ApiKeysSection() {
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([])
  const [loading, setLoading] = useState(true)
  const [showGenerateModal, setShowGenerateModal] = useState(false)
  const [showKeyModal, setShowKeyModal] = useState(false)
  const [newKeyName, setNewKeyName] = useState('')
  const [generatedKey, setGeneratedKey] = useState<{ key: string; name: string } | null>(null)

  useEffect(() => {
    loadApiKeys()
  }, [])

  async function loadApiKeys() {
    try {
      const keys = await getApiKeys()
      setApiKeys(keys)
    } catch (error) {
      console.error('Failed to load API keys:', error)
    } finally {
      setLoading(false)
    }
  }

  async function handleGenerateKey() {
    if (!newKeyName.trim()) return

    try {
      const result = await generateApiKey(newKeyName)
      setGeneratedKey({ key: result.key, name: newKeyName })
      setShowGenerateModal(false)
      setShowKeyModal(true)
      setNewKeyName('')
      await loadApiKeys()
    } catch (error) {
      console.error('Failed to generate API key:', error)
      alert('Failed to generate API key. Please try again.')
    }
  }

  async function handleRevokeKey(keyId: string) {
    if (!confirm('Are you sure you want to revoke this API key? This action cannot be undone.')) {
      return
    }

    try {
      await revokeApiKey(keyId)
      await loadApiKeys()
    } catch (error) {
      console.error('Failed to revoke API key:', error)
      alert('Failed to revoke API key. Please try again.')
    }
  }

  function copyToClipboard(text: string) {
    navigator.clipboard.writeText(text)
    alert('API key copied to clipboard!')
  }

  if (loading) {
    return <div>Loading API keys...</div>
  }

  return (
    <>
      <div className="mb-6">
        <Button
          onClick={() => setShowGenerateModal(true)}
          className="bg-black text-white hover:bg-gray-800"
        >
          Generate New Key
        </Button>
      </div>

      {apiKeys.length === 0 ? (
        <div className="text-center py-12 text-gray-500">
          <p className="mb-2">No API keys yet</p>
          <p className="text-sm">Generate your first API key to get started</p>
        </div>
      ) : (
        <div className="space-y-4">
          {apiKeys.map((key) => (
            <div
              key={key.id}
              className="border-2 border-black rounded-lg p-4 shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]"
            >
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-bold text-lg">{key.name}</h3>
                  <p className="text-gray-600 font-mono text-sm">
                    {key.key_prefix}... • Created {new Date(key.created_at).toLocaleDateString()}
                  </p>
                  {key.last_used_at && (
                    <p className="text-gray-500 text-sm">
                      Last used: {new Date(key.last_used_at).toLocaleString()}
                    </p>
                  )}
                </div>
                <Button
                  onClick={() => handleRevokeKey(key.id)}
                  variant="secondary"
                  className="text-red-600 border-red-600 hover:bg-red-50"
                >
                  Revoke
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Generate Key Modal */}
      <Modal
        isOpen={showGenerateModal}
        onClose={() => {
          setShowGenerateModal(false)
          setNewKeyName('')
        }}
        title="Generate New API Key"
      >
        <div className="space-y-4">
          <div>
            <label htmlFor="keyName" className="block text-sm font-medium mb-2">
              Name your API key:
            </label>
            <input
              id="keyName"
              type="text"
              value={newKeyName}
              onChange={(e) => setNewKeyName(e.target.value)}
              placeholder="e.g., Production API"
              className="w-full px-4 py-2 border-2 border-black rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
              onKeyDown={(e) => {
                if (e.key === 'Enter') handleGenerateKey()
              }}
            />
            <p className="text-sm text-gray-600 mt-2">
              This helps you identify the key later
            </p>
          </div>
          <div className="flex justify-end gap-3">
            <Button
              variant="secondary"
              onClick={() => {
                setShowGenerateModal(false)
                setNewKeyName('')
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleGenerateKey}
              disabled={!newKeyName.trim()}
              className="bg-black text-white hover:bg-gray-800"
            >
              Generate
            </Button>
          </div>
        </div>
      </Modal>

      {/* Display Generated Key Modal */}
      <Modal
        isOpen={showKeyModal}
        onClose={() => {
          setShowKeyModal(false)
          setGeneratedKey(null)
        }}
        title="API Key Generated"
      >
        <div className="space-y-4">
          <div className="bg-yellow-50 border-2 border-yellow-400 rounded-lg p-4">
            <p className="font-bold text-yellow-800 mb-2">
              ⚠️ Save this key - it won&apos;t be shown again!
            </p>
          </div>
          
          {generatedKey && (
            <>
              <div className="bg-gray-100 border-2 border-black rounded-lg p-4 font-mono text-sm break-all">
                {generatedKey.key}
              </div>
              
              <Button
                onClick={() => copyToClipboard(generatedKey.key)}
                className="w-full bg-black text-white hover:bg-gray-800"
              >
                📋 Copy to Clipboard
              </Button>
              
              <p className="text-sm text-gray-600">
                <strong>Name:</strong> {generatedKey.name}
              </p>
            </>
          )}
          
          <div className="flex justify-end">
            <Button
              onClick={() => {
                setShowKeyModal(false)
                setGeneratedKey(null)
              }}
              variant="primary"
            >
              I&apos;ve saved this key
            </Button>
          </div>
        </div>
      </Modal>
    </>
  )
}