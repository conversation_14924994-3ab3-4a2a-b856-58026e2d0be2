'use client'

import { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react'
import { useTeams, useCurrentTeam } from '@/lib/teams/hooks'
import type { TeamContextType, TeamWithDetails, CreateTeamInput, UpdateTeamInput, Team } from '@/lib/teams/types'

const TeamContext = createContext<TeamContextType | undefined>(undefined)

export function TeamProvider({ children }: { children: ReactNode }) {
  const { teams, loading, error, refetch, createTeam: createTeamHook, updateTeam: updateTeamHook } = useTeams()
  const { currentTeamId, switchTeam: switchTeamHook } = useCurrentTeam()
  const [currentTeam, setCurrentTeam] = useState<TeamWithDetails | null>(null)

  // Update current team when teams or currentTeamId changes
  useEffect(() => {
    if (teams.length > 0) {
      if (currentTeamId) {
        const team = teams.find(t => t.id === currentTeamId)
        if (team) {
          setCurrentTeam(team)
        } else {
          // If saved team not found, use first team
          setCurrentTeam(teams[0])
          switchTeamHook(teams[0].id)
        }
      } else {
        // No saved team, use first team
        setCurrentTeam(teams[0])
        switchTeamHook(teams[0].id)
      }
    }
  }, [teams, currentTeamId, switchTeamHook])

  const switchTeam = useCallback(async (teamId: string) => {
    // Always allow switching - the team might not be in our current list yet
    // The page will reload and fetch the correct teams
    switchTeamHook(teamId)
  }, [switchTeamHook])

  const createTeam = useCallback(async (input: CreateTeamInput): Promise<Team> => {
    const newTeam = await createTeamHook(input)
    // Don't automatically switch to the new team to allow modal to close
    // User can manually switch if they want
    return newTeam
  }, [createTeamHook])

  const updateTeam = useCallback(async (teamId: string, input: UpdateTeamInput): Promise<Team> => {
    return await updateTeamHook(teamId, input)
  }, [updateTeamHook])

  const value: TeamContextType = {
    currentTeam,
    teams,
    switchTeam,
    createTeam,
    updateTeam,
    loading,
    error,
    refetch
  }

  return <TeamContext.Provider value={value}>{children}</TeamContext.Provider>
}

export function useTeamContext() {
  const context = useContext(TeamContext)
  if (context === undefined) {
    throw new Error('useTeamContext must be used within a TeamProvider')
  }
  return context
}