-- Migration for multi-team support enhancements

-- Update RLS policies for teams to support multiple teams per user

-- Drop existing team update policy
DROP POLICY IF EXISTS "Team admins can update team" ON teams;

-- Create new policy for team admins to update their teams
CREATE POLICY "Team admins can update teams" ON teams
  FOR UPDATE USING (
    id IN (
      SELECT team_id FROM team_members 
      WHERE user_id = auth.uid() 
      AND role = 'admin'
    )
  );

-- Add policy for team admins to delete teams (if needed)
CREATE POLICY "Team admins can delete teams" ON teams
  FOR DELETE USING (
    id IN (
      SELECT team_id FROM team_members 
      WHERE user_id = auth.uid() 
      AND role = 'admin'
    )
  );

-- Create a function to get team member count
CREATE OR REPLACE FUNCTION get_team_member_count(team_id UUID)
RETURNS INTEGER AS $$
BEGIN
  RETURN (
    SELECT COUNT(*)::INTEGER 
    FROM team_members 
    WHERE team_members.team_id = get_team_member_count.team_id
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get project count for a team
CREATE OR REPLACE FUNCTION get_team_project_count(team_id UUID)
RETURNS INTEGER AS $$
BEGIN
  RETURN (
    SELECT COUNT(*)::INTEGER 
    FROM projects 
    WHERE projects.team_id = get_team_project_count.team_id
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to check if team name is unique for user
CREATE OR REPLACE FUNCTION is_team_name_unique_for_user(user_id UUID, team_name TEXT, exclude_team_id UUID DEFAULT NULL)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN NOT EXISTS (
    SELECT 1 
    FROM teams t
    JOIN team_members tm ON t.id = tm.team_id
    WHERE tm.user_id = is_team_name_unique_for_user.user_id
    AND t.name = is_team_name_unique_for_user.team_name
    AND (exclude_team_id IS NULL OR t.id != exclude_team_id)
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get user's role in a team
CREATE OR REPLACE FUNCTION get_user_team_role(check_team_id UUID, check_user_id UUID)
RETURNS team_role AS $$
DECLARE
  user_role team_role;
BEGIN
  SELECT role INTO user_role
  FROM team_members 
  WHERE team_id = check_team_id 
  AND user_id = check_user_id;
  
  RETURN user_role;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add updated_at column to teams table if it doesn't exist
ALTER TABLE teams ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ NOT NULL DEFAULT now();

-- Create trigger for teams updated_at
DROP TRIGGER IF EXISTS update_teams_updated_at ON teams;
CREATE TRIGGER update_teams_updated_at BEFORE UPDATE ON teams
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create a view for team details including member and project counts
CREATE OR REPLACE VIEW team_details AS
SELECT 
  t.*,
  tm.user_id,
  tm.role,
  get_team_member_count(t.id) as member_count,
  get_team_project_count(t.id) as project_count
FROM teams t
JOIN team_members tm ON t.id = tm.team_id;

-- Grant access to the view
GRANT SELECT ON team_details TO authenticated;

-- Add RLS to the view (views inherit RLS from base tables)

-- Add policy to prevent last admin from leaving team
CREATE OR REPLACE FUNCTION prevent_last_admin_removal()
RETURNS TRIGGER AS $$
DECLARE
  admin_count INTEGER;
BEGIN
  -- Count remaining admins if this is a delete or role change from admin
  IF (TG_OP = 'DELETE' AND OLD.role = 'admin') OR 
     (TG_OP = 'UPDATE' AND OLD.role = 'admin' AND NEW.role != 'admin') THEN
    
    SELECT COUNT(*) INTO admin_count
    FROM team_members
    WHERE team_id = OLD.team_id
    AND role = 'admin'
    AND user_id != OLD.user_id;
    
    IF admin_count = 0 THEN
      RAISE EXCEPTION 'Cannot remove the last admin from a team';
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to prevent last admin removal
DROP TRIGGER IF EXISTS prevent_last_admin_removal_trigger ON team_members;
CREATE TRIGGER prevent_last_admin_removal_trigger
  BEFORE UPDATE OR DELETE ON team_members
  FOR EACH ROW EXECUTE FUNCTION prevent_last_admin_removal();

-- Add composite index for performance on team_members
CREATE INDEX IF NOT EXISTS idx_team_members_team_user ON team_members(team_id, user_id);

-- Add index on teams.name for faster searches
CREATE INDEX IF NOT EXISTS idx_teams_name ON teams(name);

-- Function to create a team and add the creator as admin in a single transaction
CREATE OR REPLACE FUNCTION create_team_with_admin(team_name TEXT, admin_user_id UUID)
RETURNS UUID AS $$
DECLARE
  new_team_id UUID;
BEGIN
  -- Check if team name is unique for user
  IF NOT is_team_name_unique_for_user(admin_user_id, team_name) THEN
    RAISE EXCEPTION 'You already have a team with this name';
  END IF;
  
  -- Create the team
  INSERT INTO teams (name)
  VALUES (team_name)
  RETURNING id INTO new_team_id;
  
  -- Add the creator as admin
  INSERT INTO team_members (team_id, user_id, role)
  VALUES (new_team_id, admin_user_id, 'admin');
  
  RETURN new_team_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on functions to authenticated users
GRANT EXECUTE ON FUNCTION get_team_member_count TO authenticated;
GRANT EXECUTE ON FUNCTION get_team_project_count TO authenticated;
GRANT EXECUTE ON FUNCTION is_team_name_unique_for_user TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_team_role TO authenticated;
GRANT EXECUTE ON FUNCTION create_team_with_admin TO authenticated;