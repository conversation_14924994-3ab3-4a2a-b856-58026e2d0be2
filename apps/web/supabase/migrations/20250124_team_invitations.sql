-- Migration for team invitation system

-- Create team invitations table
CREATE TABLE IF NOT EXISTS team_invitations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  team_id UUID NOT NULL REFERENCES teams(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  role team_role NOT NULL,
  token TEXT NOT NULL UNIQUE,
  invited_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  expires_at TIMESTAMPTZ NOT NULL DEFAULT (now() + interval '7 days'),
  accepted_at TIMESTAMPTZ,
  accepted_by UUID REFERENCES auth.users(id),
  UNIQUE(team_id, email)
);

-- Index for token lookup
CREATE INDEX IF NOT EXISTS idx_invitations_token ON team_invitations(token);

-- Index for team invitations lookup
CREATE INDEX IF NOT EXISTS idx_invitations_team_id ON team_invitations(team_id);

-- Index for email lookup
CREATE INDEX IF NOT EXISTS idx_invitations_email ON team_invitations(email);

-- Enable RLS
ALTER TABLE team_invitations ENABLE ROW LEVEL SECURITY;

-- RLS Policies for invitations

-- Team admins can view their team's invitations
CREATE POLICY "View team invitations" ON team_invitations
  FOR SELECT USING (
    team_id IN (
      SELECT team_id FROM team_members 
      WHERE user_id = auth.uid() 
      AND role = 'admin'
    )
  );

-- Team admins can create invitations
CREATE POLICY "Create invitations" ON team_invitations
  FOR INSERT WITH CHECK (
    team_id IN (
      SELECT team_id FROM team_members 
      WHERE user_id = auth.uid() 
      AND role = 'admin'
    )
    AND invited_by = auth.uid()
  );

-- Team admins can update invitations (for cancellation)
CREATE POLICY "Update invitations" ON team_invitations
  FOR UPDATE USING (
    team_id IN (
      SELECT team_id FROM team_members 
      WHERE user_id = auth.uid() 
      AND role = 'admin'
    )
  );

-- Team admins can delete invitations
CREATE POLICY "Delete invitations" ON team_invitations
  FOR DELETE USING (
    team_id IN (
      SELECT team_id FROM team_members 
      WHERE user_id = auth.uid() 
      AND role = 'admin'
    )
  );

-- Anyone can view invitations by token (for acceptance)
CREATE POLICY "View invitation by token" ON team_invitations
  FOR SELECT USING (
    token = current_setting('request.jwt.claims', true)::json->>'invitation_token'
    OR EXISTS (
      SELECT 1 FROM team_invitations ti 
      WHERE ti.token = team_invitations.token 
      AND ti.email = current_setting('request.jwt.claims', true)::json->>'email'
    )
  );

-- Function to generate secure invitation token
CREATE OR REPLACE FUNCTION generate_invitation_token()
RETURNS TEXT AS $$
DECLARE
  token TEXT;
BEGIN
  -- Generate a random token (32 bytes = 64 hex chars)
  token := encode(gen_random_bytes(32), 'hex');
  RETURN token;
END;
$$ LANGUAGE plpgsql;

-- Function to check if user can be invited to team
CREATE OR REPLACE FUNCTION can_invite_to_team(check_team_id UUID, check_email TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if user is already a member
  IF EXISTS (
    SELECT 1 FROM team_members tm
    JOIN auth.users u ON tm.user_id = u.id
    WHERE tm.team_id = check_team_id
    AND u.email = check_email
  ) THEN
    RETURN FALSE;
  END IF;
  
  -- Check if there's already a pending invitation
  IF EXISTS (
    SELECT 1 FROM team_invitations
    WHERE team_id = check_team_id
    AND email = check_email
    AND accepted_at IS NULL
    AND expires_at > now()
  ) THEN
    RETURN FALSE;
  END IF;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to accept invitation
CREATE OR REPLACE FUNCTION accept_team_invitation(invitation_token TEXT, accepting_user_id UUID)
RETURNS TABLE(team_id UUID, role team_role) AS $$
DECLARE
  inv RECORD;
BEGIN
  -- Find the invitation
  SELECT * INTO inv
  FROM team_invitations
  WHERE token = invitation_token
  AND accepted_at IS NULL
  AND expires_at > now();
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Invalid or expired invitation';
  END IF;
  
  -- Check if user email matches (for security)
  IF NOT EXISTS (
    SELECT 1 FROM auth.users
    WHERE id = accepting_user_id
    AND email = inv.email
  ) THEN
    RAISE EXCEPTION 'Invitation email does not match user email';
  END IF;
  
  -- Check if user is already a member
  IF EXISTS (
    SELECT 1 FROM team_members
    WHERE team_id = inv.team_id
    AND user_id = accepting_user_id
  ) THEN
    RAISE EXCEPTION 'User is already a member of this team';
  END IF;
  
  -- Add user to team
  INSERT INTO team_members (team_id, user_id, role)
  VALUES (inv.team_id, accepting_user_id, inv.role);
  
  -- Mark invitation as accepted
  UPDATE team_invitations
  SET accepted_at = now(), accepted_by = accepting_user_id
  WHERE id = inv.id;
  
  -- Return team info
  RETURN QUERY SELECT inv.team_id, inv.role;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to cleanup expired invitations (can be called periodically)
CREATE OR REPLACE FUNCTION cleanup_expired_invitations()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM team_invitations
  WHERE accepted_at IS NULL
  AND expires_at < now();
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION can_invite_to_team TO authenticated;
GRANT EXECUTE ON FUNCTION accept_team_invitation TO authenticated;
GRANT EXECUTE ON FUNCTION generate_invitation_token TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_expired_invitations TO service_role;