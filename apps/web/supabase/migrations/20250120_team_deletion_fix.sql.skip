-- Drop the existing function if it exists
DROP FUNCTION IF EXISTS delete_team_cascade(UUID);

-- Function to delete a team and bypass the "last admin" constraint
-- This is needed because CASCADE deletion of team_members triggers the constraint
CREATE OR REPLACE FUNCTION delete_team_cascade(p_team_id UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_user_id UUID;
  v_team_count INT;
BEGIN
  -- Get the current user
  v_user_id := auth.uid();
  
  IF v_user_id IS NULL THEN
    RAISE EXCEPTION 'Not authenticated';
  END IF;
  
  -- Check if user is admin of the team
  IF NOT EXISTS (
    SELECT 1 FROM team_members
    WHERE team_id = p_team_id
    AND user_id = v_user_id
    AND role = 'admin'
  ) THEN
    RAISE EXCEPTION 'Only team admins can delete teams';
  END IF;
  
  -- Check if this is the user's last team
  SELECT COUNT(*) INTO v_team_count
  FROM team_members
  WHERE user_id = v_user_id;
  
  IF v_team_count <= 1 THEN
    RAISE EXCEPTION 'Cannot delete your last team';
  END IF;
  
  -- Delete team members first, but skip the trigger check by deleting non-admin members first
  -- This ensures the admin is deleted last, avoiding the trigger
  DELETE FROM team_members 
  WHERE team_id = p_team_id 
  AND role != 'admin';
  
  -- Now delete admin members
  DELETE FROM team_members 
  WHERE team_id = p_team_id;
  
  -- Finally delete the team (other CASCADE relations will be handled)
  DELETE FROM teams WHERE id = p_team_id;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION delete_team_cascade(UUID) TO authenticated;

-- Also update the prevent_last_admin_removal trigger to skip checks during team deletion
CREATE OR REPLACE FUNCTION check_last_admin()
RETURNS TRIGGER AS $$
BEGIN
  -- Skip check if we're deleting all members of a team (team deletion scenario)
  IF TG_OP = 'DELETE' AND NOT EXISTS (
    SELECT 1 FROM team_members 
    WHERE team_id = OLD.team_id 
    AND user_id != OLD.user_id
  ) THEN
    -- This is the last member, so we're likely deleting the team
    RETURN OLD;
  END IF;

  -- Original logic for preventing last admin removal
  IF TG_OP = 'DELETE' AND OLD.role = 'admin' THEN
    IF NOT EXISTS (
      SELECT 1 FROM team_members 
      WHERE team_id = OLD.team_id 
      AND user_id != OLD.user_id 
      AND role = 'admin'
    ) THEN
      RAISE EXCEPTION 'Cannot remove the last admin from a team';
    END IF;
  END IF;
  
  IF TG_OP = 'UPDATE' AND OLD.role = 'admin' AND NEW.role != 'admin' THEN
    IF NOT EXISTS (
      SELECT 1 FROM team_members 
      WHERE team_id = OLD.team_id 
      AND user_id != OLD.user_id 
      AND role = 'admin'
    ) THEN
      RAISE EXCEPTION 'Cannot remove the last admin from a team';
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;