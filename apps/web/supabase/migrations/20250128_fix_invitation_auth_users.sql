-- Fix permission denied for auth.users access in invitation functions

-- Create a helper function to check if email belongs to a team member
CREATE OR REPLACE FUNCTION is_email_team_member(check_team_id UUID, check_email TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  member_exists BOOLEAN;
BEGIN
  -- Use the team_members_with_email view which has proper access to emails
  SELECT EXISTS (
    SELECT 1 FROM team_members_with_email
    WHERE team_id = check_team_id
    AND user_email = check_email
  ) INTO member_exists;
  
  RETURN member_exists;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the can_invite_to_team function to use the helper
CREATE OR REPLACE FUNCTION can_invite_to_team(check_team_id UUID, check_email TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if user is already a member using the helper function
  IF is_email_team_member(check_team_id, check_email) THEN
    RETURN FALSE;
  END IF;
  
  -- Check if there's already a pending invitation
  IF EXISTS (
    SELECT 1 FROM team_invitations
    WHERE team_id = check_team_id
    AND email = check_email
    AND accepted_at IS NULL
    AND expires_at > now()
  ) THEN
    RETURN FALSE;
  END IF;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Also fix the accept_team_invitation function
CREATE OR REPLACE FUNCTION accept_team_invitation(invitation_token TEXT, accepting_user_id UUID)
RETURNS TABLE(team_id UUID, role team_role) AS $$
DECLARE
  inv RECORD;
  user_email TEXT;
BEGIN
  -- Find the invitation
  SELECT * INTO inv
  FROM team_invitations
  WHERE token = invitation_token
  AND accepted_at IS NULL
  AND expires_at > now();
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Invalid or expired invitation';
  END IF;
  
  -- Get the user's email using our helper function
  user_email := get_user_email(accepting_user_id);
  
  -- Check if user email matches (for security)
  IF user_email != inv.email THEN
    RAISE EXCEPTION 'Invitation email does not match user email';
  END IF;
  
  -- Check if user is already a member
  IF EXISTS (
    SELECT 1 FROM team_members
    WHERE team_id = inv.team_id
    AND user_id = accepting_user_id
  ) THEN
    RAISE EXCEPTION 'User is already a member of this team';
  END IF;
  
  -- Add user to team
  INSERT INTO team_members (team_id, user_id, role)
  VALUES (inv.team_id, accepting_user_id, inv.role);
  
  -- Mark invitation as accepted
  UPDATE team_invitations
  SET accepted_at = now(), accepted_by = accepting_user_id
  WHERE id = inv.id;
  
  -- Return team info
  RETURN QUERY SELECT inv.team_id, inv.role;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the new helper function
GRANT EXECUTE ON FUNCTION is_email_team_member TO authenticated;