-- Create environments table
CREATE TABLE IF NOT EXISTS environments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  UNIQUE(project_id, name)
);

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_environments_project_id ON environments(project_id);

-- Enable RLS
ALTER TABLE environments ENABLE ROW LEVEL SECURITY;

-- RLS Policies for environments
-- Users can view environments for projects they have access to
CREATE POLICY "View project environments" ON environments
  FOR SELECT USING (
    project_id IN (
      SELECT p.id FROM projects p
      JOIN team_members tm ON p.team_id = tm.team_id
      WHERE tm.user_id = auth.uid()
    )
  );

-- Only admins/managers can create environments
CREATE POLICY "Create environments" ON environments
  FOR INSERT WITH CHECK (
    project_id IN (
      SELECT p.id FROM projects p
      JOIN team_members tm ON p.team_id = tm.team_id
      WHERE tm.user_id = auth.uid()
      AND tm.role IN ('admin', 'manager')
    )
  );

-- Only admins/managers can update environments
CREATE POLICY "Update environments" ON environments
  FOR UPDATE USING (
    project_id IN (
      SELECT p.id FROM projects p
      JOIN team_members tm ON p.team_id = tm.team_id
      WHERE tm.user_id = auth.uid()
      AND tm.role IN ('admin', 'manager')
    )
  );

-- Only admins can delete environments
CREATE POLICY "Delete environments" ON environments
  FOR DELETE USING (
    project_id IN (
      SELECT p.id FROM projects p
      JOIN team_members tm ON p.team_id = tm.team_id
      WHERE tm.user_id = auth.uid()
      AND tm.role = 'admin'
    )
  );

-- Function to create default environment for new projects
CREATE OR REPLACE FUNCTION create_default_environment_for_project()
RETURNS TRIGGER AS $$
BEGIN
  -- Create a default 'development' environment for the new project
  INSERT INTO public.environments (project_id, name)
  VALUES (NEW.id, 'development');
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

-- Trigger to create default environment on project creation
CREATE TRIGGER on_project_created
  AFTER INSERT ON projects
  FOR EACH ROW EXECUTE FUNCTION create_default_environment_for_project();

-- Trigger for environments updated_at
CREATE TRIGGER update_environments_updated_at BEFORE UPDATE ON environments
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create default environments for existing projects
INSERT INTO environments (project_id, name)
SELECT id, 'development'
FROM projects
WHERE NOT EXISTS (
  SELECT 1 FROM environments WHERE project_id = projects.id
);