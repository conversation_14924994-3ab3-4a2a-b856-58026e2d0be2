-- Migration to enforce role-based access control via RLS policies

-- First, let's create a helper function to check user role in team
CREATE OR REPLACE FUNCTION get_user_role_in_team(check_team_id UUID)
RETURNS team_role AS $$
DECLARE
  user_role team_role;
BEGIN
  SELECT role INTO user_role
  FROM team_members
  WHERE team_id = check_team_id
  AND user_id = auth.uid();
  
  RETURN user_role;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper to check if user has at least a certain role level
CREATE OR REPLACE FUNCTION has_role_or_higher(check_team_id UUID, minimum_role team_role)
RETURNS BOOLEAN AS $$
DECLARE
  user_role team_role;
BEGIN
  user_role := get_user_role_in_team(check_team_id);
  
  IF user_role IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- Role hierarchy: admin > manager > member
  IF minimum_role = 'member' THEN
    RETURN TRUE; -- Any role is sufficient
  ELSIF minimum_role = 'manager' THEN
    RETURN user_role IN ('manager', 'admin');
  ELSIF minimum_role = 'admin' THEN
    RETURN user_role = 'admin';
  END IF;
  
  RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update Projects policies
DROP POLICY IF EXISTS "Team admins can create projects" ON projects;
CREATE POLICY "Only admins can create projects" ON projects
  FOR INSERT WITH CHECK (
    has_role_or_higher(team_id, 'admin')
  );

DROP POLICY IF EXISTS "Team admins can update projects" ON projects;
CREATE POLICY "Only admins can update projects" ON projects
  FOR UPDATE USING (
    has_role_or_higher(team_id, 'admin')
  );

DROP POLICY IF EXISTS "Team admins can delete projects" ON projects;
CREATE POLICY "Only admins can delete projects" ON projects
  FOR DELETE USING (
    has_role_or_higher(team_id, 'admin')
  );

-- Update Environments policies
DROP POLICY IF EXISTS "Create environments" ON environments;
CREATE POLICY "Only admins can create environments" ON environments
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM projects p
      WHERE p.id = project_id
      AND has_role_or_higher(p.team_id, 'admin')
    )
  );

DROP POLICY IF EXISTS "Update environments" ON environments;
CREATE POLICY "Only admins can update environments" ON environments
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM projects p
      WHERE p.id = project_id
      AND has_role_or_higher(p.team_id, 'admin')
    )
  );

DROP POLICY IF EXISTS "Delete environments" ON environments;
CREATE POLICY "Only admins can delete environments" ON environments
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM projects p
      WHERE p.id = project_id
      AND has_role_or_higher(p.team_id, 'admin')
    )
  );

-- Update Secrets policies
DROP POLICY IF EXISTS "Create secrets" ON secrets;
CREATE POLICY "Managers and admins can create secrets" ON secrets
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM environments e
      JOIN projects p ON e.project_id = p.id
      WHERE e.id = environment_id
      AND has_role_or_higher(p.team_id, 'manager')
    )
  );

DROP POLICY IF EXISTS "Update secrets" ON secrets;
CREATE POLICY "Managers and admins can update secrets" ON secrets
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM environments e
      JOIN projects p ON e.project_id = p.id
      WHERE e.id = environment_id
      AND has_role_or_higher(p.team_id, 'manager')
    )
  );

DROP POLICY IF EXISTS "Delete secrets" ON secrets;
CREATE POLICY "Managers and admins can delete secrets" ON secrets
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM environments e
      JOIN projects p ON e.project_id = p.id
      WHERE e.id = environment_id
      AND has_role_or_higher(p.team_id, 'manager')
    )
  );

-- API Keys policies (all authenticated users can manage their own keys)
-- No changes needed as these are user-specific, not team-specific

-- Team management policies already correctly restrict to admins
-- No changes needed

-- Grant execute permissions on new functions
GRANT EXECUTE ON FUNCTION get_user_role_in_team TO authenticated;
GRANT EXECUTE ON FUNCTION has_role_or_higher TO authenticated;