-- Fix infinite recursion in team_invitations policies

-- Drop the problematic policy
DROP POLICY IF EXISTS "View invitation by token" ON team_invitations;

-- Replace with a simpler policy that doesn't self-reference
CREATE POLICY "View invitation by token" ON team_invitations
  FOR SELECT USING (
    -- Allow viewing if the email matches the authenticated user's email
    email = (SELECT email FROM auth.users WHERE id = auth.uid())
    -- OR if user is an admin of the team
    OR has_role_or_higher(team_id, 'admin')
  );

-- Update other invitation policies to use the new helper functions
DROP POLICY IF EXISTS "View team invitations" ON team_invitations;
CREATE POLICY "<PERSON><PERSON> can view team invitations" ON team_invitations
  FOR SELECT USING (
    has_role_or_higher(team_id, 'admin')
  );

DROP POLICY IF EXISTS "Create invitations" ON team_invitations;
CREATE POLICY "Ad<PERSON> can create invitations" ON team_invitations
  FOR INSERT WITH CHECK (
    has_role_or_higher(team_id, 'admin')
    AND invited_by = auth.uid()
  );

DROP POLICY IF EXISTS "Update invitations" ON team_invitations;
CREATE POLICY "<PERSON><PERSON> can update invitations" ON team_invitations
  FOR UPDATE USING (
    has_role_or_higher(team_id, 'admin')
  );

DROP POLICY IF EXISTS "Delete invitations" ON team_invitations;
CREATE POLICY "Admins can delete invitations" ON team_invitations
  FOR DELETE USING (
    has_role_or_higher(team_id, 'admin')
  );