-- Fix team_id ambiguous error and allow users to read team names for invitations

-- First, fix the ambiguous team_id in the policies
DROP POLICY IF EXISTS "Users can view their own invitations" ON team_invitations;

-- Recreate with explicit table reference
CREATE POLICY "Users can view their own invitations" ON team_invitations
  FOR SELECT USING (
    team_invitations.email = get_current_user_email()
  );

-- Allow users to read basic team info for teams they're invited to
CREATE POLICY "Users can view teams they are invited to" ON teams
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM team_invitations
      WHERE team_invitations.team_id = teams.id
      AND team_invitations.email = get_current_user_email()
      AND team_invitations.accepted_at IS NULL
      AND team_invitations.expires_at > now()
    )
  );

-- Fix other policies that might have ambiguous references
DROP POLICY IF EXISTS "View invitations" ON team_invitations;
DROP POLICY IF EXISTS "Ad<PERSON> can view team invitations" ON team_invitations;
CREATE POLICY "<PERSON><PERSON> can view team invitations" ON team_invitations
  FOR SELECT USING (
    has_role_or_higher(team_invitations.team_id, 'admin')
  );

DROP POLICY IF EXISTS "<PERSON><PERSON> can create invitations" ON team_invitations;
CREATE POLICY "Admins can create invitations" ON team_invitations
  FOR INSERT WITH CHECK (
    has_role_or_higher(team_invitations.team_id, 'admin')
    AND team_invitations.invited_by = auth.uid()
  );

DROP POLICY IF EXISTS "Admins can update invitations" ON team_invitations;
CREATE POLICY "Admins can update invitations" ON team_invitations
  FOR UPDATE USING (
    has_role_or_higher(team_invitations.team_id, 'admin')
  );

DROP POLICY IF EXISTS "Admins can delete invitations" ON team_invitations;
CREATE POLICY "Admins can delete invitations" ON team_invitations
  FOR DELETE USING (
    has_role_or_higher(team_invitations.team_id, 'admin')
  );