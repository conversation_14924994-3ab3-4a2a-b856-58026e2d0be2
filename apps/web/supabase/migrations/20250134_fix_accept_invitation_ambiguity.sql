-- Fix ambiguous column reference in accept_team_invitation function

CREATE OR REPLACE FUNCTION accept_team_invitation(invitation_token TEXT, accepting_user_id UUID)
RETURNS TABLE(team_id UUID, role team_role) AS $$
DECLARE
  inv RECORD;
  user_email TEXT;
BEGIN
  -- Find the invitation
  SELECT * INTO inv
  FROM team_invitations
  WHERE team_invitations.token = invitation_token
  AND team_invitations.accepted_at IS NULL
  AND team_invitations.expires_at > now();
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Invalid or expired invitation';
  END IF;
  
  -- Get the user's email using our helper function
  user_email := get_user_email(accepting_user_id);
  
  -- Check if user email matches (for security)
  IF user_email != inv.email THEN
    RAISE EXCEPTION 'Invitation email does not match user email';
  END IF;
  
  -- Check if user is already a member
  IF EXISTS (
    SELECT 1 FROM team_members tm
    WHERE tm.team_id = inv.team_id
    AND tm.user_id = accepting_user_id
  ) THEN
    RAISE EXCEPTION 'User is already a member of this team';
  END IF;
  
  -- Add user to team
  INSERT INTO team_members (team_id, user_id, role)
  VALUES (inv.team_id, accepting_user_id, inv.role);
  
  -- Mark invitation as accepted
  UPDATE team_invitations ti
  SET accepted_at = now(), accepted_by = accepting_user_id
  WHERE ti.id = inv.id;
  
  -- Return team info (use explicit column names to avoid ambiguity)
  RETURN QUERY SELECT inv.team_id AS team_id, inv.role AS role;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;