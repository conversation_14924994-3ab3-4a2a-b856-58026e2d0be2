-- Comprehensive fix for auth.users access issues

-- First, drop the problematic view policy
DROP POLICY IF EXISTS "View invitation by token or admin" ON team_invitations;
DROP POLICY IF EXISTS "Public can view invitation basics" ON team_invitations;

-- Create a simple view policy
CREATE POLICY "View invitations" ON team_invitations
  FOR SELECT USING (
    -- <PERSON><PERSON> can view their team's invitations
    has_role_or_higher(team_id, 'admin')
  );

-- Create a function to get current user email safely
CREATE OR REPLACE FUNCTION get_current_user_email()
RETURNS TEXT AS $$
DECLARE
  user_email TEXT;
BEGIN
  SELECT email INTO user_email
  FROM auth.users
  WHERE id = auth.uid();
  
  RETURN user_email;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the policies to add a separate one for users viewing their own invitations
CREATE POLICY "Users can view their own invitations" ON team_invitations
  FOR SELECT USING (
    email = get_current_user_email()
  );

-- Make sure the can_invite_to_team function doesn't have any auth.users references
-- This was already fixed in the previous migration, but let's ensure it's correct
CREATE OR REPLACE FUNCTION can_invite_to_team(check_team_id UUID, check_email TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if email is already a team member
  IF EXISTS (
    SELECT 1 FROM team_members_with_email
    WHERE team_id = check_team_id
    AND user_email = check_email
  ) THEN
    RETURN FALSE;
  END IF;
  
  -- Check if there's already a pending invitation
  IF EXISTS (
    SELECT 1 FROM team_invitations
    WHERE team_id = check_team_id
    AND email = check_email
    AND accepted_at IS NULL
    AND expires_at > now()
  ) THEN
    RETURN FALSE;
  END IF;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION get_current_user_email TO authenticated;

-- Also ensure all our helper functions are properly granted
GRANT EXECUTE ON FUNCTION get_user_email TO authenticated;
GRANT EXECUTE ON FUNCTION is_email_team_member TO authenticated;