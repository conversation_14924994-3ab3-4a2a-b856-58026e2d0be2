-- Create a view for team members with user email
-- This avoids the need to access auth.users directly from the client

-- First, create a function to get user email
CREATE OR REPLACE FUNCTION get_user_email(user_id UUID)
RETURNS TEXT AS $$
DECLARE
  user_email TEXT;
BEGIN
  SELECT email INTO user_email
  FROM auth.users
  WHERE id = user_id;
  
  RETURN COALESCE(user_email, 'Unknown User');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a view that includes user email
CREATE OR REPLACE VIEW team_members_with_email AS
SELECT 
  tm.*,
  get_user_email(tm.user_id) as user_email
FROM team_members tm;

-- Grant access to the view
GRANT SELECT ON team_members_with_email TO authenticated;

-- Create RLS policies for the view (views inherit from base table but we can be explicit)
ALTER VIEW team_members_with_email SET (security_invoker = on);