-- Create secrets table for encrypted key-value storage
CREATE TABLE IF NOT EXISTS secrets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  environment_id UUID NOT NULL REFERENCES environments(id) ON DELETE CASCADE,
  key TEXT NOT NULL,
  encrypted_value TEXT NOT NULL, -- The encrypted secret value
  iv TEXT NOT NULL, -- Initialization Vector for AES
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  UNIQUE(environment_id, key)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_secrets_environment_id ON secrets(environment_id);
CREATE INDEX IF NOT EXISTS idx_secrets_key ON secrets(key);

-- Enable RLS
ALTER TABLE secrets ENABLE ROW LEVEL SECURITY;

-- RLS Policies for secrets

-- View secrets (all team members can view)
CREATE POLICY "View secrets" ON secrets
  FOR SELECT USING (
    environment_id IN (
      SELECT e.id FROM environments e
      JOIN projects p ON e.project_id = p.id
      JOIN team_members tm ON p.team_id = tm.team_id
      WHERE tm.user_id = auth.uid()
    )
  );

-- Create secrets (managers and admins only)
CREATE POLICY "Create secrets" ON secrets
  FOR INSERT WITH CHECK (
    environment_id IN (
      SELECT e.id FROM environments e
      JOIN projects p ON e.project_id = p.id
      JOIN team_members tm ON p.team_id = tm.team_id
      WHERE tm.user_id = auth.uid()
      AND tm.role IN ('admin', 'manager')
    )
  );

-- Update secrets (managers and admins only)
CREATE POLICY "Update secrets" ON secrets
  FOR UPDATE USING (
    environment_id IN (
      SELECT e.id FROM environments e
      JOIN projects p ON e.project_id = p.id
      JOIN team_members tm ON p.team_id = tm.team_id
      WHERE tm.user_id = auth.uid()
      AND tm.role IN ('admin', 'manager')
    )
  );

-- Delete secrets (managers and admins only)
CREATE POLICY "Delete secrets" ON secrets
  FOR DELETE USING (
    environment_id IN (
      SELECT e.id FROM environments e
      JOIN projects p ON e.project_id = p.id
      JOIN team_members tm ON p.team_id = tm.team_id
      WHERE tm.user_id = auth.uid()
      AND tm.role IN ('admin', 'manager')
    )
  );

-- Trigger for updated_at
CREATE TRIGGER update_secrets_updated_at BEFORE UPDATE ON secrets
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant permissions
GRANT ALL ON secrets TO anon, authenticated;

-- Function to track who created a secret
CREATE OR REPLACE FUNCTION set_secret_created_by()
RETURNS TRIGGER AS $$
BEGIN
  NEW.created_by = auth.uid();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically set created_by
CREATE TRIGGER set_secret_created_by_trigger
  BEFORE INSERT ON secrets
  FOR EACH ROW EXECUTE FUNCTION set_secret_created_by();