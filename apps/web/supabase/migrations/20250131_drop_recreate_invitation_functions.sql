-- Drop and recreate all invitation-related functions to ensure no auth.users references

-- Drop existing functions
DROP FUNCTION IF EXISTS can_invite_to_team(UUID, TEXT);
DROP FUNCTION IF EXISTS is_email_team_member(UUID, TEXT);

-- Recreate is_email_team_member without any auth.users reference
CREATE OR REPLACE FUNCTION is_email_team_member(check_team_id UUID, check_email TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  member_exists BOOLEAN;
BEGIN
  -- Use the team_members_with_email view which has proper access to emails
  SELECT EXISTS (
    SELECT 1 FROM team_members_with_email
    WHERE team_id = check_team_id
    AND user_email = check_email
  ) INTO member_exists;
  
  RETURN COALESCE(member_exists, FALSE);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate can_invite_to_team without any auth.users reference
CREATE OR REPLACE FUNCTION can_invite_to_team(check_team_id UUID, check_email TEXT)
R<PERSON>URNS BOOLEAN AS $$
DECLARE
  is_member BO<PERSON><PERSON><PERSON>;
  has_invitation BOOLEAN;
BEGIN
  -- Check if email is already a team member
  is_member := is_email_team_member(check_team_id, check_email);
  IF is_member THEN
    RETURN FALSE;
  END IF;
  
  -- Check if there's already a pending invitation
  SELECT EXISTS (
    SELECT 1 FROM team_invitations
    WHERE team_id = check_team_id
    AND email = check_email
    AND accepted_at IS NULL
    AND expires_at > now()
  ) INTO has_invitation;
  
  IF has_invitation THEN
    RETURN FALSE;
  END IF;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Ensure all permissions are granted
GRANT EXECUTE ON FUNCTION is_email_team_member TO authenticated;
GRANT EXECUTE ON FUNCTION can_invite_to_team TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_email TO authenticated;
GRANT EXECUTE ON FUNCTION get_current_user_email TO authenticated;