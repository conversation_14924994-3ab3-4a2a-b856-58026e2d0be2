-- API Keys table
CREATE TABLE api_keys (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  key_hash TEXT NOT NULL, -- Store bcrypt hash of key
  key_prefix TEXT NOT NULL, -- First 8 chars for display
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  last_used_at TIMESTAMPTZ,
  revoked_at TIMESTAMPTZ,
  UNIQUE(key_hash)
);

-- Index for fast key lookup
CREATE INDEX idx_api_keys_hash ON api_keys(key_hash) WHERE revoked_at IS NULL;

-- Index for user's keys
CREATE INDEX idx_api_keys_user_id ON api_keys(user_id) WHERE revoked_at IS NULL;

-- Enable RLS
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Users can only see their own keys
CREATE POLICY "Users view own keys" ON api_keys
  FOR SELECT USING (user_id = auth.uid());

-- Users can only create their own keys
CREATE POLICY "Users create own keys" ON api_keys
  FOR INSERT WITH CHECK (user_id = auth.uid());

-- Users can only revoke their own keys
CREATE POLICY "Users revoke own keys" ON api_keys
  FOR UPDATE USING (user_id = auth.uid());

-- Create function to verify API key
CREATE OR REPLACE FUNCTION verify_api_key(key_hash TEXT)
RETURNS TABLE (user_id UUID, key_id UUID) AS $$
BEGIN
  RETURN QUERY
  SELECT ak.user_id, ak.id
  FROM api_keys ak
  WHERE ak.key_hash = verify_api_key.key_hash
    AND ak.revoked_at IS NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to update last_used_at
CREATE OR REPLACE FUNCTION update_api_key_last_used(key_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE api_keys
  SET last_used_at = now()
  WHERE id = update_api_key_last_used.key_id
    AND revoked_at IS NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;