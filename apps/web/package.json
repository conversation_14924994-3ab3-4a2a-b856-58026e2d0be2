{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@supabase/ssr": "^0.1.0", "@supabase/supabase-js": "^2.39.0", "bcryptjs": "^3.0.2", "ezenv-sdk": "^1.0.0", "next": "15.4.2", "react": "19.1.0", "react-dom": "19.1.0", "zustand": "^4.5.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.2", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "tailwindcss": "^4", "typescript": "^5"}}