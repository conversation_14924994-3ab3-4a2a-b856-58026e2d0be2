/_ From Uiverse.io by 0xnihilism _/
.card {
width: 300px;
padding: 20px;
background: #fff;
border: 6px solid #000;
box-shadow: 12px 12px 0 #000;
transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
transform: translate(-5px, -5px);
box-shadow: 17px 17px 0 #000;
}

.card\_\_title {
font-size: 32px;
font-weight: 900;
color: #000;
text-transform: uppercase;
margin-bottom: 15px;
display: block;
position: relative;
overflow: hidden;
}

.card\_\_title::after {
content: "";
position: absolute;
bottom: 0;
left: 0;
width: 90%;
height: 3px;
background-color: #000;
transform: translateX(-100%);
transition: transform 0.3s;
}

.card:hover .card\_\_title::after {
transform: translateX(0);
}

.card\_\_content {
font-size: 16px;
line-height: 1.4;
color: #000;
margin-bottom: 20px;
}

.card\_\_form {
display: flex;
flex-direction: column;
gap: 15px;
}

.card\_\_form input {
padding: 10px;
border: 3px solid #000;
font-size: 16px;
font-family: inherit;
transition: transform 0.3s;
width: calc(100% - 26px); /_ Adjust for padding and border _/
}

.card\_\_form input:focus {
outline: none;
transform: scale(1.05);
background-color: #000;
color: #ffffff;
}

.card\_\_button {
border: 3px solid #000;
background: #000;
color: #fff;
padding: 10px;
font-size: 18px;
left: 20%;
font-weight: bold;
text-transform: uppercase;
cursor: pointer;
position: relative;
overflow: hidden;
transition: transform 0.3s;
width: 50%;
height: 100%;
}

.card\_\_button::before {
content: "Sure?";
position: absolute;
top: 0;
left: 0;
width: 100%;
height: 105%;
background-color: #5ad641;
color: #000;
display: flex;
align-items: center;
justify-content: center;
transform: translateY(100%);
transition: transform 0.3s;
}

.card\_\_button:hover::before {
transform: translateY(0);
}

.card\_\_button:active {
transform: scale(0.95);
}

@keyframes glitch {
0% {
transform: translate(2px, 2px);
}
25% {
transform: translate(-2px, -2px);
}
50% {
transform: translate(-2px, 2px);
}
75% {
transform: translate(2px, -2px);
}
100% {
transform: translate(2px, 2px);
}
}

.glitch {
animation: glitch 0.3s infinite;
}
