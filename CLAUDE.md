# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

SecureEnv Platform (EzEnv) - A secure environment variable management platform for development teams. Project setup is complete with all dependencies and infrastructure configured.

## Tech Stack

- **Frontend**: Next.js 14+ with App Router, TypeScript 5.x, React
- **Styling**: Tailwind CSS with Neobrutalist design system
- **Backend**: Supabase (PostgreSQL, Auth, Edge Functions)
- **State Management**: Zustand
- **Testing**: Jest, React Testing Library, Playwright

## Development Commands

Development commands:
```bash
npm install          # Install dependencies
npm run dev          # Start development server
npm run build        # Build for production
npm run test         # Run unit tests
npm run e2e          # Run end-to-end tests
npm run lint         # Lint code
npm run typecheck    # Run TypeScript checks
```

## Architecture

### Monorepo Structure
All components (web UI, NPM package) in single repository.

### Directory Structure
```
/src
├── app/               # Next.js App Router
│   ├── (auth)/       # Public auth routes
│   ├── (main)/       # Protected routes
│   └── api/          # API routes
├── components/       # React components
│   ├── atoms/        # Basic UI components
│   └── molecules/    # Composite components
├── lib/              # Utilities and configurations
├── store/            # Zustand state management
└── types/            # TypeScript definitions
```

### Key Architectural Decisions
1. **Serverless**: Leveraging Supabase BaaS for all backend needs
2. **Security First**: Application-level encryption (AES-256) for all secrets
3. **Row Level Security**: Database-level access control via Supabase RLS
4. **JWT Authentication**: Short-lived tokens for API access

## Core Features

1. **Projects & Environments**: Container-based organization with dev/staging/prod environments
2. **Team Management**: RBAC with Admin, Manager, Member roles
3. **Secrets Management**: Encrypted key-value storage with version history
4. **Programmatic Access**: NPM package for fetching secrets via API

## Design System

**Neobrutalist** aesthetic:
- High contrast (black/white with single bold accent)
- Hard shadows: `shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]`
- Sharp borders: `border-2 border-black`
- Sans-serif for UI, monospace for code/secrets
- No gradients or complex animations

## Database Schema

Key tables:
- `teams`: Group users and projects
- `team_members`: Junction table with roles
- `projects`: Belong to teams
- `environments`: Within projects (dev, staging, production)
- `secrets`: Encrypted key-value pairs

## Security Requirements

1. All secrets encrypted before storage
2. RLS policies enforce access control
3. Input validation on all Edge Functions
4. HTTPS only
5. Never log or expose secrets in plain text

## Testing Strategy

- E2E tests for critical user journeys (login, project creation, secret management)
- Component testing for all UI components
- Integration tests for Supabase Edge Functions

## Environment Variables

Required for development:
```
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
```

## Important Notes

1. Project setup is complete - Next.js, TypeScript, Tailwind CSS, Supabase, and testing infrastructure are configured
2. Follow Neobrutalist design patterns from css-ref.md
3. Implement security patterns from technical-architecture.md
4. UI/UX requirements detailed in frontend-specs.md
5. Product requirements in PRD.md
6. Use pnpm instead of npm