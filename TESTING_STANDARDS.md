# EzEnv Testing Standards & Requirements

## Testing Philosophy

1. **Test Behavior, Not Implementation**: Focus on what the code does, not how
2. **Test Public APIs**: Don't test private methods directly
3. **Meaningful Tests**: Each test should validate real requirements
4. **Fast & Reliable**: Tests should run quickly and consistently
5. **Clear Failure Messages**: Failed tests should clearly indicate the problem

## Testing Pyramid

```
         /\
        /E2E\        (5%) - Critical user journeys
       /------\
      /  Integ  \    (20%) - API & database tests  
     /------------\
    /     Unit     \ (75%) - Component & function tests
   /----------------\
```

## Unit Testing Standards

### Component Testing
```typescript
// ✅ Good - Comprehensive component test
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ProjectCard } from '@/components/molecules/project-card';

describe('ProjectCard', () => {
  const mockProject = {
    id: '123',
    name: 'Test Project',
    description: 'Test description',
    environmentCount: 3,
    secretCount: 10,
    lastUpdated: '2024-01-01T00:00:00Z'
  };

  it('renders project information correctly', () => {
    render(<ProjectCard project={mockProject} />);
    
    expect(screen.getByText('Test Project')).toBeInTheDocument();
    expect(screen.getByText('Test description')).toBeInTheDocument();
    expect(screen.getByText('3 environments')).toBeInTheDocument();
    expect(screen.getByText('10 secrets')).toBeInTheDocument();
  });

  it('handles click navigation', async () => {
    const user = userEvent.setup();
    const onNavigate = jest.fn();
    
    render(<ProjectCard project={mockProject} onNavigate={onNavigate} />);
    
    await user.click(screen.getByRole('article'));
    
    expect(onNavigate).toHaveBeenCalledWith(mockProject.id);
  });

  it('displays loading state during deletion', async () => {
    const user = userEvent.setup();
    const onDelete = jest.fn().mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 100))
    );
    
    render(<ProjectCard project={mockProject} onDelete={onDelete} />);
    
    await user.click(screen.getByRole('button', { name: /delete/i }));
    
    expect(screen.getByText(/deleting/i)).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.queryByText(/deleting/i)).not.toBeInTheDocument();
    });
  });

  it('handles deletion errors gracefully', async () => {
    const user = userEvent.setup();
    const onDelete = jest.fn().mockRejectedValue(new Error('Network error'));
    
    render(<ProjectCard project={mockProject} onDelete={onDelete} />);
    
    await user.click(screen.getByRole('button', { name: /delete/i }));
    
    await waitFor(() => {
      expect(screen.getByText(/failed to delete/i)).toBeInTheDocument();
    });
  });
});
```

### Hook Testing
```typescript
// ✅ Good - Custom hook test
import { renderHook, act } from '@testing-library/react';
import { useProjects } from '@/hooks/use-projects';

describe('useProjects', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('fetches projects on mount', async () => {
    const mockProjects = [{ id: '1', name: 'Project 1' }];
    global.fetch = jest.fn().mockResolvedValue({
      ok: true,
      json: async () => ({ data: mockProjects })
    });

    const { result } = renderHook(() => useProjects('team-123'));

    expect(result.current.loading).toBe(true);
    expect(result.current.projects).toEqual([]);

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(result.current.loading).toBe(false);
    expect(result.current.projects).toEqual(mockProjects);
  });

  it('handles fetch errors', async () => {
    global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));

    const { result } = renderHook(() => useProjects('team-123'));

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe('Failed to fetch projects');
    expect(result.current.projects).toEqual([]);
  });
});
```

### Utility Function Testing
```typescript
// ✅ Good - Utility function tests
import { encryptSecret, decryptSecret, validateProjectName } from '@/lib/utils';

describe('Encryption Utils', () => {
  const testKey = 'test-encryption-key-32-chars-long';

  it('encrypts and decrypts data correctly', async () => {
    const plaintext = 'my-secret-value';
    
    const encrypted = await encryptSecret(plaintext, testKey);
    expect(encrypted.ciphertext).not.toBe(plaintext);
    expect(encrypted.iv).toBeDefined();
    expect(encrypted.tag).toBeDefined();
    
    const decrypted = await decryptSecret(encrypted, testKey);
    expect(decrypted).toBe(plaintext);
  });

  it('produces different ciphertext for same input', async () => {
    const plaintext = 'my-secret-value';
    
    const encrypted1 = await encryptSecret(plaintext, testKey);
    const encrypted2 = await encryptSecret(plaintext, testKey);
    
    expect(encrypted1.ciphertext).not.toBe(encrypted2.ciphertext);
    expect(encrypted1.iv).not.toBe(encrypted2.iv);
  });

  it('throws error with invalid key on decrypt', async () => {
    const encrypted = await encryptSecret('test', testKey);
    
    await expect(
      decryptSecret(encrypted, 'wrong-key-32-chars-long-padding')
    ).rejects.toThrow('Decryption failed');
  });
});

describe('Validation Utils', () => {
  it('validates project names correctly', () => {
    expect(validateProjectName('valid-project')).toBe(true);
    expect(validateProjectName('valid_project_123')).toBe(true);
    expect(validateProjectName('VALID-PROJECT')).toBe(true);
    
    expect(validateProjectName('invalid project')).toBe(false);
    expect(validateProjectName('invalid@project')).toBe(false);
    expect(validateProjectName('')).toBe(false);
    expect(validateProjectName('a'.repeat(51))).toBe(false);
  });
});
```

## Integration Testing

### API Route Testing
```typescript
// ✅ Good - API integration test
import { createMocks } from 'node-mocks-http';
import handler from '@/app/api/projects/route';
import { createTestUser, cleanupDatabase } from '@/tests/helpers';

describe('/api/projects', () => {
  let testUser: any;
  
  beforeEach(async () => {
    testUser = await createTestUser();
  });
  
  afterEach(async () => {
    await cleanupDatabase();
  });

  describe('GET /api/projects', () => {
    it('returns user projects', async () => {
      const { req, res } = createMocks({
        method: 'GET',
        headers: {
          authorization: `Bearer ${testUser.token}`
        }
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(200);
      const data = JSON.parse(res._getData());
      expect(Array.isArray(data.projects)).toBe(true);
    });

    it('requires authentication', async () => {
      const { req, res } = createMocks({
        method: 'GET'
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(401);
      expect(JSON.parse(res._getData())).toEqual({
        error: 'Authentication required'
      });
    });
  });

  describe('POST /api/projects', () => {
    it('creates new project', async () => {
      const { req, res } = createMocks({
        method: 'POST',
        headers: {
          authorization: `Bearer ${testUser.token}`
        },
        body: {
          name: 'test-project',
          description: 'Test description'
        }
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(201);
      const data = JSON.parse(res._getData());
      expect(data.project).toMatchObject({
        name: 'test-project',
        description: 'Test description'
      });
    });

    it('validates input', async () => {
      const { req, res } = createMocks({
        method: 'POST',
        headers: {
          authorization: `Bearer ${testUser.token}`
        },
        body: {
          name: 'invalid name!',
          description: 'a'.repeat(201)
        }
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(400);
      const data = JSON.parse(res._getData());
      expect(data.errors).toBeDefined();
    });
  });
});
```

### Database Testing
```typescript
// ✅ Good - Database integration test
import { supabaseAdmin } from '@/lib/supabase-admin';
import { ProjectRepository } from '@/repositories/project-repository';

describe('ProjectRepository', () => {
  const repo = new ProjectRepository(supabaseAdmin);
  let teamId: string;
  
  beforeEach(async () => {
    // Setup test data
    const { data } = await supabaseAdmin
      .from('teams')
      .insert({ name: 'Test Team' })
      .select()
      .single();
    teamId = data.id;
  });

  afterEach(async () => {
    // Cleanup
    await supabaseAdmin
      .from('teams')
      .delete()
      .eq('id', teamId);
  });

  it('creates project with environments', async () => {
    const project = await repo.create({
      teamId,
      name: 'test-project',
      description: 'Test project'
    });

    expect(project.id).toBeDefined();
    expect(project.name).toBe('test-project');

    // Verify environments were created
    const { data: environments } = await supabaseAdmin
      .from('environments')
      .select('*')
      .eq('project_id', project.id);

    expect(environments).toHaveLength(3);
    expect(environments.map(e => e.name)).toEqual(
      expect.arrayContaining(['development', 'staging', 'production'])
    );
  });

  it('enforces unique project names per team', async () => {
    await repo.create({
      teamId,
      name: 'duplicate-project',
      description: 'First project'
    });

    await expect(
      repo.create({
        teamId,
        name: 'duplicate-project',
        description: 'Second project'
      })
    ).rejects.toThrow('Project name already exists');
  });
});
```

## End-to-End Testing

### Critical User Journeys
```typescript
// ✅ Good - E2E test for complete flow
import { test, expect } from '@playwright/test';
import { createTestAccount, deleteTestAccount } from './helpers';

test.describe('Project Management Flow', () => {
  let testEmail: string;
  let testPassword: string;

  test.beforeEach(async () => {
    const account = await createTestAccount();
    testEmail = account.email;
    testPassword = account.password;
  });

  test.afterEach(async () => {
    await deleteTestAccount(testEmail);
  });

  test('complete project lifecycle', async ({ page }) => {
    // 1. Login
    await page.goto('/login');
    await page.fill('input[name="email"]', testEmail);
    await page.fill('input[name="password"]', testPassword);
    await page.click('button[type="submit"]');
    await expect(page).toHaveURL('/dashboard');

    // 2. Create project
    await page.click('text="New Project"');
    await page.fill('input[name="name"]', 'e2e-test-project');
    await page.fill('textarea[name="description"]', 'E2E test project');
    await page.click('button:has-text("Create Project")');
    
    // 3. Verify project created
    await expect(page.locator('text="e2e-test-project"')).toBeVisible();

    // 4. Navigate to project
    await page.click('text="e2e-test-project"');
    await expect(page).toHaveURL(/\/projects\/[a-zA-Z0-9-]+/);

    // 5. Add secret
    await page.click('text="Add Secret"');
    await page.fill('input[name="key"]', 'API_KEY');
    await page.fill('input[name="value"]', 'secret-value-123');
    await page.click('button:has-text("Save Secret")');

    // 6. Verify secret added
    await expect(page.locator('text="API_KEY"')).toBeVisible();
    await expect(page.locator('text="••••••"')).toBeVisible();

    // 7. Delete project
    await page.click('button[aria-label="Project settings"]');
    await page.click('text="Delete Project"');
    await page.fill('input[name="confirmName"]', 'e2e-test-project');
    await page.click('button:has-text("Delete Forever")');

    // 8. Verify deletion
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('text="e2e-test-project"')).not.toBeVisible();
  });

  test('handles errors gracefully', async ({ page }) => {
    await page.goto('/login');
    
    // Test invalid credentials
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'wrongpassword');
    await page.click('button[type="submit"]');
    
    await expect(page.locator('text="Invalid credentials"')).toBeVisible();
    
    // Test network error
    await page.route('**/api/auth/login', route => route.abort());
    await page.fill('input[name="email"]', testEmail);
    await page.fill('input[name="password"]', testPassword);
    await page.click('button[type="submit"]');
    
    await expect(page.locator('text="Network error"')).toBeVisible();
  });
});
```

### Visual Regression Testing
```typescript
// ✅ Good - Visual regression test
test('visual regression - dashboard', async ({ page }) => {
  await page.goto('/dashboard');
  await expect(page).toHaveScreenshot('dashboard.png', {
    fullPage: true,
    animations: 'disabled'
  });
});

test('visual regression - dark mode', async ({ page }) => {
  await page.goto('/dashboard');
  await page.click('button[aria-label="Toggle theme"]');
  await expect(page).toHaveScreenshot('dashboard-dark.png', {
    fullPage: true,
    animations: 'disabled'
  });
});
```

## Test Data Management

### Test Fixtures
```typescript
// tests/fixtures/projects.ts
export const mockProjects = [
  {
    id: 'proj-1',
    name: 'test-project-1',
    description: 'Test project 1',
    teamId: 'team-1',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 'proj-2',
    name: 'test-project-2',
    description: 'Test project 2',
    teamId: 'team-1',
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z'
  }
];

// tests/fixtures/secrets.ts
export const mockSecrets = [
  {
    id: 'secret-1',
    key: 'DATABASE_URL',
    value: 'encrypted-value',
    environmentId: 'env-1',
    version: 1
  }
];
```

### Test Utilities
```typescript
// tests/utils/test-helpers.ts
export async function createTestUser(overrides = {}) {
  const user = {
    email: `test-${Date.now()}@example.com`,
    password: 'Test123!@#',
    ...overrides
  };
  
  // Create user in test database
  const { data } = await supabaseAdmin.auth.admin.createUser({
    email: user.email,
    password: user.password,
    email_confirm: true
  });
  
  return { ...user, id: data.user.id };
}

export async function createTestProject(teamId: string, overrides = {}) {
  const project = {
    name: `test-project-${Date.now()}`,
    description: 'Test project',
    teamId,
    ...overrides
  };
  
  const { data } = await supabaseAdmin
    .from('projects')
    .insert(project)
    .select()
    .single();
    
  return data;
}
```

## Testing Best Practices

### Test Organization
```
/tests
├── unit/           # Unit tests
├── integration/    # Integration tests
├── e2e/           # End-to-end tests
├── fixtures/      # Test data
├── utils/         # Test helpers
└── setup.ts       # Test configuration
```

### Naming Conventions
```typescript
// ✅ Good test names
it('should create project when valid data is provided')
it('should return 401 when user is not authenticated')
it('should display error message when network request fails')

// ❌ Bad test names
it('test project creation')
it('auth check')
it('error')
```

### Async Testing
```typescript
// ✅ Good - Proper async handling
it('fetches data asynchronously', async () => {
  const data = await fetchData();
  expect(data).toBeDefined();
});

// ✅ Good - Waiting for UI updates
it('shows loading state', async () => {
  render(<DataList />);
  
  expect(screen.getByText(/loading/i)).toBeInTheDocument();
  
  await waitFor(() => {
    expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
  });
  
  expect(screen.getByText(/data loaded/i)).toBeInTheDocument();
});
```

### Mocking Best Practices
```typescript
// ✅ Good - Mock at the boundary
jest.mock('@/lib/supabase', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => Promise.resolve({ data: [], error: null }))
      }))
    }))
  }
}));

// ❌ Bad - Over-mocking internals
jest.mock('./internal-function');
```

## Performance Testing

### Load Testing
```typescript
// tests/performance/load-test.ts
import { check } from 'k6';
import http from 'k6/http';

export const options = {
  stages: [
    { duration: '2m', target: 100 }, // Ramp up
    { duration: '5m', target: 100 }, // Stay at 100
    { duration: '2m', target: 0 },   // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests under 500ms
    http_req_failed: ['rate<0.1'],    // Error rate under 10%
  },
};

export default function () {
  const response = http.get('https://api.ezenv.dev/health');
  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 500ms': (r) => r.timings.duration < 500,
  });
}
```

## Test Coverage Requirements

### Coverage Targets
- Overall: 80%
- Statements: 80%
- Branches: 75%
- Functions: 80%
- Lines: 80%

### Critical Path Coverage
These areas must have 100% test coverage:
- Authentication flows
- Encryption/decryption logic
- API authorization checks
- Payment processing
- Data deletion operations

### Coverage Configuration
```javascript
// jest.config.js
module.exports = {
  coverageThreshold: {
    global: {
      branches: 75,
      functions: 80,
      lines: 80,
      statements: 80
    },
    './src/lib/crypto': {
      branches: 100,
      functions: 100,
      lines: 100,
      statements: 100
    }
  }
};
```