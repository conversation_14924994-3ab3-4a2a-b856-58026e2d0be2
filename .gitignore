# Dependencies
node_modules
.pnp
.pnp.js

# Testing
coverage
.nyc_output

# Next.js
.next/
out/
build/
dist/

# Production
*.production

# Misc
.DS_Store
*.pem
.vscode/
.idea/

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel

# Typescript
*.tsbuildinfo
next-env.d.ts

# Turbo
.turbo

# Supabase
**/supabase/.branches
**/supabase/.temp

# Test app (contains sensitive data)
test-sdk-app/

# Build outputs
packages/*/dist/

# Claude settings
.claude/settings.local.json

# OS files
Thumbs.db

# Lock files (keep only pnpm-lock.yaml)
package-lock.json
yarn.lock