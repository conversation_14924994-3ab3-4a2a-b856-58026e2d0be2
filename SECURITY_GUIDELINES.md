# EzEnv Security Guidelines & Best Practices

## Core Security Principles

1. **Zero Trust Architecture**: Never trust, always verify
2. **Defense in Depth**: Multiple layers of security
3. **Principle of Least Privilege**: Minimal access by default
4. **Secure by Default**: Security built-in, not bolted-on
5. **Fail Securely**: Errors should not compromise security

## Application Security

### Authentication & Authorization

#### Implementation Standards
```typescript
// ✅ Good - Check authentication at every level
export async function getProject(projectId: string) {
  // 1. Check authentication
  const { user } = await requireAuth();
  
  // 2. Check authorization
  const hasAccess = await checkProjectAccess(user.id, projectId);
  if (!hasAccess) {
    throw new UnauthorizedError('Access denied');
  }
  
  // 3. Fetch data
  return await db.projects.findOne({ id: projectId });
}

// ❌ Bad - Trusting client-side auth state
export async function getProject(projectId: string, userId: string) {
  // Never trust userId from client!
  return await db.projects.findOne({ id: projectId });
}
```

#### Session Management
- JWT tokens expire in 1 hour
- Refresh tokens expire in 7 days
- Implement token rotation on refresh
- Clear tokens on logout across all tabs
- Use httpOnly cookies for refresh tokens

### Encryption Standards

#### Secret Storage
```typescript
// All secrets must be encrypted before storage
import { encrypt, decrypt } from '@/lib/crypto';

// ✅ Encrypting secrets
export async function storeSecret(key: string, value: string) {
  const encrypted = await encrypt(value, process.env.ENCRYPTION_KEY!);
  
  await supabase
    .from('secrets')
    .insert({
      key,
      value: encrypted.ciphertext,
      iv: encrypted.iv,
      tag: encrypted.tag
    });
}

// ✅ Decrypting secrets
export async function getSecret(id: string) {
  const { data } = await supabase
    .from('secrets')
    .select('*')
    .eq('id', id)
    .single();
    
  if (!data) return null;
  
  return await decrypt({
    ciphertext: data.value,
    iv: data.iv,
    tag: data.tag
  }, process.env.ENCRYPTION_KEY!);
}
```

#### Encryption Requirements
- Algorithm: AES-256-GCM
- Key rotation every 90 days
- Unique IV for each encryption
- Store authentication tags
- Never log decrypted values

### Input Validation & Sanitization

#### Validation Rules
```typescript
// ✅ Good - Comprehensive validation
const projectSchema = z.object({
  name: z.string()
    .min(1, 'Name required')
    .max(50, 'Name too long')
    .regex(/^[a-zA-Z0-9-_]+$/, 'Invalid characters'),
  description: z.string()
    .max(200)
    .transform(val => sanitizeHtml(val, {
      allowedTags: [],
      allowedAttributes: {}
    })),
  teamId: z.string().uuid()
});

export async function createProject(input: unknown) {
  // Validate input
  const validated = projectSchema.parse(input);
  
  // Additional business logic validation
  await checkTeamMembership(validated.teamId);
  await checkProjectLimit(validated.teamId);
  
  // Create project
  return await db.projects.create(validated);
}
```

#### SQL Injection Prevention
```typescript
// ✅ Good - Parameterized queries
const { data } = await supabase
  .from('projects')
  .select('*')
  .eq('team_id', teamId)
  .eq('name', projectName);

// ❌ Bad - String concatenation
const query = `SELECT * FROM projects WHERE name = '${projectName}'`;
```

### XSS Prevention

#### Content Security Policy
```typescript
// next.config.js
const cspHeader = `
  default-src 'self';
  script-src 'self' 'unsafe-eval' 'unsafe-inline';
  style-src 'self' 'unsafe-inline';
  img-src 'self' blob: data:;
  font-src 'self';
  object-src 'none';
  base-uri 'self';
  form-action 'self';
  frame-ancestors 'none';
  upgrade-insecure-requests;
`
```

#### Output Encoding
```typescript
// ✅ Good - React automatically escapes
<div>{userInput}</div>

// ⚠️ Dangerous - Only if absolutely necessary
<div dangerouslySetInnerHTML={{ __html: sanitizedHtml }} />

// ✅ Safe HTML sanitization
import DOMPurify from 'isomorphic-dompurify';

const sanitized = DOMPurify.sanitize(userHtml, {
  ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'a'],
  ALLOWED_ATTR: ['href']
});
```

## API Security

### Rate Limiting
```typescript
// Edge Function middleware
const rateLimiter = new Map();

export async function rateLimit(req: Request) {
  const ip = req.headers.get('x-forwarded-for') || 'unknown';
  const key = `${ip}:${req.url}`;
  
  const current = rateLimiter.get(key) || { count: 0, resetAt: Date.now() + 60000 };
  
  if (Date.now() > current.resetAt) {
    current.count = 0;
    current.resetAt = Date.now() + 60000;
  }
  
  current.count++;
  
  if (current.count > 100) { // 100 requests per minute
    return new Response('Rate limit exceeded', { status: 429 });
  }
  
  rateLimiter.set(key, current);
}
```

### API Key Management
```typescript
// ✅ Secure API key generation
import { randomBytes } from 'crypto';

export function generateApiKey(): string {
  return `ezenv_${randomBytes(32).toString('hex')}`;
}

// ✅ API key validation
export async function validateApiKey(key: string) {
  // Hash the key before lookup
  const hashedKey = await hash(key);
  
  const { data } = await supabase
    .from('api_keys')
    .select('*')
    .eq('key_hash', hashedKey)
    .single();
    
  if (!data || data.expires_at < new Date()) {
    throw new UnauthorizedError('Invalid API key');
  }
  
  // Log usage for audit
  await logApiUsage(data.id);
  
  return data;
}
```

## Database Security

### Row Level Security (RLS)
```sql
-- Enable RLS on all tables
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE secrets ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_members ENABLE ROW LEVEL SECURITY;

-- Example policy: Users can only see their team's data
CREATE POLICY "team_isolation" ON projects
FOR ALL USING (
  team_id IN (
    SELECT team_id FROM team_members 
    WHERE user_id = auth.uid()
  )
);

-- Admin override for support
CREATE POLICY "admin_access" ON projects
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  )
);
```

### Data Privacy
```typescript
// ✅ Never expose sensitive data
export function sanitizeUser(user: User): PublicUser {
  return {
    id: user.id,
    email: maskEmail(user.email),
    name: user.name,
    avatar: user.avatar,
    // Never include: password_hash, mfa_secret, etc.
  };
}

// ✅ Audit logs without sensitive data
export async function logActivity(action: string, metadata: any) {
  await supabase.from('audit_logs').insert({
    user_id: auth.uid(),
    action,
    metadata: sanitizeMetadata(metadata), // Remove secrets
    ip_address: hashIp(request.ip), // Hash PII
    timestamp: new Date()
  });
}
```

## Infrastructure Security

### Environment Variables
```bash
# ✅ Good - Use proper secret management
ENCRYPTION_KEY=$(openssl rand -hex 32)
DATABASE_URL="postgresql://..."
SUPABASE_SERVICE_KEY="..."

# ❌ Bad - Never commit secrets
# .env file should be in .gitignore
```

### CORS Configuration
```typescript
// ✅ Restrictive CORS
export const corsHeaders = {
  'Access-Control-Allow-Origin': process.env.NEXT_PUBLIC_APP_URL,
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Max-Age': '86400',
};

// ❌ Bad - Too permissive
// 'Access-Control-Allow-Origin': '*'
```

### Security Headers
```typescript
// middleware.ts
export function middleware(request: NextRequest) {
  const response = NextResponse.next();
  
  // Security headers
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
  
  return response;
}
```

## Incident Response

### Security Incident Checklist
1. **Detect**: Monitor for anomalies
2. **Contain**: Isolate affected systems
3. **Investigate**: Determine scope and impact
4. **Remediate**: Fix vulnerabilities
5. **Recover**: Restore normal operations
6. **Review**: Post-mortem and improvements

### Breach Response Plan
```typescript
// Automatic response to detected breaches
export async function handleSecurityBreach(event: SecurityEvent) {
  // 1. Lock affected accounts
  await lockCompromisedAccounts(event.affectedUsers);
  
  // 2. Revoke tokens
  await revokeAllTokens(event.affectedUsers);
  
  // 3. Force password reset
  await forcePasswordReset(event.affectedUsers);
  
  // 4. Notify users
  await notifyUsers(event.affectedUsers, event.type);
  
  // 5. Log incident
  await logSecurityIncident(event);
  
  // 6. Alert security team
  await alertSecurityTeam(event);
}
```

## Security Testing

### Automated Security Checks
```json
// package.json scripts
{
  "security:audit": "pnpm audit",
  "security:scan": "semgrep --config=auto",
  "security:secrets": "trufflehog filesystem .",
  "security:deps": "snyk test"
}
```

### Manual Security Testing
- [ ] Test authentication bypass attempts
- [ ] Verify authorization on all endpoints
- [ ] Check for injection vulnerabilities
- [ ] Test rate limiting effectiveness
- [ ] Verify encryption implementation
- [ ] Check for sensitive data exposure
- [ ] Test session management
- [ ] Verify CSRF protection

## Compliance & Privacy

### GDPR Compliance
- Right to access data
- Right to rectification
- Right to erasure
- Right to data portability
- Privacy by design
- Data minimization

### Audit Logging
```typescript
// Comprehensive audit trail
interface AuditLog {
  id: string;
  timestamp: Date;
  userId: string;
  action: AuditAction;
  resourceType: ResourceType;
  resourceId: string;
  changes?: Record<string, any>;
  ipAddress: string; // Hashed
  userAgent: string;
  result: 'success' | 'failure';
  errorMessage?: string;
}
```

## Security Resources

### Regular Reviews
- Weekly dependency updates
- Monthly security audit
- Quarterly penetration testing
- Annual security training

### Security Contacts
- Security Team: <EMAIL>
- Bug Bounty: <EMAIL>
- Incident Response: <EMAIL>