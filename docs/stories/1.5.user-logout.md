# Story 1.5: User Logout

## Status
Ready for Review

## Story
**As an** authenticated user,
**I want** to be able to log out of the platform,
**so that** I can securely end my session.

## Acceptance Criteria
1. A 'Logout' button is available and visible to authenticated users within the application.
2. Clicking the 'Logout' button calls the Supabase authentication service to end the user's session.
3. Upon successful logout, the user is redirected to the login page.

## Tasks / Subtasks
- [x] Add logout button to UI (AC: 1)
  - [x] Add logout button to AppHeader user menu
  - [x] Style button consistently with design system
  - [x] Add logout icon (power/exit icon)
  - [x] Ensure button is keyboard accessible
- [x] Implement logout functionality (AC: 2)
  - [x] Add signOut method to auth helpers
  - [x] Call `supabase.auth.signOut()`
  - [x] Clear any local state/cache
  - [x] Handle logout errors gracefully
- [x] Handle post-logout flow (AC: 3)
  - [x] Redirect to `/login` after successful logout
  - [x] Show brief confirmation message
  - [x] Ensure all protected routes redirect properly
  - [x] Clear any persisted user data
- [x] Update auth context
  - [x] Add logout method to auth context
  - [x] Ensure user state is cleared
  - [x] Trigger re-render of auth-dependent components
  - [x] Handle any cleanup operations

## Dev Notes
### Dependencies on Previous Stories
This story depends on:
- Story 1.3: Auth context and helpers
- Story 1.4: AppHeader component where logout button will be placed

### Supabase Logout Implementation
From technical-architecture.md:
- Sessions are cleared by Supabase
- Cookies are automatically removed

Key logout method:
```typescript
// lib/supabase/auth.ts
export async function signOut() {
  const { error } = await supabase.auth.signOut()
  if (error) {
    console.error('Error signing out:', error)
    throw error
  }
}
```

### Logout Button Placement
From Story 1.4, the logout button should be in the AppHeader user menu:
```typescript
// components/AppHeader.tsx (partial)
<DropdownMenu>
  <DropdownMenuTrigger>
    {user.email}
  </DropdownMenuTrigger>
  <DropdownMenuContent>
    <DropdownMenuItem onClick={handleLogout}>
      <PowerIcon className="mr-2 h-4 w-4" />
      Logout
    </DropdownMenuItem>
  </DropdownMenuContent>
</DropdownMenu>
```

### Auth Context Update
The auth context from Story 1.3 should include:
```typescript
interface AuthContextType {
  user: User | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => Promise<void> // This method
}
```

### Logout Flow
1. User clicks logout button
2. Show loading state on button (optional)
3. Call `supabase.auth.signOut()`
4. Clear auth context user state
5. Redirect to `/login`
6. Optional: Show "You have been logged out" message

### Design Requirements
From frontend-specs.md - Neobrutalist style:
- Logout button in dropdown: simple text with icon
- Hover state: background color change
- Use consistent spacing and typography
- Power/exit icon should be simple and bold

### Error Handling
Rare but possible errors:
- Network failure: Still clear local state and redirect
- Server error: Log error but proceed with local cleanup
- Always ensure user is redirected to login

### State Cleanup
When logging out:
- Clear user from auth context
- Clear any Zustand stores (future stories)
- Clear any cached data
- Cancel any pending API requests

### Security Considerations
- Ensure complete session termination
- No user data should remain in memory
- All protected routes should immediately redirect
- Consider clearing browser history if sensitive

### Testing
**Testing Requirements:**
- Test logout button visibility when authenticated
- Test successful logout and redirect
- Test error handling during logout
- Test that all protected routes redirect after logout
- Test auth context state is cleared
- E2E test: login → navigate → logout flow
- Verify no user data remains after logout

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-19 | 1.0 | Initial story creation | Bob (Scrum Master) |
| 2025-07-20 | 1.1 | Completed implementation | James (Developer) |

## Dev Agent Record
### Agent Model Used
Claude 3.5 Sonnet (claude-3-5-sonnet-20241022)

### Debug Log References
[To be populated by Dev Agent]

### Completion Notes List
- SignOut method was already implemented in auth helpers
- Auth context already had logout functionality built in
- Added AuthProvider to root layout to make auth context available app-wide
- Wired up logout button in AppHeader to call signOut method
- Logout redirects to /login page as expected
- Auth state is cleared on logout via onAuthStateChange listener
- All protected routes will redirect to login after logout
- User dropdown menu closes immediately when logout is clicked

### File List
**Modified:**
- `/apps/web/src/app/layout.tsx` - Added AuthProvider wrapper
- `/apps/web/src/components/molecules/AppHeader.tsx` - Wired up logout functionality

**Already Implemented:**
- `/apps/web/src/lib/supabase/auth.ts` - signOut method already existed
- `/apps/web/src/contexts/AuthContext.tsx` - Full auth context with logout support

## QA Results
[To be populated by QA Agent]