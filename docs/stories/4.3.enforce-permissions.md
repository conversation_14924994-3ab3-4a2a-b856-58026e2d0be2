# Story 4.3: Enforce Access Permissions

## Status
Draft

## Story
**As a** developer,
**I want** the system to strictly enforce the defined user roles so that team members can only perform actions they are authorized for.

## Acceptance Criteria
1. Permissions are enforced on both the frontend UI (e.g., hiding/disabling buttons) and the backend API (rejecting unauthorized requests).
2. Member Role: Users with this role can only view projects/environments and retrieve secrets/API keys. They cannot create, edit, or delete anything.
3. Manager Role: Users with this role have all 'Member' permissions, and can also edit existing environments and secrets. They cannot create new environments or manage the team.
4. Admin Role: Users with this role have full access, including creating projects, environments, and managing team members.

## Tasks / Subtasks
- [ ] Implement frontend permission checks (AC: 1)
  - [ ] Create usePermissions hook
  - [ ] Add role-based UI conditionals
  - [ ] Hide/disable buttons based on role
  - [ ] Show permission tooltips
  - [ ] Update all CRUD components
- [ ] Enhance RLS policies (AC: 1, 2, 3, 4)
  - [ ] Review all existing policies
  - [ ] Add role-specific policies
  - [ ] Test policy effectiveness
  - [ ] Document policy matrix
  - [ ] Add policy unit tests
- [ ] Create permission service (AC: 2, 3, 4)
  - [ ] Define permission constants
  - [ ] Create permission checking functions
  - [ ] Add role hierarchy logic
  - [ ] Cache permission results
  - [ ] Handle team context
- [ ] Update UI components for each role
  - [ ] Member view restrictions
  - [ ] Manager edit capabilities
  - [ ] Admin full access
  - [ ] Consistent messaging
  - [ ] Graceful degradation
- [ ] Add comprehensive testing
  - [ ] Test each role's capabilities
  - [ ] Test permission boundaries
  - [ ] Test UI reflects permissions
  - [ ] Test API enforcement
  - [ ] Test edge cases

## Dev Notes
### Dependencies on Previous Stories
This story depends on:
- Story 4.2: Team roles are assigned
- All previous stories: Permissions affect all features

### Permission Matrix
| Feature | Member | Manager | Admin |
|---------|---------|----------|--------|
| View projects | ✅ | ✅ | ✅ |
| Create projects | ❌ | ❌ | ✅ |
| View environments | ✅ | ✅ | ✅ |
| Create environments | ❌ | ❌ | ✅ |
| View secrets | ✅ | ✅ | ✅ |
| Create/Edit secrets | ❌ | ✅ | ✅ |
| Delete secrets | ❌ | ✅ | ✅ |
| Export secrets | ✅ | ✅ | ✅ |
| Import secrets | ❌ | ✅ | ✅ |
| Manage team | ❌ | ❌ | ✅ |
| Generate API keys | ✅ | ✅ | ✅ |

### RLS Policy Updates
Update all existing policies to include role checks:

```sql
-- Projects: Only admins can create
CREATE POLICY "Create projects" ON projects
  FOR INSERT WITH CHECK (
    team_id IN (
      SELECT team_id FROM team_members 
      WHERE user_id = auth.uid() 
      AND role = 'admin'
    )
  );

-- Environments: Only admins can create
CREATE POLICY "Create environments" ON environments
  FOR INSERT WITH CHECK (
    project_id IN (
      SELECT p.id FROM projects p
      JOIN team_members tm ON p.team_id = tm.team_id
      WHERE tm.user_id = auth.uid()
      AND tm.role = 'admin'
    )
  );

-- Secrets: Managers and admins can modify
CREATE POLICY "Modify secrets" ON secrets
  FOR INSERT WITH CHECK (
    environment_id IN (
      SELECT e.id FROM environments e
      JOIN projects p ON e.project_id = p.id
      JOIN team_members tm ON p.team_id = tm.team_id
      WHERE tm.user_id = auth.uid()
      AND tm.role IN ('admin', 'manager')
    )
  );

-- Same pattern for UPDATE and DELETE
```

### Frontend Permission Hook
```typescript
// hooks/usePermissions.ts
interface Permissions {
  canCreateProject: boolean
  canCreateEnvironment: boolean
  canEditSecrets: boolean
  canDeleteSecrets: boolean
  canManageTeam: boolean
  canInviteMembers: boolean
  role: TeamRole
}

export function usePermissions(): Permissions {
  const { currentTeam, userRole } = useTeam()
  
  return useMemo(() => {
    const isAdmin = userRole === 'admin'
    const isManager = userRole === 'manager'
    const isMember = userRole === 'member'
    
    return {
      canCreateProject: isAdmin,
      canCreateEnvironment: isAdmin,
      canEditSecrets: isAdmin || isManager,
      canDeleteSecrets: isAdmin || isManager,
      canManageTeam: isAdmin,
      canInviteMembers: isAdmin,
      role: userRole
    }
  }, [userRole])
}
```

### UI Component Updates
Example of conditional rendering:
```typescript
// In ProjectsPage
const { canCreateProject } = usePermissions()

return (
  <div>
    <h1>Projects</h1>
    {canCreateProject ? (
      <Button onClick={openCreateModal}>
        Create New Project
      </Button>
    ) : (
      <Tooltip content="Only team admins can create projects">
        <Button disabled>
          Create New Project
        </Button>
      </Tooltip>
    )}
  </div>
)
```

### Permission Messages
Consistent messaging for unauthorized actions:
- "Only team admins can create projects"
- "Only team admins can create environments"
- "Only managers and admins can edit secrets"
- "You need manager permissions to perform this action"

### API Error Responses
When RLS policies block actions:
```json
{
  "error": "Permission denied",
  "message": "You don't have permission to perform this action",
  "required_role": "admin"
}
```

### Permission Service Implementation
```typescript
// lib/permissions/index.ts
export const PERMISSIONS = {
  PROJECT_CREATE: 'project:create',
  PROJECT_DELETE: 'project:delete',
  ENVIRONMENT_CREATE: 'environment:create',
  SECRET_CREATE: 'secret:create',
  SECRET_EDIT: 'secret:edit',
  SECRET_DELETE: 'secret:delete',
  TEAM_MANAGE: 'team:manage',
  MEMBER_INVITE: 'member:invite',
  MEMBER_REMOVE: 'member:remove'
} as const

export function hasPermission(
  role: TeamRole, 
  permission: string
): boolean {
  const rolePermissions = {
    admin: Object.values(PERMISSIONS),
    manager: [
      PERMISSIONS.SECRET_CREATE,
      PERMISSIONS.SECRET_EDIT,
      PERMISSIONS.SECRET_DELETE
    ],
    member: []
  }
  
  return rolePermissions[role]?.includes(permission) ?? false
}
```

### Testing Scenarios
For each role, test:
1. **Member Role**:
   - Can view all resources
   - Cannot create/edit/delete anything
   - Can generate own API keys
   - UI shows read-only state

2. **Manager Role**:
   - All member permissions
   - Can create/edit/delete secrets
   - Cannot create projects/environments
   - Cannot manage team

3. **Admin Role**:
   - Full access to everything
   - Can manage team members
   - Can create all resources
   - No restrictions

### Edge Cases
- User's role changed while using app
- Permissions in multi-team context
- Handling permission errors gracefully
- Caching permission state
- Real-time permission updates

### Security Considerations
- Never trust client-side permission checks alone
- Always enforce at database level with RLS
- Log permission violations for audit
- Clear permission errors without revealing system details
- Rate limit permission checks

### Testing
**Testing Requirements:**
- Unit test permission functions
- Test each RLS policy individually
- Test UI components show correct state per role
- Test API rejects unauthorized requests
- Integration test full permission flow
- Test role changes update UI immediately
- Test multi-team permission context
- E2E: Test each role's complete workflow

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-19 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record
### Agent Model Used
[To be populated by Dev Agent]

### Debug Log References
[To be populated by Dev Agent]

### Completion Notes List
[To be populated by Dev Agent]

### File List
[To be populated by Dev Agent]

## QA Results
[To be populated by QA Agent]