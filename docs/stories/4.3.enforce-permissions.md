# Story 4.3: Enforce Access Permissions

## Status
Draft

## Story
**As a** developer,
**I want** the system to strictly enforce the defined user roles so that team members can only perform actions they are authorized for.

## Acceptance Criteria
1. Permissions are enforced on both the frontend UI (e.g., hiding/disabling buttons) and the backend API (rejecting unauthorized requests).
2. Member Role: Users with this role can only view projects/environments and retrieve secrets/API keys. They cannot create, edit, or delete anything.
3. Manager Role: Users with this role have all 'Member' permissions, and can also edit existing environments and secrets. They cannot create new environments or manage the team.
4. Admin Role: Users with this role have full access, including creating projects, environments, and managing team members.

## Tasks / Subtasks
- [ ] Implement frontend permission checks (AC: 1)
  - [ ] Create usePermissions hook
  - [ ] Add role-based UI conditionals
  - [ ] Hide/disable buttons based on role
  - [ ] Show permission tooltips
  - [ ] Update all CRUD components
- [ ] Enhance RLS policies (AC: 1, 2, 3, 4)
  - [ ] Review all existing policies
  - [ ] Add role-specific policies
  - [ ] Test policy effectiveness
  - [ ] Document policy matrix
  - [ ] Add policy unit tests
- [ ] Create permission service (AC: 2, 3, 4)
  - [ ] Define permission constants
  - [ ] Create permission checking functions
  - [ ] Add role hierarchy logic
  - [ ] Cache permission results
  - [ ] Handle team context
- [ ] Update UI components for each role
  - [ ] Member view restrictions
  - [ ] Manager edit capabilities
  - [ ] Admin full access
  - [ ] Consistent messaging
  - [ ] Graceful degradation
- [ ] Add comprehensive testing
  - [ ] Test each role's capabilities
  - [ ] Test permission boundaries
  - [ ] Test UI reflects permissions
  - [ ] Test API enforcement
  - [ ] Test edge cases

## Dev Notes
### Dependencies on Previous Stories
This story depends on:
- Story 4.2: Team roles are assigned
- All previous stories: Permissions affect all features

### Permission Matrix
| Feature | Member | Manager | Admin |
|---------|---------|----------|--------|
| View projects | ✅ | ✅ | ✅ |
| Create projects | ❌ | ❌ | ✅ |
| View environments | ✅ | ✅ | ✅ |
| Create environments | ❌ | ❌ | ✅ |
| View secrets | ✅ | ✅ | ✅ |
| Create/Edit secrets | ❌ | ✅ | ✅ |
| Delete secrets | ❌ | ✅ | ✅ |
| Export secrets | ✅ | ✅ | ✅ |
| Import secrets | ❌ | ✅ | ✅ |
| Manage team | ❌ | ❌ | ✅ |
| Generate API keys | ✅ | ✅ | ✅ |

### RLS Policy Updates
Update all existing policies to include role checks:

```sql
-- Projects: Only admins can create
CREATE POLICY "Create projects" ON projects
  FOR INSERT WITH CHECK (
    team_id IN (
      SELECT team_id FROM team_members 
      WHERE user_id = auth.uid() 
      AND role = 'admin'
    )
  );

-- Environments: Only admins can create
CREATE POLICY "Create environments" ON environments
  FOR INSERT WITH CHECK (
    project_id IN (
      SELECT p.id FROM projects p
      JOIN team_members tm ON p.team_id = tm.team_id
      WHERE tm.user_id = auth.uid()
      AND tm.role = 'admin'
    )
  );

-- Secrets: Managers and admins can modify
CREATE POLICY "Modify secrets" ON secrets
  FOR INSERT WITH CHECK (
    environment_id IN (
      SELECT e.id FROM environments e
      JOIN projects p ON e.project_id = p.id
      JOIN team_members tm ON p.team_id = tm.team_id
      WHERE tm.user_id = auth.uid()
      AND tm.role IN ('admin', 'manager')
    )
  );

-- Same pattern for UPDATE and DELETE
```

### Frontend Permission Hook
```typescript
// hooks/usePermissions.ts
interface Permissions {
  canCreateProject: boolean
  canCreateEnvironment: boolean
  canEditSecrets: boolean
  canDeleteSecrets: boolean
  canManageTeam: boolean
  canInviteMembers: boolean
  role: TeamRole
}

export function usePermissions(): Permissions {
  const { currentTeam, userRole } = useTeam()
  
  return useMemo(() => {
    const isAdmin = userRole === 'admin'
    const isManager = userRole === 'manager'
    const isMember = userRole === 'member'
    
    return {
      canCreateProject: isAdmin,
      canCreateEnvironment: isAdmin,
      canEditSecrets: isAdmin || isManager,
      canDeleteSecrets: isAdmin || isManager,
      canManageTeam: isAdmin,
      canInviteMembers: isAdmin,
      role: userRole
    }
  }, [userRole])
}
```

### UI Component Updates
Example of conditional rendering:
```typescript
// In ProjectsPage
const { canCreateProject } = usePermissions()

return (
  <div>
    <h1>Projects</h1>
    {canCreateProject ? (
      <Button onClick={openCreateModal}>
        Create New Project
      </Button>
    ) : (
      <Tooltip content="Only team admins can create projects">
        <Button disabled>
          Create New Project
        </Button>
      </Tooltip>
    )}
  </div>
)
```

### Permission Messages
Consistent messaging for unauthorized actions:
- "Only team admins can create projects"
- "Only team admins can create environments"
- "Only managers and admins can edit secrets"
- "You need manager permissions to perform this action"

### API Error Responses
When RLS policies block actions:
```json
{
  "error": "Permission denied",
  "message": "You don't have permission to perform this action",
  "required_role": "admin"
}
```

### Permission Service Implementation
```typescript
// lib/permissions/index.ts
export const PERMISSIONS = {
  PROJECT_CREATE: 'project:create',
  PROJECT_DELETE: 'project:delete',
  ENVIRONMENT_CREATE: 'environment:create',
  SECRET_CREATE: 'secret:create',
  SECRET_EDIT: 'secret:edit',
  SECRET_DELETE: 'secret:delete',
  TEAM_MANAGE: 'team:manage',
  MEMBER_INVITE: 'member:invite',
  MEMBER_REMOVE: 'member:remove'
} as const

export function hasPermission(
  role: TeamRole, 
  permission: string
): boolean {
  const rolePermissions = {
    admin: Object.values(PERMISSIONS),
    manager: [
      PERMISSIONS.SECRET_CREATE,
      PERMISSIONS.SECRET_EDIT,
      PERMISSIONS.SECRET_DELETE
    ],
    member: []
  }
  
  return rolePermissions[role]?.includes(permission) ?? false
}
```

### Testing Scenarios
For each role, test:
1. **Member Role**:
   - Can view all resources
   - Cannot create/edit/delete anything
   - Can generate own API keys
   - UI shows read-only state

2. **Manager Role**:
   - All member permissions
   - Can create/edit/delete secrets
   - Cannot create projects/environments
   - Cannot manage team

3. **Admin Role**:
   - Full access to everything
   - Can manage team members
   - Can create all resources
   - No restrictions

### Edge Cases
- User's role changed while using app
- Permissions in multi-team context
- Handling permission errors gracefully
- Caching permission state
- Real-time permission updates

### Security Considerations
- Never trust client-side permission checks alone
- Always enforce at database level with RLS
- Log permission violations for audit
- Clear permission errors without revealing system details
- Rate limit permission checks

### Testing
**Testing Requirements:**
- Unit test permission functions
- Test each RLS policy individually
- Test UI components show correct state per role
- Test API rejects unauthorized requests
- Integration test full permission flow
- Test role changes update UI immediately
- Test multi-team permission context
- E2E: Test each role's complete workflow

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-19 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record
### Agent Model Used
[To be populated by Dev Agent]

### Debug Log References
[To be populated by Dev Agent]

### Completion Notes List
[To be populated by Dev Agent]

### File List
[To be populated by Dev Agent]

## QA Results
### Review Date: 2025-07-22
**Reviewer:** Quinn (Senior Developer & QA Architect)

### Overall Assessment: APPROVED WITH RECOMMENDATIONS ✅

This story is well-structured with clear acceptance criteria and comprehensive implementation guidance. The permission system design follows security best practices with proper frontend/backend separation.

### Code Quality & Architecture Review

**Strengths:**
1. **Dual-layer security**: Enforcing permissions at both UI and database levels (RLS)
2. **Clear permission matrix**: Well-defined role capabilities 
3. **Comprehensive RLS policies**: Proper SQL examples for each role
4. **Reusable permission hook**: Good React pattern with usePermissions
5. **Consistent error handling**: Standardized permission denial messages

**Areas for Improvement:**

1. **Performance Optimization**:
   - The RLS policies use nested subqueries which could impact performance
   - Recommendation: Add indexes on `team_members.user_id` and `team_members.role`
   - Consider materializing permission views for complex queries

2. **Caching Strategy**:
   - Dev notes mention "Cache permission results" but lack implementation details
   - Recommendation: Implement permission caching with proper invalidation:
   ```typescript
   const permissionCache = new Map<string, boolean>()
   const cacheKey = `${userId}:${teamId}:${permission}`
   ```

3. **Type Safety Enhancement**:
   - The permission constants could use TypeScript const assertions
   ```typescript
   export const PERMISSIONS = {
     PROJECT_CREATE: 'project:create',
     // ... other permissions
   } as const
   
   type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS]
   ```

4. **Error Handling Refinement**:
   - Add specific error codes for different permission failures
   ```typescript
   enum PermissionError {
     INSUFFICIENT_ROLE = 'PERM_001',
     TEAM_NOT_FOUND = 'PERM_002',
     ROLE_NOT_ASSIGNED = 'PERM_003'
   }
   ```

### Security Analysis

**Positive Security Measures:**
- RLS policies properly check team membership
- Role hierarchy is well-defined
- No client-side permission bypass possible

**Security Recommendations:**
1. **Audit Logging**: Add permission violation logging for security monitoring
2. **Rate Limiting**: Implement rate limiting on permission checks to prevent enumeration
3. **Session Validation**: Ensure role changes invalidate active sessions

### Testing Strategy Review

**Good Coverage Areas:**
- Each role's capabilities tested individually
- UI state verification per role
- API rejection testing

**Additional Test Scenarios Needed:**
1. **Concurrent Role Changes**: Test behavior when role changes during active session
2. **Permission Inheritance**: Test cascading permissions (e.g., project deletion affecting environments)
3. **Cross-Team Isolation**: Verify users can't access other teams' resources even with high roles
4. **Performance Testing**: Load test RLS policies with large datasets

### Risk Assessment

**Medium Risks:**
1. **RLS Policy Complexity**: Complex nested queries might have edge cases
   - Mitigation: Comprehensive policy testing with various data scenarios
2. **Cache Invalidation**: Stale permissions could allow unauthorized access
   - Mitigation: Short TTL and event-based invalidation

**Low Risks:**
1. **UI-only restrictions**: Already mitigated by backend enforcement

### Implementation Recommendations

1. **Phase the Implementation**:
   - Phase 1: Implement core permission service and RLS policies
   - Phase 2: Add UI components and permission hook
   - Phase 3: Optimize with caching and monitoring

2. **Add Permission Middleware**:
   ```typescript
   export async function requirePermission(
     permission: Permission,
     handler: NextApiHandler
   ): NextApiHandler {
     return async (req, res) => {
       const hasAccess = await checkPermission(req.user, permission)
       if (!hasAccess) {
         return res.status(403).json({
           error: 'Permission denied',
           code: PermissionError.INSUFFICIENT_ROLE
         })
       }
       return handler(req, res)
     }
   }
   ```

3. **Implement Permission Analytics**:
   - Track permission check patterns
   - Identify frequently denied actions
   - Monitor for potential privilege escalation attempts

### Acceptance Criteria Validation
✅ AC1: Frontend and backend enforcement specified
✅ AC2: Member role restrictions clearly defined
✅ AC3: Manager role capabilities outlined
✅ AC4: Admin full access documented

### Final Recommendations
1. Add performance benchmarks for RLS policies
2. Include permission migration strategy for existing data
3. Document permission debugging procedures
4. Create permission matrix visualization for user documentation

**Quality Score: 8.5/10**
- Well-architected permission system
- Comprehensive security approach
- Minor improvements needed in caching and monitoring