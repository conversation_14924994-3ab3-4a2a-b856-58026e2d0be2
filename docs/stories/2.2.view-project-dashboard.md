# Story 2.2: View Project Dashboard

## Status
Ready for Review

## Story
**As an** authenticated user,
**I want** to see a list of all my projects on the dashboard,
**so I** can easily select one to work on.

## Acceptance Criteria
1. The dashboard page (/dashboard) displays a list of all projects the logged-in user has access to.
2. Each project in the list displays its name and is a clickable link.
3. If the user has no projects, a clear message and the "Create New Project" button are displayed.
4. Clicking on a project in the list navigates the user to that project's specific page (e.g., /project/{projectId}).

## Tasks / Subtasks
- [x] Enhance dashboard page layout (AC: 1, 3)
  - [x] Update `app/(main)/dashboard/page.tsx`
  - [x] Add projects section with loading state
  - [x] Implement responsive grid layout
  - [x] Add search/filter capability (optional enhancement)
- [x] Create project fetching logic (AC: 1)
  - [x] Implement `getProjects()` in project service
  - [x] Add React Query or SWR for data fetching
  - [x] Handle loading and error states
  - [x] Implement data caching strategy
- [x] Build project list components (AC: 2)
  - [x] Create ProjectCard component
  - [x] Add project name and metadata display
  - [x] Implement hover states and transitions
  - [x] Make entire card clickable
- [x] Implement empty state (AC: 3)
  - [x] Create EmptyState component
  - [x] Add illustration or icon
  - [x] Include helpful messaging
  - [x] Prominent CTA button
- [x] Set up project routing (AC: 4)
  - [x] Create project detail route structure
  - [x] Implement navigation with Next.js Link
  - [x] Pass project ID to detail page
  - [x] Set up URL structure `/projects/[id]`

## Dev Notes
### Dependencies on Previous Stories
This story depends on:
- Story 1.4: Dashboard page exists
- Story 2.1: Projects can be created and exist in database

### Data Fetching Pattern
Use React Query or SWR for optimal data fetching:
```typescript
// lib/projects/hooks.ts
export function useProjects() {
  return useQuery({
    queryKey: ['projects'],
    queryFn: getProjects,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}
```

### Project List Query
Fetch projects with team information:
```sql
SELECT 
  p.*,
  t.name as team_name
FROM projects p
JOIN teams t ON p.team_id = t.id
WHERE p.team_id IN (
  SELECT team_id FROM team_members 
  WHERE user_id = auth.uid()
)
ORDER BY p.created_at DESC;
```

### Dashboard Grid Layout
Responsive grid using Tailwind:
- Mobile: 1 column
- Tablet: 2 columns  
- Desktop: 3-4 columns
```css
grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6
```

### ProjectCard Component Design
From frontend-specs.md - Neobrutalist style:
```typescript
// components/molecules/ProjectCard.tsx
interface ProjectCardProps {
  project: Project
  onClick: () => void
}

// Visual design:
// - White background: bg-white
// - Black border: border-2 border-black
// - Hard shadow: shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]
// - Hover: Move shadow, change border to accent color
// - Transition: transition-all duration-200
```

### Empty State Component
When no projects exist:
```
┌─────────────────────────────┐
│                             │
│      [Icon/Illustration]    │
│                             │
│    No projects yet          │
│                             │
│ Create your first project   │
│    to get started          │
│                             │
│  [Create New Project BTN]   │
│                             │
└─────────────────────────────┘
```

### Loading States
Show skeleton cards while loading:
- Same layout as real cards
- Animated pulse effect
- Show 4-6 skeleton cards
- Smooth transition when data loads

### Error Handling
- Network errors: Show retry button
- Permission errors: Show appropriate message
- Empty results: Show empty state (not error)

### Navigation Pattern
Use Next.js Link for client-side navigation:
```typescript
<Link href={`/projects/${project.id}`}>
  <ProjectCard project={project} />
</Link>
```

### Performance Considerations
- Implement virtual scrolling if >50 projects
- Use optimistic updates when creating projects
- Prefetch project details on hover
- Cache project list data

### Search/Filter Enhancement
Optional enhancement for better UX:
- Search by project name
- Filter by team (future)
- Sort by name/date
- Debounced search input

### Testing
**Testing Requirements:**
- Test project list displays correctly
- Test empty state when no projects
- Test loading states
- Test error handling and retry
- Test navigation to project detail
- Test responsive grid layout
- Test data refetch on focus
- E2E test: Create project → See in list → Navigate

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-19 | 1.0 | Initial story creation | Bob (Scrum Master) |
| 2025-07-20 | 1.1 | Completed implementation | James (Developer) |

## Dev Agent Record
### Agent Model Used
Claude 3.5 Sonnet (claude-3-5-sonnet-20241022)

### Debug Log References
[To be populated by Dev Agent]

### Completion Notes List
- Most functionality was already implemented in Story 2.1
- Created project detail page with route structure `/projects/[id]`
- Enhanced project API to include team information in queries
- Added team name display to project cards and detail page
- Implemented search functionality with real-time filtering
- Search works on both project name and team name
- Added "no results" state for search with clear button
- Project cards were already clickable links from Story 2.1
- Loading states and empty states were already implemented
- Used custom hook pattern for data fetching (useProjects)

### File List
**Created:**
- `/apps/web/src/app/(main)/projects/[id]/page.tsx` - Project detail page

**Modified:**
- `/apps/web/src/lib/projects/types.ts` - Added team relation to Project type
- `/apps/web/src/lib/projects/api.ts` - Enhanced queries to include team data
- `/apps/web/src/components/molecules/ProjectList.tsx` - Added team name display
- `/apps/web/src/app/(main)/dashboard/page.tsx` - Added search functionality

## QA Results
[To be populated by QA Agent]