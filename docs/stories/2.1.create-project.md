# Story 2.1: Create New Project

## Status
Ready for Review

## Story
**As an** authenticated user,
**I want** to create a new project,
**so I** can organize my environment variables.

## Acceptance Criteria
1. A "Create New Project" button is visible on the main dashboard.
2. Clicking the button opens a form or modal asking for a 'Project Name'.
3. Submitting the form creates a new project record in the database, associated with my user/team.
4. After creation, the user is returned to the dashboard where the new project is now listed.

## Tasks / Subtasks
- [x] Set up database schema and RLS (AC: 3)
  - [x] Create teams, projects tables via Supabase migrations
  - [x] Implement RLS policies for team-based access
  - [x] Create initial team for user on signup
  - [x] Set up foreign key relationships
- [x] Create project creation UI (AC: 1, 2)
  - [x] Add "Create New Project" button to dashboard
  - [x] Create modal/dialog component for project form
  - [x] Implement form with project name input
  - [x] Add form validation (required, max length)
- [x] Implement project creation logic (AC: 3)
  - [x] Create project service/hooks in `lib/projects/`
  - [x] Implement create project API call
  - [x] Handle team association automatically
  - [x] Add optimistic updates for better UX
- [x] Update dashboard to show projects (AC: 4)
  - [x] Create project list component
  - [x] Fetch user's projects on dashboard load
  - [x] Display projects in grid/list layout
  - [x] Show empty state when no projects
- [x] Handle errors and edge cases
  - [x] Duplicate project name handling
  - [x] Network error handling
  - [x] Show success toast after creation
  - [x] Prevent multiple submissions

## Dev Notes
### Dependencies on Previous Stories
This story depends on:
- Epic 1 completion: Auth system and protected dashboard
- User must be authenticated and have access to dashboard

### Database Schema
From technical-architecture.md, create these tables:
```sql
-- Teams table (create default team for each user)
CREATE TABLE teams (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Team members junction table
CREATE TYPE team_role AS ENUM ('admin', 'manager', 'member');
CREATE TABLE team_members (
  team_id UUID NOT NULL REFERENCES teams(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  role team_role NOT NULL DEFAULT 'member',
  PRIMARY KEY (team_id, user_id)
);

-- Projects table
CREATE TABLE projects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  team_id UUID NOT NULL REFERENCES teams(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  UNIQUE(team_id, name)
);
```

### RLS Policies
Key RLS policies needed:
```sql
-- Users can view projects belonging to their teams
CREATE POLICY "Users can view team projects" ON projects
  FOR SELECT USING (
    team_id IN (
      SELECT team_id FROM team_members 
      WHERE user_id = auth.uid()
    )
  );

-- Only team admins can create projects
CREATE POLICY "Team admins can create projects" ON projects
  FOR INSERT WITH CHECK (
    team_id IN (
      SELECT team_id FROM team_members 
      WHERE user_id = auth.uid() 
      AND role = 'admin'
    )
  );
```

### Default Team Creation
When a user signs up (enhancement to Story 1.2):
1. Create a default team named "{user.email}'s Team"
2. Add the user as team admin
3. This happens via Supabase trigger or in signup logic

### Project Service Structure
```typescript
// lib/projects/types.ts
interface Project {
  id: string
  team_id: string
  name: string
  created_at: string
}

// lib/projects/api.ts
export async function createProject(name: string): Promise<Project>
export async function getProjects(): Promise<Project[]>
```

### UI Components
**Create Project Modal**:
- Use dialog/modal pattern
- Single input field for project name
- Cancel and Create buttons
- Loading state during submission
- Auto-focus on input when opened

**Project Card Design** (Neobrutalist):
- White background with thick border: `border-2 border-black`
- Hard shadow: `shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]`
- Project name in bold
- Created date in smaller text
- Hover state with accent color border

### Dashboard Layout
```
Dashboard
├── Header: "My Projects"
├── Create New Project Button (prominent)
└── Projects Grid
    ├── Project Card 1
    ├── Project Card 2
    └── Empty State (if no projects)
```

### Empty State Design
When user has no projects:
- Large illustration or icon
- "No projects yet" heading
- "Create your first project to get started" text
- Prominent "Create New Project" button

### Form Validation
- Project name: Required, 3-50 characters
- Alphanumeric, spaces, hyphens, underscores allowed
- Real-time validation feedback
- Trim whitespace before submission

### Error Handling
- `Duplicate name`: "A project with this name already exists"
- `Network error`: "Failed to create project. Please try again"
- `Permission denied`: "You don't have permission to create projects"

### Testing
**Testing Requirements:**
- Test project creation flow end-to-end
- Test form validation rules
- Test duplicate name handling
- Test RLS policies work correctly
- Test empty state display
- Test project list updates after creation
- Test error scenarios
- Test modal open/close behavior

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-19 | 1.0 | Initial story creation | Bob (Scrum Master) |
| 2025-07-20 | 1.1 | Completed implementation | James (Developer) |

## Dev Agent Record
### Agent Model Used
Claude 3.5 Sonnet (claude-3-5-sonnet-20241022)

### Debug Log References
[To be populated by Dev Agent]

### Completion Notes List
- Created comprehensive database schema with teams, team_members, and projects tables
- Implemented RLS policies for team-based access control
- Added trigger to automatically create default team on user signup
- Created reusable Modal component with Neobrutalist styling
- Implemented project service layer with proper error handling
- Created custom hook (useProjects) for state management
- Added form validation for project names (3-50 chars, alphanumeric + special chars)
- Implemented optimistic updates for better UX
- Created empty state design when no projects exist
- Added loading skeletons during data fetch
- Properly handles duplicate project names with user-friendly error
- Dashboard now fully integrated with project management
- Updated README with database migration instructions

### File List
**Created:**
- `/apps/web/supabase/migrations/20250120_create_teams_and_projects.sql` - Database schema and RLS
- `/apps/web/src/lib/projects/types.ts` - TypeScript types for projects
- `/apps/web/src/lib/projects/api.ts` - Project API functions
- `/apps/web/src/lib/projects/hooks.ts` - React hooks for project management
- `/apps/web/src/components/molecules/Modal.tsx` - Reusable modal component
- `/apps/web/src/components/molecules/CreateProjectModal.tsx` - Project creation modal
- `/apps/web/src/components/molecules/ProjectList.tsx` - Project list with empty state

**Modified:**
- `/apps/web/src/app/(main)/dashboard/page.tsx` - Integrated project management
- `/README.md` - Added database migration instructions

## QA Results
[To be populated by QA Agent]