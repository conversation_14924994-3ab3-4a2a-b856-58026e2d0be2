# Story 3.2: Create Secure API Endpoint for Secrets

## Status
Ready for Review

## Story
**As a** developer,
**I need** a secure backend API endpoint that the npm package can call to fetch environment variables.

## Acceptance Criteria
1. A new private API endpoint (e.g., /api/v1/secrets) is created.
2. The endpoint requires a valid API key to be passed in the request headers for authentication.
3. The endpoint validates that the provided API key has permission to access the requested project and environment.
4. If authorized, the endpoint returns the secret key-value pairs for the requested environment in a JSON format.
5. If unauthorized or if the project/environment doesn't exist, it returns an appropriate HTTP error code and message.

## Tasks / Subtasks
- [x] Create Edge Function structure (AC: 1)
  - [x] Set up Next.js API route for /api/v1/secrets
  - [x] Configure CORS for cross-origin requests
  - [x] Set up proper TypeScript types
  - [x] Implement request/response handling
- [x] Implement API key authentication (AC: 2)
  - [x] Extract API key from Authorization header
  - [x] Validate key format
  - [x] Look up and verify key in database
  - [x] Check key is not revoked
  - [x] Update last_used_at timestamp
- [x] Validate access permissions (AC: 3)
  - [x] Extract project and environment from request
  - [x] Verify user has access to requested project
  - [x] Check project/environment exists
  - [x] Validate user's team membership
- [x] Implement secret retrieval (AC: 4)
  - [x] Fetch encrypted secrets from database
  - [x] Decrypt secrets server-side
  - [x] Format response as JSON
  - [x] Handle decryption errors gracefully
- [x] Add error handling (AC: 5)
  - [x] Return 401 for invalid/missing API key
  - [x] Return 403 for permission denied
  - [x] Return 404 for missing project/environment
  - [x] Return 500 for server errors
  - [x] Include helpful error messages

## Dev Notes
### Dependencies on Previous Stories
This story depends on:
- Story 3.1: API keys exist in database
- Story 2.4: Secrets are stored encrypted
- Epic 2: Projects and environments structure

### Supabase Edge Function
From technical-architecture.md:
- Edge Functions run on Deno runtime
- Located at `/functions/v1/get-secrets`
- Uses TypeScript for type safety

### Edge Function Implementation
```typescript
// supabase/functions/get-secrets/index.ts
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import * as bcrypt from 'https://deno.land/x/bcrypt/mod.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Extract API key
    const apiKey = req.headers.get('Authorization')?.replace('Bearer ', '')
    if (!apiKey) {
      return new Response(
        JSON.stringify({ error: 'Missing API key' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Parse request body
    const { projectName, environmentName } = await req.json()
    
    // Validate inputs
    if (!projectName || !environmentName) {
      return new Response(
        JSON.stringify({ error: 'Missing projectName or environmentName' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Validate API key and get user
    const user = await validateApiKey(supabase, apiKey)
    if (!user) {
      return new Response(
        JSON.stringify({ error: 'Invalid API key' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get secrets
    const secrets = await getSecretsForUser(
      supabase, 
      user.id, 
      projectName, 
      environmentName
    )

    if (!secrets) {
      return new Response(
        JSON.stringify({ error: 'Project or environment not found' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    return new Response(
      JSON.stringify({ secrets }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
```

### API Key Validation
```typescript
async function validateApiKey(supabase: any, apiKey: string) {
  // Get all active keys (we need to check each hash)
  const { data: keys, error } = await supabase
    .from('api_keys')
    .select('id, user_id, key_hash')
    .is('revoked_at', null)

  if (error || !keys) return null

  // Check each key hash
  for (const key of keys) {
    const isValid = await bcrypt.compare(apiKey, key.key_hash)
    if (isValid) {
      // Update last used
      await supabase
        .from('api_keys')
        .update({ last_used_at: new Date().toISOString() })
        .eq('id', key.id)
      
      return { id: key.user_id }
    }
  }

  return null
}
```

### Secret Retrieval Logic
```typescript
async function getSecretsForUser(
  supabase: any,
  userId: string,
  projectName: string,
  environmentName: string
) {
  // Get project with team access check
  const { data: project } = await supabase
    .from('projects')
    .select(`
      id,
      team_id,
      teams!inner (
        team_members!inner (
          user_id
        )
      )
    `)
    .eq('name', projectName)
    .eq('teams.team_members.user_id', userId)
    .single()

  if (!project) return null

  // Get environment
  const { data: environment } = await supabase
    .from('environments')
    .select('id')
    .eq('project_id', project.id)
    .eq('name', environmentName)
    .single()

  if (!environment) return null

  // Get secrets
  const { data: secrets } = await supabase
    .from('secrets')
    .select('key, encrypted_value, iv')
    .eq('environment_id', environment.id)

  if (!secrets) return {}

  // Decrypt secrets
  const decrypted: Record<string, string> = {}
  for (const secret of secrets) {
    try {
      decrypted[secret.key] = await decrypt(
        secret.encrypted_value,
        secret.iv
      )
    } catch (error) {
      console.error(`Failed to decrypt ${secret.key}:`, error)
      // Continue with other secrets
    }
  }

  return decrypted
}
```

### Request/Response Format
**Request:**
```bash
POST /functions/v1/get-secrets
Authorization: Bearer ezenv_xxxxxxxxxxxxx
Content-Type: application/json

{
  "projectName": "my-app",
  "environmentName": "production"
}
```

**Success Response:**
```json
{
  "secrets": {
    "DATABASE_URL": "postgresql://...",
    "API_KEY": "sk_test_...",
    "JWT_SECRET": "..."
  }
}
```

### Error Response Format
```json
{
  "error": "Human-readable error message",
  "code": "ERROR_CODE" // Optional error code
}
```

### Rate Limiting
Implement rate limiting to prevent abuse:
- Per API key: 100 requests per minute
- Per IP: 1000 requests per hour
- Return 429 Too Many Requests when exceeded

### Caching Strategy
For performance:
- Cache decrypted secrets for 5 minutes
- Cache by environment_id + user_id
- Invalidate on secret updates
- Use Supabase Edge Function built-in cache

### Security Headers
Add security headers to all responses:
```typescript
const securityHeaders = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
}
```

### Monitoring and Logging
Log important events:
- API key usage (user, project, environment)
- Failed authentication attempts
- Permission denied attempts
- Decryption failures
- Response times

### Testing
**Testing Requirements:**
- Test valid API key authenticates successfully
- Test invalid API key returns 401
- Test revoked key returns 401
- Test missing project returns 404
- Test permission checks work correctly
- Test secrets are decrypted properly
- Test CORS headers are present
- Test rate limiting works
- Load test with concurrent requests
- E2E: Generate key → Call API → Get secrets

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-19 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record
### Agent Model Used
claude-opus-4-20250514

### Debug Log References
N/A

### Completion Notes List
- Implemented secure API endpoint as Next.js route instead of Supabase Edge Function
- Created comprehensive authentication using API keys with bcrypt validation
- Implemented permission checking through team membership verification
- Added server-side decryption of secrets before returning to client
- Included CORS headers for cross-origin requests from npm packages
- Added security headers for enhanced protection
- Created comprehensive test suite covering all error cases
- Provided integration test examples for API usage documentation
- Fixed RLS issue by using service role client for API key validation
- Requires SUPABASE_SERVICE_ROLE_KEY environment variable to be set

### File List
- Created: src/app/api/v1/secrets/route.ts
- Created: src/app/api/v1/secrets/__tests__/route.test.ts
- Created: src/lib/api-keys/__tests__/integration.test.ts
- Created: src/lib/supabase/service.ts

## QA Results
[To be populated by QA Agent]