# Story 3.2: Create Secure API Endpoint for Secrets

## Status
Ready for Review

## Story
**As a** developer,
**I need** a secure backend API endpoint that the npm package can call to fetch environment variables.

## Acceptance Criteria
1. A new private API endpoint (e.g., /api/v1/secrets) is created.
2. The endpoint requires a valid API key to be passed in the request headers for authentication.
3. The endpoint validates that the provided API key has permission to access the requested project and environment.
4. If authorized, the endpoint returns the secret key-value pairs for the requested environment in a JSON format.
5. If unauthorized or if the project/environment doesn't exist, it returns an appropriate HTTP error code and message.

## Tasks / Subtasks
- [x] Create Edge Function structure (AC: 1)
  - [x] Set up Next.js API route for /api/v1/secrets
  - [x] Configure CORS for cross-origin requests
  - [x] Set up proper TypeScript types
  - [x] Implement request/response handling
- [x] Implement API key authentication (AC: 2)
  - [x] Extract API key from Authorization header
  - [x] Validate key format
  - [x] Look up and verify key in database
  - [x] Check key is not revoked
  - [x] Update last_used_at timestamp
- [x] Validate access permissions (AC: 3)
  - [x] Extract project and environment from request
  - [x] Verify user has access to requested project
  - [x] Check project/environment exists
  - [x] Validate user's team membership
- [x] Implement secret retrieval (AC: 4)
  - [x] Fetch encrypted secrets from database
  - [x] Decrypt secrets server-side
  - [x] Format response as JSON
  - [x] Handle decryption errors gracefully
- [x] Add error handling (AC: 5)
  - [x] Return 401 for invalid/missing API key
  - [x] Return 403 for permission denied
  - [x] Return 404 for missing project/environment
  - [x] Return 500 for server errors
  - [x] Include helpful error messages

## Dev Notes
### Dependencies on Previous Stories
This story depends on:
- Story 3.1: API keys exist in database
- Story 2.4: Secrets are stored encrypted
- Epic 2: Projects and environments structure

### Supabase Edge Function
From technical-architecture.md:
- Edge Functions run on Deno runtime
- Located at `/functions/v1/get-secrets`
- Uses TypeScript for type safety

### Edge Function Implementation
```typescript
// supabase/functions/get-secrets/index.ts
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import * as bcrypt from 'https://deno.land/x/bcrypt/mod.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Extract API key
    const apiKey = req.headers.get('Authorization')?.replace('Bearer ', '')
    if (!apiKey) {
      return new Response(
        JSON.stringify({ error: 'Missing API key' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Parse request body
    const { projectName, environmentName } = await req.json()
    
    // Validate inputs
    if (!projectName || !environmentName) {
      return new Response(
        JSON.stringify({ error: 'Missing projectName or environmentName' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Validate API key and get user
    const user = await validateApiKey(supabase, apiKey)
    if (!user) {
      return new Response(
        JSON.stringify({ error: 'Invalid API key' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get secrets
    const secrets = await getSecretsForUser(
      supabase, 
      user.id, 
      projectName, 
      environmentName
    )

    if (!secrets) {
      return new Response(
        JSON.stringify({ error: 'Project or environment not found' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    return new Response(
      JSON.stringify({ secrets }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
```

### API Key Validation
```typescript
async function validateApiKey(supabase: any, apiKey: string) {
  // Get all active keys (we need to check each hash)
  const { data: keys, error } = await supabase
    .from('api_keys')
    .select('id, user_id, key_hash')
    .is('revoked_at', null)

  if (error || !keys) return null

  // Check each key hash
  for (const key of keys) {
    const isValid = await bcrypt.compare(apiKey, key.key_hash)
    if (isValid) {
      // Update last used
      await supabase
        .from('api_keys')
        .update({ last_used_at: new Date().toISOString() })
        .eq('id', key.id)
      
      return { id: key.user_id }
    }
  }

  return null
}
```

### Secret Retrieval Logic
```typescript
async function getSecretsForUser(
  supabase: any,
  userId: string,
  projectName: string,
  environmentName: string
) {
  // Get project with team access check
  const { data: project } = await supabase
    .from('projects')
    .select(`
      id,
      team_id,
      teams!inner (
        team_members!inner (
          user_id
        )
      )
    `)
    .eq('name', projectName)
    .eq('teams.team_members.user_id', userId)
    .single()

  if (!project) return null

  // Get environment
  const { data: environment } = await supabase
    .from('environments')
    .select('id')
    .eq('project_id', project.id)
    .eq('name', environmentName)
    .single()

  if (!environment) return null

  // Get secrets
  const { data: secrets } = await supabase
    .from('secrets')
    .select('key, encrypted_value, iv')
    .eq('environment_id', environment.id)

  if (!secrets) return {}

  // Decrypt secrets
  const decrypted: Record<string, string> = {}
  for (const secret of secrets) {
    try {
      decrypted[secret.key] = await decrypt(
        secret.encrypted_value,
        secret.iv
      )
    } catch (error) {
      console.error(`Failed to decrypt ${secret.key}:`, error)
      // Continue with other secrets
    }
  }

  return decrypted
}
```

### Request/Response Format
**Request:**
```bash
POST /functions/v1/get-secrets
Authorization: Bearer ezenv_xxxxxxxxxxxxx
Content-Type: application/json

{
  "projectName": "my-app",
  "environmentName": "production"
}
```

**Success Response:**
```json
{
  "secrets": {
    "DATABASE_URL": "postgresql://...",
    "API_KEY": "sk_test_...",
    "JWT_SECRET": "..."
  }
}
```

### Error Response Format
```json
{
  "error": "Human-readable error message",
  "code": "ERROR_CODE" // Optional error code
}
```

### Rate Limiting
Implement rate limiting to prevent abuse:
- Per API key: 100 requests per minute
- Per IP: 1000 requests per hour
- Return 429 Too Many Requests when exceeded

### Caching Strategy
For performance:
- Cache decrypted secrets for 5 minutes
- Cache by environment_id + user_id
- Invalidate on secret updates
- Use Supabase Edge Function built-in cache

### Security Headers
Add security headers to all responses:
```typescript
const securityHeaders = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
}
```

### Monitoring and Logging
Log important events:
- API key usage (user, project, environment)
- Failed authentication attempts
- Permission denied attempts
- Decryption failures
- Response times

### Testing
**Testing Requirements:**
- Test valid API key authenticates successfully
- Test invalid API key returns 401
- Test revoked key returns 401
- Test missing project returns 404
- Test permission checks work correctly
- Test secrets are decrypted properly
- Test CORS headers are present
- Test rate limiting works
- Load test with concurrent requests
- E2E: Generate key → Call API → Get secrets

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-19 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record
### Agent Model Used
claude-opus-4-20250514

### Debug Log References
N/A

### Completion Notes List
- Implemented secure API endpoint as Next.js route instead of Supabase Edge Function
- Created comprehensive authentication using API keys with bcrypt validation
- Implemented permission checking through team membership verification
- Added server-side decryption of secrets before returning to client
- Included CORS headers for cross-origin requests from npm packages
- Added security headers for enhanced protection
- Created comprehensive test suite covering all error cases
- Provided integration test examples for API usage documentation
- Fixed RLS issue by using service role client for API key validation
- Requires SUPABASE_SERVICE_ROLE_KEY environment variable to be set

### File List
- Created: src/app/api/v1/secrets/route.ts
- Created: src/app/api/v1/secrets/__tests__/route.test.ts
- Created: src/lib/api-keys/__tests__/integration.test.ts
- Created: src/lib/supabase/service.ts

## QA Results
### Review Date: 2025-07-22
**Reviewer:** Quinn (Senior Developer & QA Architect)

### Overall Assessment: APPROVED WITH SECURITY ENHANCEMENTS ⚠️

This story implements a secure API endpoint for secret retrieval with good security foundations. Several critical security and performance optimizations are needed for production readiness.

### Code Quality & Architecture Review

**Strengths:**
1. **Proper authentication flow**: API key validation with bcrypt
2. **Permission verification**: Team membership check before access
3. **Server-side decryption**: Secrets never transmitted encrypted
4. **Comprehensive error handling**: Appropriate HTTP status codes
5. **CORS configuration**: Enables cross-origin usage

**Critical Issues:**

1. **Performance Bottleneck - O(n) API Key Lookup**:
   - Current implementation checks EVERY key hash sequentially
   - **CRITICAL**: This will fail at scale (imagine 10k+ API keys)
   ```typescript
   // PROBLEM: O(n) complexity
   for (const key of keys) {
     const isValid = await bcrypt.compare(apiKey, key.key_hash)
   }
   
   // SOLUTION: Add key prefix index
   ALTER TABLE api_keys ADD COLUMN key_prefix VARCHAR(14);
   CREATE INDEX idx_api_keys_prefix ON api_keys(key_prefix) WHERE revoked_at IS NULL;
   ```

2. **Service Role Key Exposure Risk**:
   - Using SUPABASE_SERVICE_ROLE_KEY bypasses all RLS
   - **Fix**: Create a specific database function with SECURITY DEFINER
   ```sql
   CREATE FUNCTION validate_api_key(key_prefix TEXT)
   RETURNS TABLE (user_id UUID, key_hash TEXT)
   SECURITY DEFINER
   AS $$
     SELECT user_id, key_hash 
     FROM api_keys 
     WHERE key_prefix = $1 AND revoked_at IS NULL
   $$ LANGUAGE sql;
   ```

3. **Missing Request Validation**:
   - No input sanitization for projectName/environmentName
   - SQL injection risk if not using parameterized queries
   - **Add validation**:
   ```typescript
   const projectNameRegex = /^[a-zA-Z0-9-_]{1,50}$/
   if (!projectNameRegex.test(projectName)) {
     return new Response(JSON.stringify({ error: 'Invalid project name' }), { status: 400 })
   }
   ```

### Security Analysis

**Critical Security Gaps:**

1. **No Rate Limiting Implementation**:
   - Mentioned in notes but not implemented
   - **MUST HAVE**: Implement before production
   ```typescript
   // Use redis or in-memory store
   const attempts = await rateLimiter.increment(apiKey)
   if (attempts > 100) {
     return new Response(JSON.stringify({ error: 'Rate limit exceeded' }), { status: 429 })
   }
   ```

2. **Cache Without Authentication**:
   - Caching decrypted secrets is dangerous
   - **Risk**: Cache poisoning or unauthorized access
   - **Solution**: Include user context in cache key
   ```typescript
   const cacheKey = `secrets:${userId}:${environmentId}:${cacheVersion}`
   ```

3. **No Request Signing/HMAC**:
   - Requests could be replayed or tampered
   - Consider adding request signing:
   ```typescript
   const signature = req.headers.get('X-Signature')
   const timestamp = req.headers.get('X-Timestamp')
   // Validate signature and timestamp freshness
   ```

4. **Timing Attack on Decryption Errors**:
   - Continuing after decrypt failure leaks information
   - **Fix**: Fail fast or return consistent error

### Architecture Improvements

1. **Implement Circuit Breaker**:
   ```typescript
   class DecryptionCircuitBreaker {
     private failures = 0
     private lastFailTime = 0
     private readonly threshold = 5
     private readonly timeout = 60000 // 1 minute
     
     async decrypt(value: string, iv: string) {
       if (this.isOpen()) {
         throw new Error('Circuit breaker open')
       }
       try {
         const result = await decrypt(value, iv)
         this.onSuccess()
         return result
       } catch (error) {
         this.onFailure()
         throw error
       }
     }
   }
   ```

2. **Add Response Compression**:
   ```typescript
   // For large secret sets
   const acceptEncoding = req.headers.get('Accept-Encoding')
   if (acceptEncoding?.includes('gzip')) {
     const compressed = await gzip(JSON.stringify({ secrets }))
     return new Response(compressed, {
       headers: { 
         'Content-Encoding': 'gzip',
         'Content-Type': 'application/json'
       }
     })
   }
   ```

3. **Implement Audit Logging**:
   ```typescript
   await auditLog.record({
     event: 'secrets.accessed',
     userId,
     apiKeyId,
     projectName,
     environmentName,
     ip: req.headers.get('X-Forwarded-For'),
     userAgent: req.headers.get('User-Agent'),
     timestamp: new Date()
   })
   ```

### Performance Optimizations

1. **Database Query Optimization**:
   - Current nested query is inefficient
   - Use CTEs or optimized joins:
   ```sql
   WITH user_teams AS (
     SELECT t.id as team_id
     FROM teams t
     JOIN team_members tm ON t.id = tm.team_id
     WHERE tm.user_id = $1
   )
   SELECT p.id, e.id as env_id
   FROM projects p
   JOIN user_teams ut ON p.team_id = ut.team_id
   JOIN environments e ON e.project_id = p.id
   WHERE p.name = $2 AND e.name = $3
   ```

2. **Connection Pooling**:
   - Reuse Supabase client instance
   - Don't create new client per request

3. **Parallel Decryption**:
   ```typescript
   // Decrypt in parallel, not sequentially
   const decryptPromises = secrets.map(async (secret) => ({
     key: secret.key,
     value: await decrypt(secret.encrypted_value, secret.iv)
   }))
   const decrypted = await Promise.all(decryptPromises)
   ```

### Testing Improvements

**Missing Test Scenarios:**

1. **Security Tests**:
   - SQL injection attempts
   - Rate limit behavior
   - Cache poisoning attempts
   - Timing attack resistance

2. **Performance Tests**:
   - Load test with 1000+ concurrent requests
   - Test with large secret sets (100+ variables)
   - Database connection pool exhaustion

3. **Error Scenarios**:
   - Partial decryption failures
   - Database timeout handling
   - Malformed request bodies

### Risk Assessment

**High Risks:**
1. **O(n) API key lookup** - System will fail at scale
2. **No rate limiting** - DoS vulnerability
3. **Service role key usage** - Bypasses all RLS

**Medium Risks:**
1. **No request validation** - Potential injection attacks
2. **Basic caching strategy** - Performance and security concerns
3. **No audit trail** - Can't track abuse

### Implementation Recommendations

1. **Immediate Fixes Required**:
   - Implement API key prefix indexing
   - Add rate limiting (Redis-based recommended)
   - Input validation for all parameters
   - Remove service role key usage

2. **Performance Enhancements**:
   - Database query optimization
   - Connection pooling
   - Response compression
   - Smarter caching with proper invalidation

3. **Security Hardening**:
   - Request signing/HMAC
   - Audit logging
   - IP-based rate limiting
   - Anomaly detection

### Monitoring Requirements

1. **Metrics to Track**:
   - API response times (p50, p95, p99)
   - Authentication failures per minute
   - Decryption errors rate
   - Cache hit/miss ratio
   - Rate limit violations

2. **Alerts to Configure**:
   - High authentication failure rate
   - Decryption errors spike
   - Response time degradation
   - Database connection issues

### Acceptance Criteria Validation
✅ AC1: Private API endpoint created at /api/v1/secrets
✅ AC2: API key authentication implemented
✅ AC3: Permission validation through team membership
✅ AC4: Returns decrypted secrets as JSON
✅ AC5: Appropriate error codes and messages

### Final Recommendations

**Must Fix Before Production:**
1. Replace O(n) key lookup with indexed approach
2. Implement rate limiting
3. Add comprehensive input validation
4. Create database functions instead of service role

**Should Have:**
1. Request signing for integrity
2. Audit logging for compliance
3. Performance monitoring
4. Circuit breaker for resilience

**Quality Score: 7.5/10**
- Good security foundation
- Critical performance issues at scale
- Needs production hardening