# Story 2.3: Manage Environments in a Project

## Status
Ready for Review

## Story
**As a** project admin,
**I want** to create and view different named environments within a project,
**so that** I can manage configurations for development, staging, etc., separately.

## Acceptance Criteria
1. On a project's page, there is a UI for creating a new environment (e.g., a form with a name field like 'development', 'staging').
2. The page displays a list or tabs of all existing environments for that project.
3. Selecting an environment displays the secrets associated with it.

## Tasks / Subtasks
- [x] Create project detail page structure (AC: 1, 2, 3)
  - [x] Create `app/(main)/projects/[id]/page.tsx`
  - [x] Fetch project details and verify access
  - [x] Create layout with environment tabs/list
  - [x] Handle project not found/access denied
- [x] Set up environments database schema (AC: 1)
  - [x] Create environments table via migration
  - [x] Add RLS policies for environment access
  - [x] Set up proper indexes
  - [x] Create default environment on project creation
- [x] Build environment creation UI (AC: 1)
  - [x] Create "Add Environment" button/form
  - [x] Implement environment name input
  - [x] Add validation (unique names, allowed characters)
  - [x] Handle form submission
- [x] Implement environment management (AC: 2, 3)
  - [x] Create environment service/hooks
  - [x] Fetch environments for project
  - [x] Create new environment API
  - [x] Handle environment selection state
- [x] Create environment display components (AC: 2, 3)
  - [x] Build environment tabs/selector
  - [x] Show active environment indicator
  - [x] Create environment content area
  - [x] Add loading states for environment data

## Dev Notes
### Dependencies on Previous Stories
This story depends on:
- Story 2.2: Project navigation and routing
- Story 2.1: Projects exist in database

### Database Schema
From technical-architecture.md:
```sql
-- Environments table
CREATE TABLE environments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  UNIQUE(project_id, name)
);
```

### RLS Policies for Environments
```sql
-- Users can view environments for projects they have access to
CREATE POLICY "View project environments" ON environments
  FOR SELECT USING (
    project_id IN (
      SELECT p.id FROM projects p
      JOIN team_members tm ON p.team_id = tm.team_id
      WHERE tm.user_id = auth.uid()
    )
  );

-- Only admins/managers can create environments
CREATE POLICY "Create environments" ON environments
  FOR INSERT WITH CHECK (
    project_id IN (
      SELECT p.id FROM projects p
      JOIN team_members tm ON p.team_id = tm.team_id
      WHERE tm.user_id = auth.uid()
      AND tm.role IN ('admin', 'manager')
    )
  );
```

### Project Detail Page Structure
```
/projects/[id]
├── Project Header
│   ├── Project Name
│   ├── Team Name
│   └── Actions Menu
├── Environment Tabs
│   ├── Development (active)
│   ├── Staging
│   ├── Production
│   └── [+ Add Environment]
└── Environment Content Area
    └── (Placeholder for Story 2.4)
```

### Environment Naming Conventions
Common environment names to suggest:
- development (dev)
- staging
- production (prod)
- testing
- preview

Validation rules:
- Lowercase alphanumeric and hyphens only
- 2-20 characters
- No spaces or special characters
- Must be unique within project

### UI Component Design
**Environment Tabs** (Neobrutalist):
```typescript
// Inactive tab:
// - White background
// - Black border bottom
// - Regular font weight

// Active tab:
// - Accent color background (yellow)
// - Black border all sides
// - Bold font weight
// - No bottom border (connects to content)
```

**Add Environment Form**:
- Can be inline (tab that becomes input)
- Or modal/dropdown
- Single input field
- Suggestions for common names
- Real-time validation

### Default Environment
When creating a project (enhancement to Story 2.1):
- Automatically create "development" environment
- This gives immediate value to users
- Prevents empty state confusion

### Environment Service
```typescript
// lib/environments/types.ts
interface Environment {
  id: string
  project_id: string
  name: string
  created_at: string
}

// lib/environments/api.ts
export async function getEnvironments(projectId: string): Promise<Environment[]>
export async function createEnvironment(projectId: string, name: string): Promise<Environment>
```

### State Management
Track selected environment:
- URL parameter: `/projects/[id]?env=staging`
- Or local state with persistence
- Default to first environment
- Remember last selected per project

### Loading States
While fetching environments:
- Show skeleton tabs
- Maintain layout stability
- Quick loading expected (<500ms)

### Empty State
If no environments (edge case):
- Show message: "No environments yet"
- Prominent "Create Environment" button
- Should rarely happen with default environment

### Error Handling
- `Duplicate name`: "An environment with this name already exists"
- `Invalid name`: "Environment names can only contain lowercase letters, numbers, and hyphens"
- `Permission denied`: "You don't have permission to create environments"

### Testing
**Testing Requirements:**
- Test environment creation flow
- Test duplicate name validation
- Test tab switching behavior
- Test RLS policies (admin vs member)
- Test default environment creation
- Test project page loads with environments
- Test error scenarios
- E2E: Navigate to project → Create environment → Switch between environments

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-19 | 1.0 | Initial story creation | Bob (Scrum Master) |
| 2025-07-20 | 1.1 | Completed implementation | James (Developer) |

## Dev Agent Record
### Agent Model Used
Claude 3.5 Sonnet (claude-3-5-sonnet-20241022)

### Debug Log References
[To be populated by Dev Agent]

### Completion Notes List
- Created environments table with proper indexes and unique constraint
- Implemented RLS policies for team-based access (admins/managers can create, only admins can delete)
- Added trigger to create default 'development' environment on project creation
- Created environment service layer with validation (lowercase, 2-20 chars, alphanumeric + hyphens)
- Implemented environment tabs with Neobrutalist active state (yellow background)
- Created modal for adding new environments with common suggestions
- Added loading states and empty states for environments
- Environment selection persists in component state
- First environment is auto-selected when available
- Added migration to create default environments for existing projects
- Placeholder added for secrets management (Story 2.4)

### File List
**Created:**
- `/apps/web/supabase/migrations/20250120_002_create_environments.sql` - Database schema and RLS
- `/apps/web/src/lib/environments/types.ts` - TypeScript types for environments
- `/apps/web/src/lib/environments/api.ts` - Environment API functions with validation
- `/apps/web/src/lib/environments/hooks.ts` - React hooks for environment state
- `/apps/web/src/components/molecules/CreateEnvironmentModal.tsx` - Environment creation modal

**Modified:**
- `/apps/web/src/app/(main)/projects/[id]/page.tsx` - Added environment tabs and management
- `/apps/web/src/app/globals.css` - Added yellow background for active tab

## QA Results
[To be populated by QA Agent]