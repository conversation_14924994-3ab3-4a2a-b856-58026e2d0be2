# Story 1.3: User Login

## Status
Ready for Review

## Story
**As a** registered user,
**I want** to be able to log in to the platform with my email and password,
**so that** I can access my projects and settings.

## Acceptance Criteria
1. A login page is created with input fields for 'email' and 'password', and a 'Log In' button.
2. Clicking the 'Log In' button calls the Supabase authentication service to sign the user in.
3. Upon successful login, the user is redirected to a protected dashboard page.
4. The system provides a clear error message for incorrect login credentials.

## Tasks / Subtasks
- [x] Create login page structure (AC: 1)
  - [x] Create `app/(auth)/login/page.tsx`
  - [x] Implement form with email and password fields
  - [x] Add "Sign up" link for new users
  - [x] Apply consistent Neobrutalist design styling
- [x] Implement Supabase authentication (AC: 2)
  - [x] Extend auth helper functions in `lib/supabase/auth.ts`
  - [x] Implement login logic using `supabase.auth.signInWithPassword()`
  - [x] Handle form submission with loading states
  - [x] Store session tokens properly
- [x] Handle success flow (AC: 3)
  - [x] Create protected route middleware
  - [x] Implement redirect to `/dashboard` after successful login
  - [x] Ensure session persistence across page reloads
  - [x] Handle "remember me" functionality if needed
- [x] Implement error handling (AC: 4)
  - [x] Handle invalid credentials error specifically
  - [x] Display user-friendly error messages
  - [x] Clear password field on error
  - [x] Implement rate limiting awareness
- [x] Create auth context/provider
  - [x] Set up auth context for app-wide user state
  - [x] Implement useAuth hook for components
  - [x] Handle session refresh automatically
  - [x] Add auth state persistence

## Dev Notes
### Dependencies on Previous Stories
This story depends on:
- Story 1.1: Supabase client and Next.js app structure
- Story 1.2: Reusable form components and auth helper structure

### Supabase Auth Implementation
From technical-architecture.md:
- Sessions are managed by Supabase Auth
- JWTs are used for API authentication (short-lived tokens)

Key Supabase login method:
```typescript
const { data, error } = await supabase.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'password'
})

// Session is automatically stored in cookies by Supabase
```

### Session Management
Supabase handles session management automatically:
- Sessions stored in httpOnly cookies
- Automatic token refresh before expiry
- Session persistence across browser restarts

Create auth helpers:
```typescript
// lib/supabase/auth.ts
export async function getSession() {
  const { data: { session } } = await supabase.auth.getSession()
  return session
}

export async function getUser() {
  const { data: { user } } = await supabase.auth.getUser()
  return user
}
```

### Page Route Structure
The login page should be located at:
- File: `apps/web/src/app/(auth)/login/page.tsx`
- Route: `/login`
- This should be the default redirect for unauthenticated users

### Middleware for Protected Routes
Create middleware to protect routes:
- File: `apps/web/src/middleware.ts`
- Protect all routes under `/(main)/`
- Redirect unauthenticated users to `/login`
- Allow access to `/(auth)/` routes for everyone

### Form Design Consistency
Maintain consistency with signup page:
- Same input field styling with thick borders
- Same button styles and hover effects
- Consistent error message display
- Link to signup page styled as underlined text

### Error Handling Specifics
Common login errors to handle:
- `Invalid login credentials`: "Invalid email or password"
- `Email not confirmed`: "Please verify your email before logging in"
- `Too many requests`: "Too many login attempts. Please try again later"
- Network errors: "Unable to connect. Please check your internet connection"

### Auth Context Structure
```typescript
// contexts/AuthContext.tsx
interface AuthContextType {
  user: User | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => Promise<void>
}
```

### Accessibility Requirements
- Form must announce errors to screen readers
- Tab order: email → password → login button → signup link
- Clear focus indicators on all interactive elements
- Login button disabled state must be perceivable

### Testing
**Testing Requirements:**
- Unit tests for auth context and hooks
- Integration test for Supabase login flow
- Test session persistence after page reload
- Test redirect to dashboard after login
- Test redirect to login when accessing protected routes
- E2E test for complete login journey
- Test all error scenarios

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-19 | 1.0 | Initial story creation | Bob (Scrum Master) |
| 2025-07-20 | 1.1 | Completed implementation | James (Developer) |

## Dev Agent Record
### Agent Model Used
Claude 3.5 Sonnet (claude-3-5-sonnet-20241022)

### Debug Log References
[To be populated by Dev Agent]

### Completion Notes List
- Successfully created login page with form validation
- Implemented auth context for app-wide user state management
- Created middleware for protecting routes
- Added session persistence and automatic refresh
- Error messages are mapped to user-friendly text
- Password field clears on error for security
- Middleware redirects authenticated users away from auth pages
- Created placeholder dashboard page for successful login

### File List
**Created:**
- `/apps/web/src/app/(auth)/login/page.tsx` - Login page component
- `/apps/web/src/contexts/AuthContext.tsx` - Auth context provider
- `/apps/web/src/middleware.ts` - Route protection middleware
- `/apps/web/src/app/dashboard/page.tsx` - Placeholder dashboard page

**Modified:**
- `/apps/web/src/lib/supabase/auth.ts` - Added session management helpers
- `/apps/web/src/app/globals.css` - Added additional utility classes

## QA Results
[To be populated by QA Agent]