# Story 2.4: Manage Secrets Key-Value Pairs

## Status
Ready for Review

## Story
**As a** project admin/manager,
**I want** to add, view, edit, and delete individual secret key-value pairs within an environment,
**so I** can manage the configuration data.

## Acceptance Criteria
1. When viewing an environment, a UI is presented to add a new key-value pair.
2. All existing key-value pairs for the selected environment are displayed in a clear list or table.
3. There are controls to edit the value of an existing key.
4. There is a control to delete a key-value pair, with a confirmation step.

## Tasks / Subtasks
- [x] Set up secrets database schema (AC: 1, 2, 3, 4)
  - [x] Create secrets table with encryption fields
  - [x] Implement RLS policies for secret access
  - [x] Set up encryption key management
  - [x] Create indexes for performance
- [x] Implement encryption layer (AC: 1, 2, 3)
  - [x] Create encryption utilities using AES-256
  - [x] Implement encrypt/decrypt functions
  - [x] Handle initialization vectors (IV)
  - [x] Secure key storage strategy
- [x] Build secrets UI components (AC: 1, 2)
  - [x] Create AddSecretForm component
  - [x] Build SecretsList table/grid
  - [x] Implement key-value input fields
  - [x] Add validation for keys and values
- [x] Implement CRUD operations (AC: 1, 3, 4)
  - [x] Create secret service/hooks
  - [x] Add secret creation with encryption
  - [x] Edit secret value (re-encrypt)
  - [x] Delete with confirmation dialog
- [x] Handle secret display (AC: 2, 3)
  - [x] Show/hide toggle for values
  - [x] Copy to clipboard functionality
  - [x] Mask values by default
  - [ ] Search/filter capabilities

## Dev Notes
### Dependencies on Previous Stories
This story depends on:
- Story 2.3: Environments exist and can be selected
- User has proper role (admin/manager) to modify secrets

### Database Schema
From technical-architecture.md:
```sql
-- Encrypted secrets table
CREATE TABLE secrets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  environment_id UUID NOT NULL REFERENCES environments(id) ON DELETE CASCADE,
  key TEXT NOT NULL,
  encrypted_value TEXT NOT NULL, -- The encrypted secret
  iv TEXT NOT NULL, -- Initialization Vector for AES
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  UNIQUE(environment_id, key)
);

-- Add trigger for updated_at
CREATE TRIGGER update_secrets_updated_at
  BEFORE UPDATE ON secrets
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();
```

### RLS Policies for Secrets
```sql
-- View secrets (members and above)
CREATE POLICY "View secrets" ON secrets
  FOR SELECT USING (
    environment_id IN (
      SELECT e.id FROM environments e
      JOIN projects p ON e.project_id = p.id
      JOIN team_members tm ON p.team_id = tm.team_id
      WHERE tm.user_id = auth.uid()
    )
  );

-- Modify secrets (managers and admins only)
CREATE POLICY "Modify secrets" ON secrets
  FOR ALL USING (
    environment_id IN (
      SELECT e.id FROM environments e
      JOIN projects p ON e.project_id = p.id
      JOIN team_members tm ON p.team_id = tm.team_id
      WHERE tm.user_id = auth.uid()
      AND tm.role IN ('admin', 'manager')
    )
  );
```

### Encryption Implementation
```typescript
// lib/crypto/encryption.ts
import { createCipheriv, createDecipheriv, randomBytes } from 'crypto'

const algorithm = 'aes-256-cbc'
const key = process.env.ENCRYPTION_KEY! // 32 bytes key

export function encrypt(text: string): { encrypted: string; iv: string } {
  const iv = randomBytes(16)
  const cipher = createCipheriv(algorithm, Buffer.from(key, 'hex'), iv)
  
  let encrypted = cipher.update(text)
  encrypted = Buffer.concat([encrypted, cipher.final()])
  
  return {
    encrypted: encrypted.toString('hex'),
    iv: iv.toString('hex')
  }
}

export function decrypt(encrypted: string, iv: string): string {
  const decipher = createDecipheriv(
    algorithm, 
    Buffer.from(key, 'hex'), 
    Buffer.from(iv, 'hex')
  )
  
  let decrypted = decipher.update(Buffer.from(encrypted, 'hex'))
  decrypted = Buffer.concat([decrypted, decipher.final()])
  
  return decrypted.toString()
}
```

### Secrets UI Layout
```
Environment: Production
┌─────────────────────────────────────────┐
│ [+ Add Secret]                          │
├─────────────────────────────────────────┤
│ Search: [_______________]               │
├─────────────────────────────────────────┤
│ KEY              VALUE         ACTIONS  │
├─────────────────────────────────────────┤
│ DATABASE_URL     ••••••• [👁] [📋] [✏️] [🗑] │
│ API_KEY          ••••••• [👁] [📋] [✏️] [🗑] │
│ JWT_SECRET       ••••••• [👁] [📋] [✏️] [🗑] │
└─────────────────────────────────────────┘
```

### Key Validation Rules
- Uppercase alphanumeric and underscores only
- Must start with letter
- 1-50 characters
- Common format: VARIABLE_NAME
- No spaces or special characters
- Warn if lowercase (auto-uppercase option)

### Value Handling
- No character limit on values
- Support multiline values (certificates, keys)
- Preserve exact formatting
- Handle special characters properly
- Clear indication when value is empty

### UI Components Design
**Add Secret Form**:
```typescript
// Modal or inline form
// Key input: Uppercase transformation
// Value input: Textarea for multiline
// Cancel and Save buttons
// Loading state during save
```

**Secret Row Actions**:
- 👁 Show/Hide: Toggle value visibility
- 📋 Copy: Copy value to clipboard
- ✏️ Edit: Inline edit or modal
- 🗑 Delete: Confirmation required

### Security Considerations
- Never log or display decrypted values in console
- Implement rate limiting for decryption operations
- Clear clipboard after timeout
- Mask values by default
- Audit log for secret operations (future)
- Never store decrypted values in state longer than needed

### Empty State
When no secrets in environment:
```
No secrets yet
Add your first secret to get started
[+ Add Secret]
```

### Error Handling
- `Duplicate key`: "A secret with this key already exists"
- `Invalid key format`: "Keys must be uppercase letters, numbers, and underscores"
- `Encryption error`: "Failed to save secret. Please try again"
- `Decryption error`: "Unable to retrieve secret value"

### Performance Optimization
- Decrypt values only when requested (lazy loading)
- Cache decrypted values briefly in memory
- Batch operations when possible
- Virtual scrolling for large secret lists

### Testing
**Testing Requirements:**
- Test secret CRUD operations
- Test encryption/decryption roundtrip
- Test key validation rules
- Test value masking/revealing
- Test copy to clipboard
- Test RLS policies (role-based access)
- Test duplicate key handling
- Test search/filter functionality
- E2E: Add secret → Edit → Delete flow

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-19 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record
### Agent Model Used
Claude 3.5 Sonnet

### Debug Log References
- Fixed infinite recursion in RLS policies 
- Resolved migration version conflicts
- Implemented client-server encryption architecture due to browser crypto limitations

### Completion Notes List
- Created AES-256-CBC encryption with separate IV storage
- Built client-side encryption API routes since crypto module not available in browser
- Implemented full CRUD operations with proper state management
- Added show/hide, copy, inline edit, and delete with confirmation
- Auto-uppercase secret keys with validation
- Search/filter functionality not implemented (marked as future enhancement)

### File List
- `/apps/web/supabase/migrations/20250121_001_create_secrets.sql` - Database schema and RLS policies
- `/apps/web/src/lib/crypto/encryption.ts` - Encryption utilities with client/server functions
- `/apps/web/src/app/api/crypto/encrypt/route.ts` - API route for client-side encryption
- `/apps/web/src/app/api/crypto/decrypt/route.ts` - API route for client-side decryption  
- `/apps/web/src/lib/secrets/types.ts` - TypeScript types for secrets
- `/apps/web/src/lib/secrets/api.ts` - API functions for secrets CRUD operations
- `/apps/web/src/lib/secrets/hooks.ts` - React hooks for secrets state management
- `/apps/web/src/components/molecules/AddSecretModal.tsx` - Modal for adding new secrets
- `/apps/web/src/components/molecules/SecretsList.tsx` - List component with all secret actions
- `/apps/web/src/app/(main)/projects/[id]/page.tsx` - Updated to integrate secrets into environment view

## QA Results
[To be populated by QA Agent]