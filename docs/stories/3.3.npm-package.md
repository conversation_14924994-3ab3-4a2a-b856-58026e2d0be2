# Story 3.3: <PERSON><PERSON><PERSON> and Publish NPM Package

## Status

Ready for Review

## Story

**As a** developer,
**I want** to install and use a simple npm package to load my project's environment variables into my application's runtime.

## Acceptance Criteria

1. A new npm package (e.g., easy-env-sdk) is created within the monorepo.
2. The package provides a simple initialization method where a developer can provide their API key.
3. The package has a primary function, like EasyEnv.get("project-name", "environment-name"), which calls the secure API endpoint.
4. The package correctly handles both successful responses (returning the variables) and error responses from the API.
5. The package includes a README.md with clear installation, configuration, and usage instructions.
6. The package is successfully published to the public NPM registry.

## Tasks / Subtasks

- [x] Set up package structure (AC: 1)
  - [x] Create packages/sdk directory in monorepo
  - [x] Initialize package.json with correct metadata
  - [x] Configure TypeScript for package
  - [x] Set up build process (tsup or rollup)
  - [x] Configure ESM and CommonJS outputs
- [x] Implement core SDK functionality (AC: 2, 3)
  - [x] Create main class/interface
  - [x] Implement initialization with API key
  - [x] Add get() method for fetching secrets
  - [x] Handle HTTP requests to API endpoint
  - [x] Add TypeScript type definitions
- [x] Add error handling (AC: 4)
  - [x] Handle network errors gracefully
  - [x] Parse and throw meaningful errors
  - [x] Add retry logic with backoff
  - [x] Provide clear error messages
  - [x] Handle timeout scenarios
- [x] Create comprehensive documentation (AC: 5)
  - [x] Write detailed README.md
  - [x] Add installation instructions
  - [x] Include usage examples
  - [x] Document error handling
  - [x] Add API reference
- [x] Prepare for publishing (AC: 6)
  - [x] Add .npmignore file
  - [x] Configure package metadata
  - [x] Add license file
  - [x] Set up automated publishing
  - [x] Test package locally first
- [x] Add developer experience features
  - [x] Auto-load from .env.ezenv file
  - [x] Cache responses appropriately
  - [x] Add debug mode
  - [x] Support for different environments

## Dev Notes

### Dependencies on Previous Stories

This story depends on:

- Story 3.2: API endpoint exists and is functional
- Story 3.1: API keys can be generated

### Package Structure

```
packages/sdk/
├── src/
│   ├── index.ts          # Main entry point
│   ├── client.ts         # API client logic
│   ├── errors.ts         # Custom error classes
│   ├── types.ts          # TypeScript definitions
│   └── utils.ts          # Helper functions
├── test/
│   └── ezenv.test.ts     # Unit tests
├── package.json
├── tsconfig.json
├── README.md
├── LICENSE
└── .npmignore
```

### Package.json Configuration

```json
{
  "name": "@ezenv/sdk",
  "version": "1.0.0",
  "description": "SDK for fetching environment variables from EzEnv",
  "main": "./dist/index.js",
  "module": "./dist/index.mjs",
  "types": "./dist/index.d.ts",
  "exports": {
    ".": {
      "require": "./dist/index.js",
      "import": "./dist/index.mjs",
      "types": "./dist/index.d.ts"
    }
  },
  "files": ["dist"],
  "scripts": {
    "build": "tsup",
    "test": "jest",
    "prepublishOnly": "npm run build"
  },
  "keywords": ["env", "environment", "variables", "config", "ezenv"],
  "author": "EzEnv Team",
  "license": "MIT",
  "repository": {
    "type": "git",
    "url": "https://github.com/ezenv/ezenv"
  },
  "dependencies": {
    "node-fetch": "^3.3.0"
  },
  "devDependencies": {
    "@types/node": "^18.0.0",
    "tsup": "^6.0.0",
    "typescript": "^5.0.0"
  }
}
```

### Core SDK Implementation

```typescript
// src/index.ts
export { EzEnv } from './client'
export type { EzEnvConfig, EzEnvOptions } from './types'

// src/client.ts
import fetch from 'node-fetch'
import { EzEnvConfig, EzEnvOptions } from './types'
import { EzEnvError, NetworkError, AuthError } from './errors'

export class EzEnv {
  private apiKey: string
  private baseUrl: string
  private cache: Map<string, any> = new Map()

  constructor(config: EzEnvConfig) {
    if (!config.apiKey) {
      throw new Error('API key is required')
    }

    this.apiKey = config.apiKey
    this.baseUrl = config.baseUrl || 'https://ezenv.dev'
  }

  async get(
    projectName: string,
    environmentName: string,
    options?: EzEnvOptions
  ): Promise<Record<string, string>> {
    const cacheKey = `${projectName}:${environmentName}`

    // Check cache
    if (!options?.noCache && this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    try {
      const response = await fetch(`${this.baseUrl}/functions/v1/get-secrets`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectName,
          environmentName,
        }),
        signal: AbortSignal.timeout(options?.timeout || 30000),
      })

      const data = await response.json()

      if (!response.ok) {
        if (response.status === 401) {
          throw new AuthError(data.error || 'Invalid API key')
        }
        if (response.status === 404) {
          throw new EzEnvError(
            `Project "${projectName}" or environment "${environmentName}" not found`
          )
        }
        throw new EzEnvError(data.error || 'Failed to fetch secrets')
      }

      // Cache successful response
      this.cache.set(cacheKey, data.secrets)

      return data.secrets
    } catch (error) {
      if (error instanceof EzEnvError) {
        throw error
      }
      if (error.name === 'AbortError') {
        throw new NetworkError('Request timeout')
      }
      throw new NetworkError('Failed to connect to EzEnv')
    }
  }

  // Convenience method to load directly into process.env
  async load(projectName: string, environmentName: string, options?: EzEnvOptions): Promise<void> {
    const secrets = await this.get(projectName, environmentName, options)

    Object.entries(secrets).forEach(([key, value]) => {
      if (!options?.override && process.env[key]) {
        return // Don't override existing values
      }
      process.env[key] = value
    })
  }
}

// Convenience factory function
export function createClient(apiKey: string): EzEnv {
  return new EzEnv({ apiKey })
}
```

### Error Classes

```typescript
// src/errors.ts
export class EzEnvError extends Error {
  constructor(message: string) {
    super(message)
    this.name = 'EzEnvError'
  }
}

export class NetworkError extends EzEnvError {
  constructor(message: string) {
    super(message)
    this.name = 'NetworkError'
  }
}

export class AuthError extends EzEnvError {
  constructor(message: string) {
    super(message)
    this.name = 'AuthError'
  }
}
```

### TypeScript Types

```typescript
// src/types.ts
export interface EzEnvConfig {
  apiKey: string
  baseUrl?: string
}

export interface EzEnvOptions {
  noCache?: boolean
  timeout?: number
  override?: boolean
}
```

### README.md Template

```markdown
# EzEnv SDK

Official SDK for fetching environment variables from EzEnv.

## Installation

\`\`\`bash
npm install @ezenv/sdk

# or

yarn add @ezenv/sdk
\`\`\`

## Quick Start

\`\`\`javascript
import { EzEnv } from '@ezenv/sdk'

// Initialize client
const ezenv = new EzEnv({
apiKey: 'ezenv_your_api_key_here'
})

// Fetch secrets
const secrets = await ezenv.get('my-project', 'production')
console.log(secrets.DATABASE_URL)

// Or load directly into process.env
await ezenv.load('my-project', 'production')
console.log(process.env.DATABASE_URL)
\`\`\`

## Configuration

### Auto-loading from file

Create a `.env.ezenv` file in your project root:

\`\`\`
EZENV_API_KEY=ezenv_your_api_key_here
EZENV_PROJECT=my-project
EZENV_ENVIRONMENT=development
\`\`\`

Then in your code:

\`\`\`javascript
import { loadFromFile } from '@ezenv/sdk'

await loadFromFile() // Reads .env.ezenv automatically
\`\`\`

## API Reference

### `new EzEnv(config)`

...

## Error Handling

The SDK throws specific error types:

- `AuthError`: Invalid API key
- `NetworkError`: Connection issues
- `EzEnvError`: General errors

\`\`\`javascript
try {
await ezenv.get('project', 'env')
} catch (error) {
if (error instanceof AuthError) {
console.error('Invalid API key')
}
}
\`\`\`

## License

MIT
```

### Auto-loading Feature

```typescript
// src/utils.ts
import { readFileSync } from 'fs'
import { join } from 'path'
import { EzEnv } from './client'

export async function loadFromFile(filePath?: string): Promise<void> {
  const path = filePath || join(process.cwd(), '.env.ezenv')

  try {
    const content = readFileSync(path, 'utf8')
    const config: any = {}

    content.split('\n').forEach((line) => {
      const [key, value] = line.split('=')
      if (key && value) {
        config[key.trim()] = value.trim()
      }
    })

    if (!config.EZENV_API_KEY) {
      throw new Error('EZENV_API_KEY not found in .env.ezenv')
    }

    const client = new EzEnv({ apiKey: config.EZENV_API_KEY })
    await client.load(config.EZENV_PROJECT || 'default', config.EZENV_ENVIRONMENT || 'development')
  } catch (error) {
    if (error.code === 'ENOENT') {
      throw new Error('.env.ezenv file not found')
    }
    throw error
  }
}
```

### Testing Requirements

**Unit Tests:**

- Test client initialization
- Test successful secret fetching
- Test error handling
- Test caching behavior
- Test timeout handling
- Test auto-loading feature

**Integration Tests:**

- Test against mock API endpoint
- Test various error scenarios
- Test retry logic

### Publishing Process

1. Run tests: `npm test`
2. Build package: `npm run build`
3. Test locally: `npm pack` and install in test project
4. Publish to NPM: `npm publish --access public`
5. Tag release in git

### Testing

**Testing Requirements:**

- Unit test all public methods
- Test error scenarios
- Test TypeScript types compile correctly
- Test both ESM and CommonJS outputs
- Test in Node.js environments
- Integration test with real API
- Test auto-loading feature
- Test caching behavior
- Test README examples work

## Change Log

| Date       | Version | Description            | Author             |
| ---------- | ------- | ---------------------- | ------------------ |
| 2025-07-19 | 1.0     | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record

### Agent Model Used

claude-opus-4-20250514

### Debug Log References

N/A

### Completion Notes List

- Created complete npm package structure with TypeScript support
- Implemented EzEnv client class with full API integration
- Added comprehensive error handling with custom error types
- Created utility functions for loading from file and environment variables
- Built with tsup for both ESM and CommonJS outputs
- Added extensive documentation with examples
- Implemented caching mechanism for better performance
- Created comprehensive test suite with 100% coverage goal
- Package is ready for publishing to npm registry

### File List

- Created: packages/sdk/package.json
- Created: packages/sdk/tsconfig.json
- Created: packages/sdk/tsup.config.ts
- Created: packages/sdk/jest.config.js
- Created: packages/sdk/README.md
- Created: packages/sdk/LICENSE
- Created: packages/sdk/.npmignore
- Created: packages/sdk/src/index.ts
- Created: packages/sdk/src/client.ts
- Created: packages/sdk/src/types.ts
- Created: packages/sdk/src/errors.ts
- Created: packages/sdk/src/utils.ts
- Created: packages/sdk/test/client.test.ts
- Created: packages/sdk/test/utils.test.ts
- Created: packages/sdk/test/errors.test.ts

## QA Results

[To be populated by QA Agent]
