# Story 1.4: Protected Application Shell

## Status
Ready for Review

## Story
**As a** developer,
**I want** to implement a protected routing system,
**so that** only authenticated users can access core application pages (like the future dashboard).

## Acceptance Criteria
1. A basic dashboard page is created (e.g., at the /dashboard route).
2. Unauthenticated users attempting to access /dashboard (or any other protected route) are automatically redirected to the login page.
3. An authenticated user can successfully access and view the /dashboard page.

## Tasks / Subtasks
- [x] Create protected route structure (AC: 1, 2, 3)
  - [x] Create `app/(main)/layout.tsx` for protected routes
  - [x] Create `app/(main)/dashboard/page.tsx` 
  - [x] Implement authentication check in layout
  - [x] Set up proper route groups
- [x] Implement middleware for route protection (AC: 2)
  - [x] Create `middleware.ts` at app root
  - [x] Configure protected route patterns
  - [x] Implement session validation
  - [x] Handle redirect logic for unauthenticated users
- [x] Create dashboard shell (AC: 1, 3)
  - [x] Design basic dashboard layout
  - [x] Add navigation header with user info
  - [x] Create sidebar for future navigation
  - [x] Apply Neobrutalist design system
- [x] Implement session validation (AC: 2, 3)
  - [x] Check session on protected route access
  - [x] Handle expired sessions gracefully
  - [x] Implement loading states during auth check
  - [x] Ensure smooth transition for authenticated users
- [x] Add navigation components
  - [x] Create AppHeader component with user menu
  - [x] Create AppSidebar for main navigation
  - [x] Add active route highlighting
  - [x] Ensure responsive design

## Dev Notes
### Dependencies on Previous Stories
This story depends on:
- Story 1.1: Next.js app structure and Supabase client
- Story 1.2: Component structure established
- Story 1.3: Authentication system and auth context

### Route Group Structure
From CLAUDE.md, the app should use route groups:
```
app/
├── (auth)/          # Public routes (login, signup)
│   ├── login/
│   └── signup/
├── (main)/          # Protected routes
│   ├── layout.tsx   # Auth check happens here
│   ├── dashboard/
│   ├── projects/    # Future stories
│   ├── team/        # Future stories
│   └── settings/    # Future stories
```

### Middleware Implementation
Next.js middleware for route protection:
```typescript
// middleware.ts
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()
  const supabase = createMiddlewareClient({ req, res })
  const { data: { session } } = await supabase.auth.getSession()

  // Protected routes check
  if (req.nextUrl.pathname.startsWith('/(main)')) {
    if (!session) {
      return NextResponse.redirect(new URL('/login', req.url))
    }
  }

  return res
}

export const config = {
  matcher: ['/(main)/:path*']
}
```

### Protected Layout Pattern
The (main)/layout.tsx should:
1. Verify authentication state
2. Fetch user data
3. Render the app shell (header, sidebar, content area)
4. Handle loading states during auth verification

### Dashboard Page Structure
Initial dashboard should include:
- Welcome message with user's email
- Placeholder sections for future features:
  - Recent projects (empty state)
  - Team activity (empty state)
  - Quick actions card
- Clear visual hierarchy using Neobrutalist design

### App Shell Components
**AppHeader** should include:
- Logo/brand on the left
- User menu dropdown on the right with:
  - User email display
  - Link to account settings (future)
  - Logout button (Story 1.5)

**AppSidebar** should include:
- Navigation links (with icons):
  - Dashboard (active)
  - Projects (disabled for now)
  - Team (disabled for now)
  - Settings (disabled for now)
- Collapsed/expanded states for future

### Design Requirements
From css-ref.md - Neobrutalist shell:
- Header: White background with thick bottom border `border-b-2 border-black`
- Sidebar: Light gray background `bg-gray-100` with right border
- Content area: White background with proper spacing
- All interactive elements with hard shadows on hover
- Consistent use of the accent color for active states

### Empty States Design
For the dashboard placeholders:
- Dashed border boxes: `border-2 border-dashed border-gray-400`
- Centered text explaining future features
- Consistent padding and spacing
- Icons to represent each section

### Loading States
During auth checking:
- Show full-page loading spinner
- Prevent layout shift when content loads
- Smooth fade-in transition when ready

### Testing
**Testing Requirements:**
- Test unauthenticated redirect to login
- Test authenticated access to dashboard
- Test session expiry handling
- Test middleware with various route patterns
- E2E test for login → dashboard flow
- Test loading states during auth check
- Verify proper layout rendering

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-19 | 1.0 | Initial story creation | Bob (Scrum Master) |
| 2025-07-20 | 1.1 | Completed implementation | James (Developer) |

## Dev Agent Record
### Agent Model Used
Claude 3.5 Sonnet (claude-3-5-sonnet-20241022)

### Debug Log References
[To be populated by Dev Agent]

### Completion Notes List
- Successfully created protected route structure with (main) route group
- Moved dashboard from public to protected routes
- Created AppHeader component with user dropdown menu
- Created AppSidebar component with navigation links
- Implemented loading state during authentication check
- Enhanced middleware with better route protection and redirect handling
- Added empty state cards for future features
- Implemented Neobrutalist design throughout the app shell
- Active route highlighting works correctly
- All future navigation items marked as "Soon" and disabled

### File List
**Created:**
- `/apps/web/src/app/(main)/layout.tsx` - Protected route layout with auth check
- `/apps/web/src/components/molecules/AppHeader.tsx` - Header with user menu
- `/apps/web/src/components/molecules/AppSidebar.tsx` - Sidebar navigation

**Modified:**
- `/apps/web/src/app/(main)/dashboard/page.tsx` - Enhanced dashboard with placeholders
- `/apps/web/src/middleware.ts` - Improved route protection logic
- `/apps/web/src/app/globals.css` - Added utility classes for app shell

**Moved:**
- Dashboard page moved from `/apps/web/src/app/dashboard/` to `/apps/web/src/app/(main)/dashboard/`

## QA Results
[To be populated by QA Agent]