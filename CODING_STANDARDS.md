# EzEnv Coding Standards & Conventions

## General Principles

1. **Clarity over Cleverness**: Write code that junior developers can understand
2. **Consistency**: Follow established patterns throughout the codebase
3. **Self-Documenting**: Code should explain itself through good naming
4. **YAGNI**: Don't add functionality until it's needed
5. **DRY**: Avoid duplication, but don't over-abstract

## TypeScript Standards

### Type Safety
```typescript
// ✅ Good - Explicit types
interface User {
  id: string;
  email: string;
  role: 'admin' | 'manager' | 'member';
}

// ❌ Bad - Using any
const processData = (data: any) => { ... }

// ✅ Good - Type narrowing
if (user.role === 'admin') {
  // TypeScript knows user.role is 'admin' here
}
```

### Naming Conventions
- **Interfaces**: PascalCase with descriptive names (e.g., `ProjectSettings`)
- **Types**: PascalCase for unions/aliases (e.g., `UserRole`)
- **Enums**: PascalCase with UPPER_SNAKE members
- **Functions**: camelCase verbs (e.g., `createProject`, `fetchSecrets`)
- **Variables**: camelCase nouns (e.g., `currentUser`, `projectList`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `MAX_RETRY_ATTEMPTS`)
- **Files**: kebab-case (e.g., `project-card.tsx`, `auth-utils.ts`)

### File Organization
```typescript
// 1. Imports (grouped and sorted)
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

import { Button } from '@/components/atoms';
import { ProjectCard } from '@/components/molecules';

import { api } from '@/lib/api';
import { formatDate } from '@/lib/utils';

import type { Project } from '@/types';

// 2. Types/Interfaces
interface ProjectListProps {
  teamId: string;
}

// 3. Constants
const ITEMS_PER_PAGE = 20;

// 4. Component/Function
export function ProjectList({ teamId }: ProjectListProps) {
  // Implementation
}

// 5. Helper functions (if needed)
function sortProjects(projects: Project[]) {
  // Implementation
}
```

## React/Next.js Standards

### Component Structure
```typescript
// ✅ Good - Clear component structure
export function ProjectCard({ project, onDelete }: ProjectCardProps) {
  // 1. Hooks
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);
  
  // 2. Derived state
  const canDelete = project.role === 'admin';
  
  // 3. Event handlers
  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await api.projects.delete(project.id);
      onDelete(project.id);
    } finally {
      setIsDeleting(false);
    }
  };
  
  // 4. Render
  return (
    <div className="p-4 border-2 border-black shadow-brutal">
      {/* Component content */}
    </div>
  );
}
```

### Server vs Client Components
```typescript
// ✅ Server Component (default)
// app/projects/page.tsx
export default async function ProjectsPage() {
  const projects = await getProjects(); // Server-side data fetching
  return <ProjectList projects={projects} />;
}

// ✅ Client Component (when needed)
// components/project-form.tsx
'use client';

export function ProjectForm() {
  const [name, setName] = useState(''); // Needs state
  // ...
}
```

### Hooks Usage
```typescript
// Custom hooks in @/hooks
export function useProjects(teamId: string) {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // Fetch logic
  }, [teamId]);
  
  return { projects, loading };
}
```

## CSS/Tailwind Standards

### Neobrutalist Classes
```typescript
// ✅ Good - Consistent brutal shadows
<div className="border-2 border-black shadow-brutal hover:shadow-brutal-lg">

// ✅ Good - Semantic color usage
<button className="bg-primary text-white border-2 border-black">

// ❌ Bad - Inline arbitrary values
<div style={{ boxShadow: '2px 2px 0 black' }}>
```

### Class Organization
```typescript
// Order: Layout → Spacing → Sizing → Typography → Colors → Borders → Effects
<div className="flex flex-col gap-4 p-6 w-full max-w-md text-lg font-mono bg-white border-2 border-black shadow-brutal">
```

## API Standards

### Endpoint Naming
```typescript
// RESTful conventions
GET    /api/projects          // List
GET    /api/projects/:id      // Get one
POST   /api/projects          // Create
PATCH  /api/projects/:id      // Update
DELETE /api/projects/:id      // Delete

// Nested resources
GET    /api/projects/:id/environments
POST   /api/projects/:id/environments
```

### Error Handling
```typescript
// ✅ Good - Consistent error format
try {
  const data = await api.projects.create(payload);
  return { data, error: null };
} catch (error) {
  return {
    data: null,
    error: {
      message: error.message || 'Failed to create project',
      code: error.code || 'UNKNOWN_ERROR'
    }
  };
}
```

## Database/Supabase Standards

### RLS Policies
```sql
-- Clear policy names
CREATE POLICY "Users can view their team's projects"
ON projects FOR SELECT
USING (team_id IN (
  SELECT team_id FROM team_members 
  WHERE user_id = auth.uid()
));
```

### Edge Functions
```typescript
// Consistent response format
export async function handler(req: Request) {
  try {
    // Validate input
    const { projectId } = await req.json();
    if (!projectId) {
      return new Response(
        JSON.stringify({ error: 'Project ID required' }),
        { status: 400 }
      );
    }
    
    // Process request
    const result = await processRequest(projectId);
    
    // Return success
    return new Response(
      JSON.stringify({ data: result }),
      { status: 200 }
    );
  } catch (error) {
    // Return error
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500 }
    );
  }
}
```

## Git Commit Standards

### Commit Message Format
```
type(scope): subject

body (optional)

footer (optional)
```

### Types
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc)
- `refactor`: Code refactoring
- `test`: Test additions or changes
- `chore`: Build process or auxiliary tool changes

### Examples
```bash
feat(auth): add magic link authentication

fix(projects): prevent duplicate project names within team

refactor(api): consolidate error handling logic
```

## Code Review Checklist

- [ ] TypeScript types are explicit and correct
- [ ] No `any` types without justification
- [ ] Components follow established patterns
- [ ] Proper error handling in place
- [ ] Security considerations addressed
- [ ] Performance implications considered
- [ ] Tests written for new functionality
- [ ] Documentation updated if needed