{"name": "ezenv", "version": "1.0.0", "private": true, "description": "SecureEnv Platform - Secure environment variable management for development teams", "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "pnpm --filter web dev", "build": "pnpm -r build", "lint": "pnpm -r lint", "typecheck": "pnpm -r typecheck", "test": "pnpm -r test", "format": "prettier --write \"**/*.{js,ts,tsx,json,md}\"", "format:check": "prettier --check \"**/*.{js,ts,tsx,json,md}\"", "clean": "rm -rf node_modules apps/*/node_modules packages/*/node_modules"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "prettier": "^3.2.0", "eslint": "^8.56.0"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}